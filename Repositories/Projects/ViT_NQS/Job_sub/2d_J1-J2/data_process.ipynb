{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/ntu/s240076/miniconda3/envs/netket/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import netket as nk\n", "# 设置2D晶格的参数\n", "L = 6 # 晶格长度，即晶格中包含的节点数\n", "N = L * L  # 晶格中的节点总数\n", "J2 = 0.55  # 下一个最近邻相互作用的耦合常数，表示相邻节点之间的相互作用强度\n", "\n", "# 创建2D晶格\n", "lattice = nk.graph.Grid(extent=[L, L], pbc=[True, True], max_neighbor_order=2)\n", "\n", "lattice.draw() \n", "\n", "# 创建<PERSON><PERSON>空间\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 创建哈密顿量\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J2 =  0.55\n", "The optimized ViT energy is E0 =  -0.4882123548761111\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import os\n", "\n", "# 假定 N 已经定义，例如:\n", "# N = 16\n", "\n", "from pathlib import Path\n", "\n", "current_dir = Path(os.getcwd())\n", "local_dir = current_dir / 'results'\n", "\n", "\n", "log_file = os.path.join(local_dir, f\"L={L}\", f\"J2={J2:.3f}\", f\"L={L}_J2={J2:.3f}.log\")\n", "with open(log_file, 'r') as f:\n", "    data_ViT = json.load(f)\n", "\n", "min_iter = 1400\n", "# max_iter = len(data_ViT['Energy']['iters']) \n", "max_iter = 10000\n", "iters_ViT = data_ViT['Energy']['iters'][min_iter:max_iter]\n", "energy_ViT = [e / (4*N) for e in data_ViT['Energy']['Mean']['real'][min_iter:max_iter]]\n", "\n", "print('J2 = ', J2)\n", "print('The optimized ViT energy is E0 = ', np.real(energy_ViT[-1000]))\n", "\n", "\n", "if L <= 4:\n", "    # 计算基态能量\n", "    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)  # 使用Lanczos算法计算哈密顿量的本征值\n", "    exact_gs_energy = evals[0] / (4*N)  # 基态能量除以四\n", "    print('Exact ground-state energy E0 = ', exact_gs_energy)\n", "\n", "    # 计算相对误差的绝对值\n", "    relative_errors = [abs((e - exact_gs_energy) / exact_gs_energy) for e in energy_ViT]\n", "\n", "    # 打印最终相对误差\n", "    final_relative_error = relative_errors[-1]\n", "    print('Final relative error = ', final_relative_error)\n", "\n", "# 绘制能量迭代图（上下限自动适应）\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_ViT, energy_ViT, color='C8', label='ViT')\n", "ax1.set_ylabel('E/N')\n", "ax1.set_xlabel('Iteration')\n", "ax1.set_title(f'Energy iteration (L={N})')\n", "\n", "if 'exact_gs_energy' in globals():\n", "    # 绘制参考的基态能量横线\n", "    ax1.axhline(y=exact_gs_energy, color='k', linewidth=2, linestyle='--', label='Exact')\n", "\n", "ax1.legend()\n", "ax1.relim()\n", "ax1.autoscale_view()  # 自动调整上下限\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}