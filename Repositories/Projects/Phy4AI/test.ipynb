{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["特征值： [-0.81  0.5   0.31  0.  ]\n", "特征向量： [[ 6.01500955e-01  7.07106781e-01  3.71748034e-01  1.61847686e-17]\n", " [ 3.71748034e-01  7.75387361e-17 -6.01500955e-01 -7.07106781e-01]\n", " [ 6.01500955e-01 -7.07106781e-01  3.71748034e-01  7.85622834e-17]\n", " [ 3.71748034e-01 -1.46728475e-16 -6.01500955e-01  7.07106781e-01]]\n", "特征值排序： [-0.81  0.    0.31  0.5 ]\n", "能量： -0.75\n"]}], "source": ["import numpy as np\n", "\n", "W = -0.5 * np.array([\n", "    [0, 0.5, 1, 0.5],\n", "    [0.5, 0, 0.5, 0],\n", "    [1, 0.5, 0, 0.5],\n", "    [0.5, 0, 0.5, 0]\n", "])\n", "\n", "s = np.array([-1, -1, -1, -1])\n", "s_normalized = s / np.linalg.norm(s)\n", "#特征值保留两位小数\n", "eigenvalues, eigenvectors = np.linalg.eig(W)\n", "eigenvalues = np.round(eigenvalues, 2) #特征值保留两位小数\n", "print(\"特征值：\", eigenvalues)\n", "print(\"特征向量：\", eigenvectors)\n", "\n", "#特征值排序\n", "eigenvalues_sorted = np.sort(eigenvalues)\n", "print(\"特征值排序：\", eigenvalues_sorted)\n", "\n", "#能量\n", "energy = s_normalized.T @ W @ s_normalized\n", "print(\"能量：\", energy)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["s = (-1, -1, -1, -1), energy = -0.75\n", "s = (1, 1, 1, 1), energy = -0.75\n", "s = (-1, -1, -1, 1), energy = -0.25\n", "s = (-1, 1, -1, -1), energy = -0.25\n", "s = (1, -1, 1, 1), energy = -0.25\n", "s = (1, 1, 1, -1), energy = -0.25\n", "s = (-1, -1, 1, -1), energy = 0.25\n", "s = (-1, -1, 1, 1), energy = 0.25\n", "s = (-1, 1, -1, 1), energy = 0.25\n", "s = (-1, 1, 1, -1), energy = 0.25\n", "s = (-1, 1, 1, 1), energy = 0.25\n", "s = (1, -1, -1, -1), energy = 0.25\n", "s = (1, -1, -1, 1), energy = 0.25\n", "s = (1, -1, 1, -1), energy = 0.25\n", "s = (1, 1, -1, -1), energy = 0.25\n", "s = (1, 1, -1, 1), energy = 0.25\n"]}], "source": ["import itertools\n", "\n", "# 枚举所有s构型（每个s_i为-1或1）\n", "all_s = list(itertools.product([-1, 1], repeat=4))\n", "\n", "energies = []\n", "for s_conf in all_s:\n", "    s_vec = np.array(s_conf)\n", "    s_norm = s_vec / np.linalg.norm(s_vec)\n", "    energy = s_norm.T @ W @ s_norm\n", "    energies.append((s_conf, energy))\n", "\n", "# 按能量从小到大排序\n", "energies_sorted = sorted(energies, key=lambda x: x[1])\n", "\n", "# 打印排序后的s构型和能量\n", "for conf, en in energies_sorted:\n", "    print(f\"s = {conf}, energy = {en}\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}