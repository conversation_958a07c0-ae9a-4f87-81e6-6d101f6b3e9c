#!/usr/bin/env python3
"""
完整的测试脚本：比较SDRG方法和RG神经网络
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import matplotlib.pyplot as plt
from src.spin_chain_model import SpinChain
from src.rg_neural_network import RGSpinNeuralNetwork
from src.sdrg import SDRG

def ensure_dir(directory):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def run_comparative_test(n_spins=12, seed=42, rg_nn_iterations=800):
    """运行对比测试"""
    print("=" * 70)
    print("                SDRG vs RG Neural Network Comparison")
    print("=" * 70)
    
    print(f"\nTest parameters:")
    print(f"  Number of spins: {n_spins}")
    print(f"  Random seed: {seed}")
    print(f"  RG NN iterations: {rg_nn_iterations}")
    
    # 创建自旋链
    print(f"\nCreating spin chain with {n_spins} spins (strong disorder)...")
    spin_chain = SpinChain(n_spins, seed=seed, disorder_type='strong')
    
    print(f"  Initial energy: {spin_chain.calculate_energy():.6f}")
    print(f"  Spin configuration: {spin_chain.spins}")
    print(f"  Interaction strength range: [{np.min(spin_chain.interactions):.3f}, {np.max(spin_chain.interactions):.3f}]")
    
    # 确保输出目录存在
    ensure_dir('figures')
    ensure_dir('figures/nn')
    ensure_dir('figures/exact')
    ensure_dir('figures/rg')
    
    # Save initial chain visualization
    print("\nSaving initial chain visualization...")
    spin_chain.plot_chain(title=f"Initial Spin Chain, Energy = {spin_chain.calculate_energy():.4f}")
    plt.savefig('figures/initial_chain.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("Initial chain plot saved to: figures/initial_chain.png")
    
    results = {}
    times = {}
    
    # 1. Exact Diagonalization (if system is small enough)
    print("\n" + "-" * 50)
    print("1. Running Exact Diagonalization...")
    print("-" * 50)
    
    start_time = time.time()
    try:
        if n_spins <= 20:  # Only for small systems
            exact_ground_state, exact_ground_energy = spin_chain.exact_diagonalization()
            
            if exact_ground_state is not None:
                results['exact'] = exact_ground_energy
                times['exact'] = time.time() - start_time
                
                print(f"  Exact diagonalization completed")
                print(f"  Ground state energy: {exact_ground_energy:.6f}")
                print(f"  Ground state spins: {exact_ground_state}")
                print(f"  Computation time: {times['exact']:.2f} seconds")
                
                # Save exact ground state visualization
                print(f"  Saving exact ground state visualization...")
                original_spins = spin_chain.spins.copy()
                spin_chain.spins = exact_ground_state
                spin_chain.plot_chain(title=f"Exact Ground State, Energy = {exact_ground_energy:.4f}")
                plt.savefig('figures/exact/ground_state.png', dpi=150, bbox_inches='tight')
                plt.close()
                print("    Exact ground state plot saved to: figures/exact/ground_state.png")
                
                # Restore original spins
                spin_chain.spins = original_spins
            else:
                print("  Exact diagonalization failed")
                results['exact'] = None
                times['exact'] = 0
        else:
            print(f"  Skipping exact diagonalization for large system ({n_spins} > 20 spins)")
            results['exact'] = None
            times['exact'] = 0
            
    except Exception as e:
        print(f"  Exact diagonalization failed: {e}")
        results['exact'] = None
        times['exact'] = 0
    
    # 2. SDRG方法
    print("\n" + "-" * 50)
    print("2. Running SDRG method...")
    print("-" * 50)
    
    start_time = time.time()
    try:
        sdrg = SDRG(spin_chain)
        ground_state, sdrg_energies = sdrg.run_until_ground_state(max_steps=20)
        
        # 计算最终能量
        original_spins = spin_chain.spins.copy()
        spin_chain.spins = ground_state
        final_energy = spin_chain.calculate_energy()
        
        results['sdrg'] = final_energy
        times['sdrg'] = time.time() - start_time
        
        print(f"  SDRG completed: {len(sdrg.rg_steps)} steps")
        print(f"  Algorithm energy: {sdrg.energy:.6f}")
        print(f"  Reconstructed energy: {final_energy:.6f}")
        print(f"  Computation time: {times['sdrg']:.2f} seconds")
        
        # Save SDRG process visualization
        print(f"  Saving SDRG process visualization...")
        sdrg.plot_rg_process()
        plt.savefig('figures/rg/sdrg_process.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("    SDRG process plot saved to: figures/rg/sdrg_process.png")
        
        # Save SDRG ground state visualization
        print(f"  Saving SDRG ground state visualization...")
        spin_chain.plot_chain(title=f"SDRG Ground State, Energy = {final_energy:.4f}")
        plt.savefig('figures/rg/sdrg_ground_state.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("    SDRG ground state plot saved to: figures/rg/sdrg_ground_state.png")
        
        # 恢复原始状态
        spin_chain.spins = original_spins
        
    except Exception as e:
        print(f"  SDRG failed: {e}")
        results['sdrg'] = None
        times['sdrg'] = 0
    
    # 3. RG神经网络
    print("\n" + "-" * 50)
    print("3. Running RG Neural Network (Input Layer Energy Optimization)...")
    print("-" * 50)
    
    start_time = time.time()
    try:
        rg_nn = RGSpinNeuralNetwork(spin_chain, learning_rate=0.1, seed=seed)
        rg_nn.train(n_iterations=rg_nn_iterations, save_interval=50)
        
        # Get final state
        final_state = rg_nn.get_final_state()
        if final_state:
            rg_nn_energy = final_state['input_energy']  # Input layer energy
            input_spins = final_state['input_spins']
            print(f"RG Neural Network completed!")
            print(f"Original input energy: {spin_chain.calculate_energy():.6f}")
            print(f"Optimized input energy: {rg_nn_energy:.6f}")
            print(f"Energy improvement: {spin_chain.calculate_energy() - rg_nn_energy:.6f}")
            print(f"Optimized input spins: {input_spins}")
        else:
            rg_nn_energy = float('inf')
            print("RG Neural Network failed to converge")
        
        # Store results
        results['rg_nn'] = rg_nn_energy
        times['rg_nn'] = time.time() - start_time
        
        # 保存可视化图
        print("\n  Generating visualizations...")
        
        # Save optimized input layer visualization
        print("    Saving optimized input layer state...")
        original_spins = spin_chain.spins.copy()
        # Convert from {-1,1} back to {0,1} for visualization
        optimized_spins_01 = ((input_spins + 1) / 2).astype(int)
        spin_chain.spins = optimized_spins_01
        spin_chain.plot_chain(title=f"RG NN Optimized Input Layer, Energy = {rg_nn_energy:.4f}")
        plt.savefig('figures/nn/rg_nn_optimized_input.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("    Optimized input layer plot saved to: figures/nn/rg_nn_optimized_input.png")
        
        # Restore original spins
        spin_chain.spins = original_spins
        
        # 网络结构图
        fig = rg_nn.plot_rg_structure()
        if fig:
            plt.savefig('figures/nn/rg_nn_structure.png', dpi=150, bbox_inches='tight')
            plt.close(fig)
            print("    Network structure plot saved to: figures/nn/rg_nn_structure.png")
        
        # 训练演变图
        fig = rg_nn.plot_training_evolution()
        if fig:
            plt.savefig('figures/nn/rg_nn_evolution.png', dpi=150, bbox_inches='tight')
            plt.close(fig)
            print("    Training evolution plot saved to: figures/nn/rg_nn_evolution.png")
        
        # 能量收敛图
        fig = rg_nn.plot_energy_convergence()
        if fig:
            plt.savefig('figures/nn/rg_nn_convergence.png', dpi=150, bbox_inches='tight')
            plt.close(fig)
            print("    Energy convergence plot saved to: figures/nn/rg_nn_convergence.png")
        
    except Exception as e:
        print(f"  RG neural network failed: {e}")
        import traceback
        traceback.print_exc()
        results['rg_nn'] = None
        times['rg_nn'] = 0
    
    print(f"\n{'=' * 60}")
    print("COMPREHENSIVE RESULTS SUMMARY")
    print(f"{'=' * 60}")
    print(f"System: {n_spins} spins, strong disorder")
    print(f"Original spin chain energy: {spin_chain.calculate_energy():.6f}")
    print()
    
    # Create nn table
    methods = []
    energies = []
    computation_times = []
    
    if 'exact' in results and results['exact'] is not None:
        methods.append("Exact Diagonalization")
        energies.append(results['exact'])
        computation_times.append(times['exact'])
    
    if 'sdrg' in results and results['sdrg'] is not None:
        methods.append("SDRG")
        energies.append(results['sdrg'])
        computation_times.append(times['sdrg'])
    
    if 'rg_nn' in results and results['rg_nn'] is not None:
        methods.append("RG Neural Network (Input Layer)")
        energies.append(results['rg_nn'])
        computation_times.append(times['rg_nn'])
    
    # Print table
    print(f"{'Method':<35} {'Energy':<15} {'Time (s)':<10} {'Performance':<12}")
    print("-" * 75)
    
    for i, (method, energy, comp_time) in enumerate(zip(methods, energies, computation_times)):
        # Performance rating
        if energy == min(energies):
            performance = "⭐⭐⭐⭐⭐"
        elif energy == sorted(energies)[1] if len(energies) > 1 else energy:
            performance = "⭐⭐⭐⭐"
        else:
            performance = "⭐⭐⭐"
        
        print(f"{method:<35} {energy:<15.6f} {comp_time:<10.2f} {performance:<12}")
    
    # Highlight best method
    if energies:
        best_idx = energies.index(min(energies))
        print(f"\n🎯 Best method: {methods[best_idx]} with energy {energies[best_idx]:.6f}")
        
        # Compare with exact energy if available
        if 'exact' in results and results['exact'] is not None:
            exact_energy = results['exact']
            print(f"\n📊 Comparison with Exact Ground State (E_exact = {exact_energy:.6f}):")
            
            for method, energy in zip(methods, energies):
                if method != "Exact Diagonalization":
                    error = abs(energy - exact_energy)
                    relative_error = error / abs(exact_energy) * 100 if exact_energy != 0 else float('inf')
                    print(f"  {method}: ΔE = {error:.6f}, relative error = {relative_error:.2f}%")
        
        # Compare RG NN vs SDRG
        if 'rg_nn' in results and 'sdrg' in results and results['rg_nn'] is not None and results['sdrg'] is not None:
            improvement = results['sdrg'] - results['rg_nn']
            if improvement > 0:
                print(f"\n🚀 RG Neural Network achieved {improvement:.6f} lower energy than SDRG!")
            elif improvement < 0:
                print(f"\n📈 SDRG achieved {abs(improvement):.6f} lower energy than RG Neural Network!")
            else:
                print(f"\n🤝 Both methods achieved the same energy!")
    
    print(f"\n{'=' * 60}")
    
    print(f"\n" + "=" * 70)
    print("                     Test Completed!")
    print("=" * 70)
    
    return results



def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Compare SDRG and RG Neural Network methods')
    parser.add_argument('--n_spins', type=int, default=12, help='Number of spins (default: 12)')
    parser.add_argument('--rg_nn_iterations', type=int, default=800, help='RG NN training iterations (default: 800)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed (default: 42)')
    
    args = parser.parse_args()
    
    try:
        results = run_comparative_test(
            n_spins=args.n_spins,
            seed=args.seed,
            rg_nn_iterations=args.rg_nn_iterations
        )
        print("\nComparison test completed successfully!")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 