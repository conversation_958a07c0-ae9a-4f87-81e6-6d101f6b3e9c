{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Hopfield Neural Network Implementation\n", "\n", "## 目标\n", "- 创建一个Hopfield网络来记忆4个模式\n", "- 从损坏的输入模式恢复完整模式\n", "- 详细可视化网络的工作过程\n", "\n", "## 记忆的模式\n", "1. <PERSON><PERSON> 1: (1, -1, 1, 1)\n", "2. <PERSON><PERSON> 2: (1, 1, 1, -1)\n", "3. <PERSON><PERSON> 3: (1, 1, 1, 1)\n", "4. <PERSON><PERSON> 4: (-1, -1, -1, -1)\n", "\n", "## 测试输入\n", "- 损坏的输入: (1, -1, -1, 1)\n", "- 目标: 恢复到最相似的记忆模式\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Required libraries imported successfully!\n", "NumPy version: 2.0.2\n", "Matplotlib version: 3.10.0\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib.patches import Rectangle\n", "import matplotlib.patches as mpatches\n", "\n", "# 设置中文字体和绘图样式\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Required libraries imported successfully!\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"Matplotlib version: {plt.matplotlib.__version__}\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HopfieldNetwork class defined successfully!\n"]}], "source": ["class HopfieldNetwork:\n", "    \"\"\"\n", "    Hopfield神经网络实现\n", "    \"\"\"\n", "    \n", "    def __init__(self, n_neurons):\n", "        \"\"\"\n", "        初始化Hopfield网络\n", "        \n", "        Parameters:\n", "        n_neurons (int): 神经元数量\n", "        \"\"\"\n", "        self.n_neurons = n_neurons\n", "        self.weights = np.zeros((n_neurons, n_neurons))\n", "        self.patterns = []\n", "        \n", "    def train(self, patterns):\n", "        \"\"\"\n", "        使用<PERSON><PERSON>ian学习规则训练网络\n", "        \n", "        Parameters:\n", "        patterns (list): 要记忆的模式列表\n", "        \"\"\"\n", "        self.patterns = patterns\n", "        n_patterns = len(patterns)\n", "        \n", "        # 重置权重矩阵\n", "        self.weights = np.zeros((self.n_neurons, self.n_neurons))\n", "        \n", "        # 对每个模式应用<PERSON><PERSON><PERSON>学习规则\n", "        for pattern in patterns:\n", "            pattern = np.array(pattern)\n", "            # 计算外积并累加到权重矩阵\n", "            self.weights += np.outer(pattern, pattern)\n", "        \n", "        # 平均化权重\n", "        self.weights /= n_patterns\n", "        \n", "        # 设置对角线为0（神经元不与自己连接）\n", "        np.fill_diagonal(self.weights, 0)\n", "        \n", "        print(f\"Network trained with {n_patterns} patterns\")\n", "        print(f\"Weight matrix shape: {self.weights.shape}\")\n", "        \n", "    def energy(self, state):\n", "        \"\"\"\n", "        计算网络的能量函数\n", "        \n", "        Parameters:\n", "        state (array): 当前状态\n", "        \n", "        Returns:\n", "        float: 能量值\n", "        \"\"\"\n", "        state = np.array(state)\n", "        return -0.5 * np.dot(state, np.dot(self.weights, state))\n", "    \n", "    def step(self, state):\n", "        \"\"\"\n", "        执行一步同步更新\n", "        \n", "        Parameters:\n", "        state (array): 当前状态\n", "        \n", "        Returns:\n", "        array: 更新后的状态\n", "        \"\"\"\n", "        state = np.array(state, dtype=float)\n", "        new_state = np.zeros_like(state)\n", "        \n", "        for i in range(self.n_neurons):\n", "            # 计算神经元i的净输入\n", "            net_input = np.dot(self.weights[i], state)\n", "            # 应用符号函数作为激活函数\n", "            new_state[i] = 1 if net_input >= 0 else -1\n", "            \n", "        return new_state\n", "    \n", "    def async_step(self, state, neuron_idx):\n", "        \"\"\"\n", "        执行异步更新（单个神经元）\n", "        \n", "        Parameters:\n", "        state (array): 当前状态\n", "        neuron_idx (int): 要更新的神经元索引\n", "        \n", "        Returns:\n", "        array: 更新后的状态\n", "        \"\"\"\n", "        state = np.array(state, dtype=float, copy=True)\n", "        \n", "        # 计算指定神经元的净输入\n", "        net_input = np.dot(self.weights[neuron_idx], state)\n", "        # 更新该神经元的状态\n", "        state[neuron_idx] = 1 if net_input >= 0 else -1\n", "        \n", "        return state\n", "    \n", "    def recall(self, input_pattern, max_iterations=100, async_update=True):\n", "        \"\"\"\n", "        从输入模式恢复记忆\n", "        \n", "        Parameters:\n", "        input_pattern (array): 输入模式\n", "        max_iterations (int): 最大迭代次数\n", "        async_update (bool): 是否使用异步更新\n", "        \n", "        Returns:\n", "        tuple: (最终状态, 状态历史, 能量历史)\n", "        \"\"\"\n", "        current_state = np.array(input_pattern, dtype=float, copy=True)\n", "        state_history = [current_state.copy()]\n", "        energy_history = [self.energy(current_state)]\n", "        \n", "        for iteration in range(max_iterations):\n", "            if async_update:\n", "                # 异步更新：随机选择神经元\n", "                neuron_idx = np.random.randint(0, self.n_neurons)\n", "                new_state = self.async_step(current_state, neuron_idx)\n", "            else:\n", "                # 同步更新：所有神经元同时更新\n", "                new_state = self.step(current_state)\n", "            \n", "            new_energy = self.energy(new_state)\n", "            \n", "            state_history.append(new_state.copy())\n", "            energy_history.append(new_energy)\n", "            \n", "            # 检查是否收敛\n", "            if np.array_equal(current_state, new_state):\n", "                print(f\"Converged after {iteration + 1} iterations\")\n", "                break\n", "                \n", "            current_state = new_state\n", "        else:\n", "            print(f\"Reached maximum iterations ({max_iterations})\")\n", "        \n", "        return current_state, state_history, energy_history\n", "\n", "# 测试类定义\n", "print(\"HopfieldNetwork class defined successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["定义的模式:\n", "Pattern 1: [1, -1, 1, 1]\n", "Pattern 2: [1, 1, 1, -1]\n", "Pattern 3: [1, 1, 1, 1]\n", "Pattern 4: [-1, -1, -1, -1]\n", "\n", "损坏的输入: [1, -1, -1, 1]\n", "Network trained with 4 patterns\n", "Weight matrix shape: (4, 4)\n", "\n", "权重矩阵:\n", "[[0.  0.5 1.  0.5]\n", " [0.5 0.  0.5 0. ]\n", " [1.  0.5 0.  0.5]\n", " [0.5 0.  0.5 0. ]]\n", "\n", "权重矩阵对称性检查: True\n"]}], "source": ["# 定义要记忆的模式和测试输入\n", "patterns = [\n", "    [1, -1, 1, 1],    # <PERSON><PERSON> 1\n", "    [1, 1, 1, -1],    # <PERSON><PERSON> 2  \n", "    [1, 1, 1, 1],     # <PERSON><PERSON> 3\n", "    [-1, -1, -1, -1]  # Pat<PERSON> 4\n", "]\n", "\n", "# 损坏的输入模式\n", "corrupted_input = [1, -1, -1, 1]\n", "#虚假记忆\n", "# corrupted_input = [-1, -1, -1, 1]\n", "# corrupted_input = [-1, 1, -1, -1]\n", "\n", "print(\"定义的模式:\")\n", "for i, pattern in enumerate(patterns):\n", "    print(f\"Pattern {i+1}: {pattern}\")\n", "\n", "print(f\"\\n损坏的输入: {corrupted_input}\")\n", "\n", "# 创建并训练Hopfield网络\n", "n_neurons = 4  # 4个神经元对应4个位置\n", "hopfield = HopfieldNetwork(n_neurons)\n", "hopfield.train(patterns)\n", "\n", "print(f\"\\n权重矩阵:\")\n", "print(hopfield.weights)\n", "print(f\"\\n权重矩阵对称性检查: {np.allclose(hopfield.weights, hopfield.weights.T)}\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x300 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArwAAAJOCAYAAABV4NRRAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAeu9JREFUeJzt3XlcVNX/x/HXgOwooOS+a4IIJG5ohvtaWWSumVZalvuSua+JYrlklpamZZmp39LMzDL3NMsyN1S0THI3FVFBWRTm9wcxPxFUBhlmHN/Px2Mewb1nzpw7w7XPvOfcMwaj0WhERERERMROOVh7ACIiIiIilqSCV0RERETsmgpeEREREbFrKnhFRERExK6p4BURERERu6aCV0RERETsmgpeEREREbFrKnhFRERExK6p4BURERERu6aCV+xWkyZN8PPz45133smy77333sPPz48GDRpYdAzz5s0jLCyMoKAg+vfvz8mTJ/Hz88PPz4+TJ0/muJ8VK1bg5+dHkyZN7thux44dpv5v5+YxtGjRguvXr2fan/HcDB8+PMfjszXDhw/Pk2M4c+aM6bnasGFDpn1PPfWUad+5c+dM2+Pi4kzb16xZk6PHyenrm52uXbvi5+fHe++9Z/Z97zQWPz8/goODSUxMzLT/9OnTpv1+fn7s2LEjTx43O7k9X0REbqWCV8RCTpw4wfTp0zl37hz169enRo0aeHp60q1bN7p164anp6e1h8ixY8f43//+lyd9JScnU7Nmzfu6UL5ViRIlKF26NAD79u0zbU9ISOCvv/4y/b57927Tz1FRUaafa9WqlaPHqVy5Mt26daNt27b3OuQ7eumll8wqqpOTk/n5558zbdu8efM9jWHVqlU5LpRt7XwRkftXAWsPQMRenTlzxvTz7NmzcXR0BGDUqFHWGlImjo6OpKamMnv2bMLDw/Hw8Lin/jZs2EBCQkIejS53UlNTTc9zXqlTpw4nT55k7969pm1RUVGkpaXh7u7OtWvX2L17Ny1btgT+vzAuX748RYsWzdFjBAcHExwcnKfjvtW5c+fYsWMHxYsXz1H7okWLcu7cOTZu3EizZs1M2zdv3oyTkxOenp7ExcWZPY5vv/02R+1SU1Px9va2mfNFRO5vSnhFbrJu3To6d+5MSEgIwcHBtGnThk8++YS0tDRTm/bt2+Pn58fHH3/MvHnzaNSoEUFBQXTq1IlDhw4B6dMCunbtarpPQEAAXbt2ve1HtFu3bqVz585Ur16dkJAQXnnlFf7+++87jjUhIYERI0ZQu3ZtatSowcCBA7l06VKOj7VgwYKEhoYSGxvLJ598ctfHioiIoGHDhgQGBtKsWTM++ugjjEYjkP6x+qBBgwD4+uuv8fPzY9OmTfj5+dGmTRtTP7/88ovp+BcsWGDaPnLkSPz8/Hj//fcBuHbtGtOmTaN58+YEBgZSu3ZtXn75ZXbt2mW6z83P5ZYtWwgPDycwMPC2xzB48GD8/Pzo2rUrKSkpwJ2nvWSoXbs2kF7kZhxvRqLbokWLTL8D7N+/H4DQ0FDTtru9vtlNach4fWvVqkVISAgDBgxg586dt52y4ujoyJdffknTpk0JCQmhW7duxMTEAOl/j2FhYaSmpnLq1KkcTYEoX748JUuWZPPmzaa//6SkJHbs2EFgYCDu7u5Z7vPzzz/z/PPPU6dOHWrXrk3Xrl35448/gP9/vX766ScAunXrZjrejCkoQ4YMYdasWdSsWZM5c+ZkOV+OHDnCI488gp+fHxs3bjQ97vjx4/Hz8+PJJ58kOTn5jsclIg8mFbwi/1m0aBF9+/Zl9+7dhIaG0rx5c44dO8aUKVMYM2aMqZ2LiwsAS5cuZfny5dSrV4+CBQuye/duXnvtNa5fv84jjzxiSvwg/X/uN/9+s02bNtGzZ0/27t1L48aNefTRR9m6dStdu3bl4sWLtx3vxIkTWbFiBYmJiTRu3JiLFy8SERGR4+NNSEhgyJAhACxYsIDY2Nhs26WmpvLyyy+zaNEi3N3dCQ8PJy0tjWnTpjF79mwAWrZsSaVKlQCoVKkS3bp1o0SJEjg6OnLkyBGuXr0KwK+//gqAk5NTpo+0M9LTunXrkpKSQvfu3fnoo4+4evUqbdq0oVKlSqbnJLuPwidNmkThwoUJDw/P9hhmzZrFd999R+XKlZk9ezbOzs45fp4yCt6EhASOHj0KwJ49ewAIDw+nQIECHDhwwFRoZUxpyLhfbl/fCRMmZHl97zRdZOfOncyfP586derg7OzMjh07GDhwIEajkUceeYT69esD4OHhQbdu3XjkkUfueux169YlNjbWlFr/8ssvJCUlZSrmM+zatYuePXuyc+dO6tevT82aNfntt994+eWXOXXqlGl6QoaWLVtmmcKxb98+li1bRsuWLXn44YezPEblypV54403TM9PQkICu3btYunSpTg7OzNt2jTT+SkicjNNaRC7t337dq5du5Zp280fT0N6MTNjxgwABgwYQK9evQD4/vvvGThwIF999RXdu3enUqVKGAwGID2F/OGHH/D09OTPP/+kTZs2nDlzhnXr1vH444/j4uLC2rVrgf+fxpDdhTfvvvsuaWlpDBgwgN69ewPpidWSJUv44osv6Nu3b5b7nD9/ntWrVwPp6djzzz8PQO/evbNcXHU7N27cIDg4mJYtW7J27Vpmz57N2LFjs7TbtGkTu3fvplChQnz55Zd4enry77//0rhxYxYsWECPHj14/vnn2b9/P3///TfBwcGm4w0ICCAqKooDBw5Qp04dtm/fTuHChalatSo7d+7kxo0bJCUlcfToUdzc3AgODmbVqlXs3r0bJycnli1bRpkyZTAajbz22mts3ryZ6dOnZ5l3XKVKFVM6fKvVq1cze/ZsHnroIT766CMKFSpk2rdw4UKuX7+Oj4/PbZ+nMmXKUKJECc6cOcPevXupVKkSe/fuxcnJiRo1alClShUOHjxIVFQUJUuW5MKFC0D6VAjI/eubccHb0KFDeeGFFwB49dVXOXHiRLbj/Ouvv/j+++/x9PRk27Zt9OjRg0OHDnHy5EkaNGjAhQsX+Pnnn82aJvDYY4+xYsUKNm7cSPXq1U3zdxs0aJBlasLevXtNz0fGG8TWrVtz9OhRtm3bRseOHRk1ahSfffYZAF26dMlSOB87doxvvvkGf39/IPvz5fnnn2fLli389NNPvPXWW+zatQuj0cjgwYNN9xMRuZUKXrF7+/bty3TBUXZ27dplKoqfeuop0/aWLVtSoEABbty4wW+//WZKMQEaNmxoupCmSpUqlC1bluPHj/Pnn3/y+OOP52hsV69eNU2DiIqKYtKkSQCmj6J/++23bO935MgRbty4AZBpfmXr1q1zXPBmGDRoEBs2bOB///ufqbC6WcZH0k5OTrz77rum7c7Ozly7do39+/eb0sxbhYaGEhUVxb59+6hatSoHDhygcePG+Pv78/PPP3PgwAESEhJIS0ujZs2aODs7my6SCgkJoUyZMgAYDAZat27N5s2biYqKyrJyQMbUgltFR0ebCsfx48dTsmTJTPvLli2bk6eI2rVrs2rVKlNRd+nSJQIDA3FxcaF69eocPHiQPXv2mFLycuXKUaxYsTx5fW/+ZOCJJ5647UVjzZo1M/091qhRw7T933//NT2P5goLC6NAgQJs2rSJwYMHs2XLFnx8fAgJCcnS9qWXXqJOnTr8+uuvTJkyhdTUVFOyf/78+Rw9Xrly5XJUtEZGRtKmTRvTG5969erx4osv5vzAROSBo4JX7N5rr71mml+a4b333suUCN4897Vw4cKmnx0cHChUqBAXL17MMj+2SJEimX738vICuOPH1LeKj483zQu9eU5ihn///Tfb+938GN7e3qaf75RU3k6FChV49tlnWbZsGe+88w4BAQGZ9l+5cgWA2NhYUzqXkzFCesE7f/589u3bR/ny5UlNTSU0NBR/f3/ef/99duzYYVoWLSPty3ieb34dbj62tLQ005gy3No2w6FDh0yJ/JIlSzK9OTBHnTp1WLVqFVFRUaYpCxlFX/Xq1fniiy84ePCg6XXJeAOQ16/v7Y7z1n03z69NTU2947HdSaFChahRowa//fYbv/zyC2fOnCE8PBwHh6yz4T766COmTZuWbT8Zz8Hd3On4bubr60vz5s1ZtmwZAO3atTO9ziIi2VHBK0LmouLixYuUKlUKSP/Y//Lly0DWYvLWK9QzCjVfX98cP27BggUxGAwYjUbef/99mjdvnqP73TyWy5cv4+rqCmD6ON1cffv2ZdWqVfzwww9ZLkbKKOT9/PxYtWqVWf3WrFmTAgUKEBUVZVodoG7dupQvXx4XFxd27NhBgQIFTNvh/1+LW984ZKSnGW9Cbp5znF0BBlC1alUmT55Mly5d2LZtG2vXrr3tXOo7yShg//zzT9MFahkFb0aa+tdff5nW481on9vX9+a/x5tf39vNs7akRo0a8dtvv5kucmvcuHGWNqmpqab9HTt2ZPjw4bi7u9OuXbtMy7TdTU6L1t27d/PVV1+ZVsl46623CAsLM/2tiojcShetiZBetGQUehlzYzN+Tk1NxcHBgUcffTTTfTZv3mz6yPbQoUOmuZV3+tKHW3l4eFC1alUg/Ur+DD/++CPz5883XeR1q0qVKpmKvPXr1wPpKdp3332X48e+WdGiRXnhhRcwGo1ZitqaNWsCcPToUU6fPg2kp77vv/8+ixcvNi1FdvPc5puPLygoiNOnT7NlyxZ8fX2pUqUKzs7OVK9e3ZSYFixYkGrVqgGYLq7avXs3p06dAtJT3Yw5ozVr1sTNzS1Hx+Xv709AQAA9e/YEYPLkyabXDOD48eP8/fffd03lM5YYu379uuk5zih4y5Qpg6+vLzExMRw4cAD4/7Q6L17fdevWmbZ///33OTru7GT3+uRERoH7xx9/4OTkxGOPPZalzeXLl00X7YWFheHu7s7Ro0dN0zlu/XKT3Izj5vsNGzaM1NRUxowZQ+vWrTl37hzjx4/PVX8i8mBQwStC+gL3AwYMAGDmzJn069ePgQMHMnr0aABefPHFbOd7tmvXjhEjRtC9e3cgvfhp2rSpWY/dt29fDAYDy5Yt47XXXmPAgAEMHjyYd95557bJZbFixUxpYWRkJK+//jrPP/88x48fN+uxb/bKK6/g7e2dpThp1KgRwcHBXL9+nc6dOzNy5Eg6duzIe++9x4YNG0zzRjPWnN20aRPDhw83zU/NKP7++eefTBcphYaGcvnyZWJjY6lVq5Zp/dynnnrK9HgdO3ZkxIgRtG/fnu3bt+Pq6srQoUPNPrbu3btTunRpzp49a1pZAtJf18cff5xPP/30rn1kpLaXLl2iaNGimeYDV69enevXr3Pt2jVKly5NiRIlTPty8/oWL17c9Hc0ZcoUXn/9dbp27cqff/5p9rFnKFasGJD+ycRrr71mmg5wNxUrVqR8+fJA+tSO7L4AonDhwqbz4+2332bMmDE8//zzNGrUCIBvvvnGNB0mYxxTpkxhxIgRZh9HZGQkx44do27durRt25bRo0dTqFAh1qxZk+nNqojIzVTwivznxRdfZObMmQQHB7N161Y2btxIlSpViIiIYNiwYVnat2vXjhYtWvDTTz8RHx9PaGgoc+fONX1En1NNmzblgw8+oHr16vzyyy9s27aNkJAQFixYYLrSPzsTJkwwXVS3detWypQpY7ooKjc8PT1Nq1PczNHRkQULFtC5c2dSU1NZtWoVycnJdO/enTlz5pjaPffcc4SEhGA0Gtm6davpwrKbi9yMaQt32u7s7MzChQvp3r07zs7OrFq1ipMnT5rmbObmCxpcXFxMr+Fnn32W6VvScurmC/NuvWjr5t9vfc1y+/q++eabmV7fUqVKmY7BycnJ7PHXrVuXJ598Ejc3N3bu3JnjC8kAU+Ga8d/svPPOOwQHB3Pu3Dl+++03Ro0axZtvvknVqlWJjY01PefDhw/noYce4vTp01lWS7mbzZs387///Q8XFxcmTJgApE8hyliq7M033+Ts2bNm9SkiDwaDMadXE4gIkP4lC7/99ht9+/alX79+1h6O2KmoqCguXrxI+fLlKVeuHABz585lxowZVKpUybT6hIiI3J0uWhMRsUFz585l3bp1eHt7ExYWRkpKimnJuYwpNCIikjMqeEVEbNDUqVN5//33WbduHT/++CMeHh6EhITQo0ePbFdKEBGR29OUBhERERGxa7poTUREROQBsXXrVh599NEsX8iUnU8//ZTGjRsTHBxM+/btTUsvAiQnJzN27Fjq1KlDSEgI/fv3N+uLl/KbCl4RERGRB8BHH31ERESE6ULYO1m3bh0zZ84kMjKSHTt20LBhQ1599VXTGtpTp05l165dLF++nI0bN5KSksLIkSMtfQi5poJXRERE5AHg4uLCV199laOC98svv6Rdu3bUrVsXNzc3+vTpg8FgYMOGDdy4cYOvv/6agQMHUqZMGXx8fBg6dCibNm2649fNW5MKXhEREZEHQLdu3ShYsGCO2h48eND0DZiQ/m2N/v7+HDhwgOPHj5OQkJBpf8WKFXFzc8s07cGWPBCrNGQsgP+gMRgMuLi4kJyczIN6beLg1YetPQSr8PVwZkLrqoz7PpoLV1OsPZx8t2DyLGsPwWoqlHqIg99EEvD0CGJO5fzLJexFj5H9rT0Eq3jQz3mAD9pXt/YQsnAOyZ8lBFN2f5znfcbFxeHt7Z1pm5eXFxcvXiQuLs70+80KFSpks/N4lfDaOYPBYO0hiBW4OTniYDDg5uRo7aFIPvMq6IajowNeBd2sPRTJRzrnJa/drn64W11hq3XHA5HwioiIiOQ3g8P9+wbEx8eHS5cuZdoWFxdHlSpVKFKkCACXLl3C3d0dAKPRyKVLl0z7bI0SXhERERHJJCgoiP3795t+T01N5eDBgwQHB1OmTBm8vb0zzdc9fPgwKSkpBAYGWmO4d6WCV0RERMQCDA6O+XLLK61atWLnzp0AdOrUieXLl/Prr79y9epVZsyYgaurK02aNMHR0ZEOHTowc+ZMTpw4QWxsLJGRkbRq1QpfX988G09e0pQGERERkQdAUFAQADdu3ABg/fr1AERFRQEQExNjWme3QYMGDB06lBEjRhAbG0tgYCDz5s3DxcUFgH79+nH16lXatm1LamoqjRs3Zvz48fl8RDmngldERETEAmxtDm9GYXs7hw9nXtmoc+fOdO7cOdu2zs7OjB07lrFjx+bZ+CxJUxpERERExK4p4RURERGxAFtLeB9kSnhFRERExK4p4RURERGxAIOjEl5boYRXREREROyaEl4RERERC3DQHF6boYRXREREROyaEl4RERERC9AqDbZDCa+IiIiI2DUlvCIiIiIWoITXdijhFRERERG7poRXRERExAIMDsoVbYVeCRERERGxa0p4RURERCxAc3hthxJeEREREbFrSnhFRERELEAJr+1QwisiIiIidk0Jr4iIiIgFKOG1HUp4RURERMSuKeEVERERsQCDoxJeW6GEV0RERETsmhJeEREREQvQHF7boYRXREREROyaEl4RERERC1DCazuU8IqIiIiIXVPCKyIiImIBDkp4bYYSXhERERGxa0p4RURERCxAc3hthxJeEREREbFrSnhFRERELEAJr+1QwisiIiIidk0Jr4iIiIgFKOG1HUp4RURERMSuKeEVERERsQAlvLZDCa+IiIiI2DUlvCIiIiIWoITXdijhFRERERG7poRXRERExAIMjkp4bYUSXhERERGxa0p4RURERCxAc3hthxJeEREREbFrSnhFRERELEAJr+1QwisiIiIidk0Jr4iIiIgFKOG1HUp4RURERMSuKeEVERERsQAHB4O1hyD/UcIrIiIiInZNCa+IiIiIBRiU8NoMJbwiIiIiYteU8IqIiIhYgMGghNdWKOEVEREREbumgvc+ExMTQ5s2bXjooYcoW7Yso0ePJi0tLdu269evp3r16nh7exMYGMjSpUsz7X/vvffw8/PDx8eHBg0asHv37vw4BMmlK/+eZHVEL+Z3fZSFPRrz6+fvYszmtT+1/3dGtfDHYDAwqoU/H3YI4cMOIfzy2QxTm72rP+fz3q35qEsoK0Z25fzR6Pw8FMmF3p2aErf9A1J2f0y5EkVu2y40qBIAWz8dzcFvIuncum6m/f27NOevNW9z5de5bPtsNNX9y1p03JJ7Oufvfw4Ohny5yd1ZteBt0qQJDRs25Nq1a5m279ixgyZNmph+37p1K48++iiDBg3K7yHaFKPRSKdOndi+fTtvvPEGjRo1YsaMGcyZMydL21OnTtG+fXvi4+OJiIjA29ubl19+mV27dgGwcuVKhg0bxsMPP8yECRM4duwYzz77bJbXQmyD0Whk7bTBnD20hxrP9KBUUB32rFpI1PdLsrRNuRYPwNNPP034oIk0fHUMDV8dQ8VHWwBw9Nf1/PLZdLxKlKNO575cOXeK79/qz/XkxHw9JskZR0cHfvhwCFMHd+TGjdQ7ti35kDdTh3QC4P0l67gUf42PJ75MSNVyADzTtCbThnTmr2P/Mub95ZQv5cvKWQNxc3W2+HGIeXTOi6WcPHmSHj16UL16derVq8fUqVOzDc66d+9OUFBQpltAQAAjRowAYNiwYQQEBGTa/9RTT+X34eSY1efwJicnM2fOHIYMGZLt/o8++oivvvqKcuXK5fPIbM/OnTuJioripZdeYujQoaSkpLBmzRoWLlxI3759M7VdtmwZSUlJTJw4kbZt21KjRg2aNm3KokWLqFGjBgsXLgRg3rx5lCxZksTERCZMmMDatWt55plnrHB0cifn/j5A7LG/qNq0LTXa9iD1xnWO/fET0Ru/JviJLpnaJidcAdLfUF5+uAkn4q7h6PT/BU30ppXp+/u8iUfhotxISeb3pbM5vnsbleo2z7djkpxxdXaisJcH9btGMHVIJxrW8r9t286P18XV2QmAZT/sYNWm3WxZOJIXnnqM3dHHeDE8DIAeYxdw5vwl3FycebNvW1o/FsSK9X/ky/FIzuictw+2tkqD0Wikb9++VK5cmS1bthAbG8vLL79MkSJF6N69e6a2H3/8cabfExMTeeKJJ3jiiScAuHLlCv379+e1117Lt/HfC6tPaejXrx+LFy8mJiYm2/0uLi4qeP+zb98+AAICAgBwdnamUqVKREdHk5SUlG3batWqAeDvn/4/yT179pj2e3t7U7JkyUz7Na3BNsX+cxgAnzLpH1c7FnDCq3gZ4k4e5UZKcqa2yf+lPfPnz2d8mxAWdKvPmin9Sbxy0dSXs0dBPAoXBaBw6YoAXDh6KF+ORcxzLSmFes9PZM/h43dt+4hf5ukJ0UdPA5gS3kf8yhJ35Spnzl/KvN9f/77aGp3zYglRUVEcPnyY0aNH4+XlRcWKFenZsyfLli27631nz55NUFAQjz32GJBe8Hp5eVl6yHnG6gVv5cqV6dChAxEREdnu79atGwULFsznUdmmixfT//Hy8PAwbfP09MRoNJr23drW09MTwPQcxsbGmvbf3M+t+8W2JMVfAsDJ1c20zcnVA4xGkhMuZ2qbkfY4OjrSbugUytVswPFdW9k6P/K/vi7j5Op+Uz/umR5DbIvRaCQ1Nft5+rcq7OWZ6ff4a+lvhH2907cX8fIg4Vpylv1FvPVvrK3ROW8fDA6GfLnl1MGDBylVqhTe3t6mbQEBAfzzzz8kJCTc9n4nT55kyZIlDB061LTtypUrbNiwgWbNmlG7dm169OjBP//8k5unKV9YfUoDpKe8rVq1Yt26dTRvnvcfr9jLsiBGoxEABweHLMd067aM+TgGgwGDwYCDg0Om341Go+nnjO236/t+Vsbb7e6N7gNHXNJP1cLuLqZjcimQ/pqW9HKj0E3HWezFPnR9pRe9mwSyaNdp/EMbMal9Pf75fTMlCzphAAo4OJj6SfZ0AcDTpYDdPF/2eiGWp7srAAGVSuHj5ZFlv1fB/3/9/MqXwOG/c9nZqQDV/cvi4OCAUwFH0/NTqcx/iZ+Xh908Z/byN6xz3jwnLmk+ck7ExcVlSWUzfo+LizOFZLf68MMPefrppylVqpRpW6lSpfD19WXy5Mk4OTkxceJEXnnlFb777jucnW3vugCbKHg9PT0ZMmQIkZGRhIWF5Xn/Li4udlHElShRAoCkpCRcXdP/x5eQkICjoyMlS5bExcXF1LZYsWIAxMfH4+LiwtWrV03bXV1d8fX1JSEhwdRPxpSIEiVKmLbZg5HN/aw9hDzhG1OV9QuhYVkPBvx3TEsdruPo6MiEZ+pkeu1v1qNueaA884oU4ezZs/SqVYwPHvIlMTHR9NysiD/AJ0DLGlXs5vka2Xy8tYdgUd+8N/CubRZFvmr6uVxJX35bMh6A4r5epp8ztG1Wi7bNauXhCOVe6Zw3T68v91h7CNlysIPa4+LFi6xatYqvv/460/YPP/ww0+9vvvkmoaGh/P7779SvXz8/h5gjNlHwAoSHh7Ns2TLmzp1L3bp1734HMyQnJ9+90X0gMDAQSJ+Dk5SURFJSEkeOHCEwMJCrV69y6NAh3NzcKFu2LMHBwSxZsoSDBw/i7+9vWp2hevXqJCUlERISwpo1a4iJiaFEiRKmub3BwcFZ5gPfz2ZsPWbtIeSJU9fTl6Fa/ON2rga04npKMof+/Iui5asQ+WM0l8+dwcnVFe+iJVn3yUyO7trGyiWfsSvZh7+Pn+bcufO4ehZi3u5YCpX148yOzQxfupVCRYqy8ZvNAMQUKMHkdYeteJR5Z+XHWa9ktwcfjHmRmgHlebrfTM5cuISLsxPFfb1ISr7Ov7GXee6Jegx8viUAXUfMxd3VmbnjXmLJml94Z9Fapg/pTFhNP1r3mkbspQR6tG3Iq+0bM2TaEn76wz5e+/Duna09hDyhc14soUiRIly6dCnTtri4OAAKFy6c7X02bNhAxYoVqVSp0h379vT0xMvLi/Pnz+fJWPOazRS8AOPGjaNz586mC6nySsZUgPtdSEgINWvWZMmSJZQrV449e/aQmJhIz549+f3332nVqhVhYWGsXbuWjh07MmnSJMaOHcupU6f4/PPPcXJyonv37hiNRnr06MGaNWvo2bMnzZo1Y86cOVSoUIEWLVrYzfMFdvQxV9FKPFSpGrs3rMLBuxjnjx7ienISVZq1Y9cfO/l2wiuUCKjJ0+Png08Jjh/eT9euXSkX9hR/bPyOtLRUgh7vwolLiVRs3JbDOzazeMowyjxSj91fL6JQsdK4V6ltN8/XnkN3v8DrfuHl6cbgbq0A8PVJ/7ixRf1ArsQnEns5gWlDOrNl5yGav/I2Z85f4pVnG+Hh5kJ1/7I0qRNAyvUbRM5fzaGYM0xb+D1hNf0Y1LUV637Zz7PNavH3iXN8sGwj1++y5Nn9oqad/A3rnLcPtrZKQ1BQEKdPnyYuLg4fHx8g/SL2ypUrZ7qu52bbtm2jXr16mbYlJCQwY8YMXn31VdMnynFxccTFxVGmTBnLHkQuWf2itZv5+/sTHh7Ou+++a+2h2KwvvviC+vXrM3nyZLZt28aYMWN46aWXsrQrXrw4y5cvx93dnVGjRpGYmMjixYtNqzG0bt2a6dOnEx0dzbhx4/D392fFihU2Oe9G0rV4fSolqtZg55fzOBO9i9odelG1adss7fwbPUWbvmO4ceMG6z55h+SEK9R9fhAhbXsAUK5GGI91H0bcyaP8tuR9fEpXoPXwWTgWcMrvQ5Ic8C7ozohX2jDilTaUK+ELQJ9OzRjxShvirmReN/vf2Cu8PjU93e73XHPcXZ3p9MYcDsWcAeD7bfsYMOVzqlYsycS+zxIdc5rw/jPtpti1NzrnJa9VrVqV4OBgIiIiuHLlCocPH2bevHl06ZK+1F2rVq3YuXNnpvtER0dTuXLlTNs8PT3Zs2cPkyZN4vLly1y+fJkJEyZQtWpVQkJC8u14zGEwWjHOa9KkCZGRkYSGhpq2Xb58mZYtW+Lu7s7GjRsJCgoC4MaNGwAUKJAeSkdFReX4cRITH8x3sAaDAVdXV5KSkuwqtTXH4NUP5sd1ZbzdGNncj8nrDj+QCc6CybOsPQSrqe5flt+WjKdO5/F2lXTnVI+R/a09BKt40M95gA/aV7f2ELIIGvpdvjxO1NtP5Ljt2bNnGTt2LDt27MDDw4PnnnvOtJa/n58fH330EQ0aNDC1DwkJYdq0aTRt2jRTP6dPn2by5Mn89ttvFChQgNq1azNy5EhT4mtrrDqlYePGjVm2eXl58euvv5p+N6ewFREREZHbK168OPPmzct23+HDWUOi263PX7JkSd5///08HZsl2dQcXhERERF74WBjc3gfZDY1h1dEREREJK8p4RURERGxAINiRZuhl0JERERE7JoSXhERERELsIdvebUXSnhFRERExK4p4RURERGxAK3SYDtU8IqIiIhYgK19tfCDTFMaRERERMSuKeEVERERsQAlvLZDCa+IiIiI2DUlvCIiIiIW4KBlyWyGEl4RERERsWtKeEVEREQsQHN4bYcSXhERERGxa0p4RURERCxACa/tUMIrIiIiInZNCa+IiIiIBeirhW2HEl4RERERsWtKeEVEREQswKB1eG2GEl4RERERsWtKeEVEREQswKBY0WbopRARERERu6aEV0RERMQCtEqD7VDCKyIiIiJ2TQmviIiIiAXom9ZshxJeEREREbFrSnhFRERELEDr8NoOJbwiIiIiYteU8IqIiIhYgFZpsB1KeEVERETErinhFREREbEArdJgO5TwioiIiIhdU8IrIiIiYgGOSnhthhJeEREREbFrSnhFRERELEAJr+1QwisiIiIidk0Jr4iIiIgFKOG1HUp4RURERMSuKeEVERERsQAlvLZDCa+IiIiI2DUlvCIiIiIWoITXdijhFRERERG7poRXRERExAIKKOG1GUp4RURERMSuKeEVERERsQDN4bUdSnhFRERExK4p4RURERGxACW8tkMJr4iIiIjYNSW8IiIiIhbg6KBc0VbolRARERERu6aEV0RERMQCNIfXdijhFREREXlAnDx5kh49elC9enXq1avH1KlTSUtLy9Ju+fLl+Pv7ExQUlOl24cIFAJKTkxk7dix16tQhJCSE/v37c/Hixfw+nBxTwSsiIiJiAY4Ohny55ZTRaKRv3774+PiwZcsWFi9ezPfff8/ChQuztI2Pj+fRRx8lKioq083X1xeAqVOnsmvXLpYvX87GjRtJSUlh5MiRefXU5TkVvCIiIiIPgKioKA4fPszo0aPx8vKiYsWK9OzZk2XLlmVpe/nyZby8vLLt58aNG3z99dcMHDiQMmXK4OPjw9ChQ9m0aRP//vuvpQ8jV1TwioiIiFiArSW8Bw8epFSpUnh7e5u2BQQE8M8//5CQkJCp7ZUrVzh27Bht27alZs2aPPPMM2zZsgWA48ePk5CQQLVq1UztK1asiJubGwcOHLi3J81CVPCKiIiIPADi4uKypLYZv8fFxWXa7u3tTeHChZk8eTJbt27l6aefpk+fPvz999+mtrf2VahQIZudx/tArNIwePVhaw/BKsp4uzGyuR8zth7jxKVEaw/HKmY86WftIViFwZD+jn9wWDmMRqOVR2MN/a09AKsp4+0GQHj3ztR8AM97nfMP6jlvmxwN9+8qDf369cv0+4svvsjq1atZtWoVDRo0uO39DDZ6zEp4RURERB4ARYoU4dKlS5m2ZaS1hQsXvuv9S5cuzfnz5ylSpAhApr6MRiOXLl0y7bM1KnhFRERELMDW5vAGBQVx+vTpTNMX9u3bR+XKlfHw8MjUdu7cuWzfvj3TtpiYGMqUKUOZMmXw9vbONF/38OHDpKSkEBgYmMtny7JU8IqIiIg8AKpWrUpwcDARERFcuXKFw4cPM2/ePLp06QJAq1at2LlzJ5Ce/L755pv8888/pKSk8Mknn3D8+HHatm2Lo6MjHTp0YObMmZw4cYLY2FgiIyNp1aqVadkyW/NAzOEVERERyW+2+E1r7777LmPHjiUsLAwPDw+ee+45nnvuOSA9wb127RoAgwcPJi0tjeeff57ExET8/PxYuHAhxYoVA9Ln+F69epW2bduSmppK48aNGT9+vLUO665U8IqIiIg8IIoXL868efOy3Xf48P9f5O/s7MzIkSNv+2USzs7OjB07lrFjx1pknHlNBa+IiIiIBRSwwYT3QaU5vCIiIiJi15TwioiIiFiALc7hfVAp4RURERERu6aEV0RERMQClPDaDiW8IiIiImLXlPCKiIiIWIASXtuhhFdERERE7JoSXhERERELUMJrO5TwioiIiIhdU8IrIiIiYgFKeG2HEl4RERERsWtKeEVEREQsQAmv7VDCKyIiIiJ2TQmviIiIiAUo4bUdSnhFRERExK4p4RURERGxACW8tkMJr4iIiIjYNSW8IiIiIhaghNd2KOEVEREREbumhFdERETEApTw2g4lvCIiIiJi15TwioiIiFiAo0EJr61QwisiIiIidk0Jr4iIiIgFOCjhtRlKeEVERETErinhFREREbEARwW8NkMJr4iIiIjYNSW8IiIiIhbgoHV4bYYSXhERERGxa0p4RURERCxA6/DaDiW8IiIiImLXlPCKiIiIWIDW4bUdKnhFRERELEDLktkOTWkQEREREbumhFdERETEArQsme1QwisiIiIidk0Jr4iIiIgF6KI126GEV0RERETsmhJeEREREQvQKg22QwmviIiIiNg1JbwiIiIiFqA5vLZDCa+IiIiI2DUlvCIiIiIW4Kh1eG2GEl4RERERsWtKeEVEREQsQHN4bYcSXhERERGxa0p4RURERCxA6/DaDiW8IiIiImLXlPCKiIiIWIDm8NoOJbwiIiIiYtdU8N5nrvx7ktURvZjf9VEW9mjMr5+/izEtLUu7U/t/Z1QLfwwGA6Na+PNhhxA+7BDCL5/NMLXZu/pzPu/dmo+6hLJiZFfOH43Oz0MRM8XExNCmTRseeughypYty+jRo0nL5rUHWL9+PdWrV8fb25vAwECWLl2aaf97772Hn58fPj4+NGjQgN27d+fHIUgumHPOf9ghJMt5r3P+/qVz/v7n6GDIl5vcnQre+4jRaGTttMGcPbSHGs/0oFRQHfasWkjU90uytE25Fg/A008/TfigiTR8dQwNXx1DxUdbAHD01/X88tl0vEqUo07nvlw5d4rv3+rP9eTEfD0myRmj0UinTp3Yvn07b7zxBo0aNWLGjBnMmTMnS9tTp07Rvn174uPjiYiIwNvbm5dffpldu3YBsHLlSoYNG8bDDz/MhAkTOHbsGM8++yzXrl3L78OSu8jNOV+1XlM++ugj03mvc/7+pHNeJG9ZteBt0qQJDRs2zHLS7dixgyZNmgBw8uRJevXqRZ06dahXrx5Dhw7l8uXL1hiu1Z37+wCxx/7i4bDHqdG2B036RlDAxZXojV9naZuccAVIf45rNA+nSoMnqdq0LcUqBwIQvWll+v4+b/LIk10Jevw5rsVd4Pjubfl2PJJzO3fuJCoqik6dOjF06FDmz5+Pu7s7CxcuzNJ22bJlJCUlMXHiRPr27cvUqVNJS0tj0aJFAKb7zJs3j/79+9O7d2/Onj3L2rVr8/GIJCdyc85XrB7KCy+8QEjTp3XO38d0ztsHB0P+3Mxx8uRJevToQfXq1alXr57p7yU7X3zxBS1atCAkJIQ2bdqwfv16075hw4YREBBAUFCQ6fbUU0/dy9NlUVZPeJOTk7N9x5qhV69eeHt7s2nTJlatWkVMTAxvv/12Po7QdsT+cxgAnzKVAHAs4IRX8TLEnTzKjZTkTG2T/0t75s+fz/g2ISzoVp81U/qTeOWiqS9nj4J4FC4KQOHSFQG4cPRQvhyLmGffvn0ABAQEAODs7EylSpWIjo4mKSkp27bVqlUDwN/fH4A9e/aY9nt7e1OyZMlM+/URp+3JzTm/84evcHd3Z8LTNXTO38d0zoslGI1G+vbti4+PD1u2bGHx4sV8//332b6R+vHHH5kxYwZvvfUWv//+O927d2fgwIEcP34cgCtXrtC/f3+ioqJMt1WrVuXzEeWc1Qvefv36sXjxYmJiYrLsi4+PJzAwkCFDhuDh4cFDDz1EeHg4O3futMJIrS8p/hIATq5upm1Orh5gNJKckDn1zkh7HB0daTd0CuVqNuD4rq1snR/5X1+XcXJ1v6kf90yPIbbl4sX0osXDw8O0zdPTE6PRaNp3a1tPT08AChYsCEBsbKxp/8393LpfbEduznkHB0c+++wzqtZrrHP+PqZz3j44Ggz5csupqKgoDh8+zOjRo/Hy8qJixYr07NmTZcuWZWmblJTE66+/TkhICAUKFOCZZ57B09OTvXv3AukFr5eXV549V5Zm9WXJKleuTIcOHYiIiGDBggWZ9hUsWJDIyMhM206dOkWJEiXMegxfD2fcnBzveazWdsQl/eUq7O5CGe/0/wG6FEh/z1LSy41C3v//P8ViL/ah6yu96N0kkEW7TuMf2ohJ7evxz++bKVnQCQNQwMHB1E+ypwsAni4FTNvsgcFOloQxGo0AODg4ZDmmW7dlfDRlMBgwGAw4ODhk+t1oNJp+zth+u77vV/byN2zuOd+qcw/KFitM57AqXCwaxIDWNXTO36d0zpsn4/mSOzt48CClSpXC29vbtC0gIIB//vmHhIQE05smIMv0hCtXrpCQkGCqwa5cucKGDRtYsGABly9fJjg4mDFjxlC+fPn8OBSzWb3ghfSUt1WrVqxbt47mzZvftl1UVBSLFy9m1qxZZvU/oXVVu1gLzzemKusXQsOyHgxo7gfAUofrODo6MuGZOri4uGR7vx51ywPlmVekCGfPnqVXrWJ88JAviYmJjPyvnxXxB/gEaFmjimmb2I6Mf2CSkpJwdXUFICEhAUdHR0qWLJnptS9WrBiQ/gmJi4sLV69eNW13dXXF19eXhIQEUz8ZH4+WKFHCtO1+Zy9/w7k95wH6NA0kQuf8fUvnvHkSE23z4ktbqz3i4uKypLIZv8fFxWUqeG9mNBoZPXo0gYGB1KhRA4BSpUrh6+vL5MmTcXJyYuLEibzyyit89913ODs7W/ZAcsEmCl5PT0+GDBlCZGQkYWFh2bb5448/6NWrF0OHDqVhw4Zm9T/u+2i7SHhPXS8CwOIft3M1oBXXU5I59OdfFC1fhcgfo7l87gxOrq54Fy3Juk9mcnTXNlYu+YxdyT78ffw0586dx9WzEPN2x1KorB9ndmxm+NKtFCpSlI3fbAYgpkAJJq87bMWjzFuDw8pZewh5IjAw/cKjqKgokpKSSEpK4siRIwQGBnL16lUOHTqEm5sbZcuWJTg4mCVLlnDw4EH8/f1NV2pXr16dpKQkQkJCWLNmDTExMZQoUcI0zy84ODjL3MD71Yytx6w9hDxh7jn/5+8/8fLot5nwwhO8s2anzvn7mM55sRXXr19n+PDhxMTEsHDhQtMnCB9++GGmdm+++SahoaH8/vvv1K9f3xpDvSObKHgBwsPDWbZsGXPnzqVu3bqZ9m3atIk33niDCRMm8MQTT5jd94WrKXk1TOsqWomHKlVj94ZVOHgX4/zRQ1xPTqJKs3bs+mMn3054hRIBNXl6/HzwKcHxw/vp2rUr5cKe4o+N35GWlkrQ4104cSmRio3bcnjHZhZPGUaZR+qx++tFFCpWGvcqtTlxyTbfKeeGvXzMFRISQs2aNVmyZAnlypVjz549JCYm0rNnT37//XdatWpFWFgYa9eupWPHjkyaNImxY8dy6tQpPv/8c5ycnOjevTtGo5EePXqwZs0aevbsSbNmzZgzZw4VKlSgRYsWdvN82c3fsJnn/OkjB/lw3CB84v5i5kef6py/j+mctw+OVr9SKrMiRYpw6dKlTNvi4uIAKFy4cJb2SUlJ9O7dm5SUFBYvXkyhQoVu27enpydeXl6cP38+T8ecV2zqpRg3bhwLFy40XQEIsGvXLoYNG8asWbNyVezamxavT6VE1Rrs/HIeZ6J3UbtDL6o2bZulnX+jp2jTdww3btxg3SfvkJxwhbrPDyKkbQ8AytUI47Huw4g7eZTflryPT+kKtB4+C8cCTvl9SJJDX3zxBfXr12fy5Mls27aNMWPG8NJLL2VpV7x4cZYvX467uzujRo0iMTGRxYsXm67Mbt26NdOnTyc6Oppx48bh7+/PihUrbPIjKDHvnH+s+3DSUm8wcuRIrl25rHP+PqdzXvJaUFAQp0+fNhW5kL6KR+XKlTNd2Ajpbx4HDRqEs7MzH3/8caZiNyEhgTfffJN///3XtC0uLo64uDjKlClj+QPJBYPRim/vmjRpQmRkJKGhoaZtEyZMYN26dTg7O/Pjjz/y1FNP0aVLF7p06ZLrx+n15Z48GO39p4y3GyOb+zF53WG7SnDMMePJB3NuosFgwNXVlaSkpAcywRm82n4+ojfXg37e65x/MM95ADc327v48tdjF+/eKA/ULZc1nb2djh07Urp0acaNG8eZM2fo0aMHvXv35rnnnqNVq1ZERERQq1YtVq1axcyZM/nuu++yfW7btm1L6dKlmThxIpAeWh4/fpyvvvrKNO3BltjciAYOHMiNGzeA9DUE//77b6ZMmZJpYeOgoCBOnTpl5ZGKiIiI3F/effdd4uPjCQsL46WXXqJTp04899xzQPrXWWd8Gdjy5cs5e/YsderUyVR/jR49GoD333+ftLQ0mjdvTuvWrTEajXzwwQc2WeyClefwbty4Mcs2Ly8vfv31V9Pvhw8/uEmNiIiI3L/MWSM3vxQvXpx58+Zlu+/mmuvTTz+9Yz8lS5bk/fffz9OxWZJtluEiIiIiInnEZlZpEBEREbEntrYO74NMCa+IiIiI2DUlvCIiIiIWYGvr8D7I9FKIiIiIiF1TwisiIiJiAZrDazuU8IqIiIiIXVPCKyIiImIBCnhthxJeEREREbFrSnhFRERELMABRby2QgmviIiIiNg1JbwiIiIiFqA5vLZDCa+IiIiI2DUVvCIiIiIW4GDIn5s9WrNmDV27dqVZs2YApKSkMH/+/Fz3Z/aUhqioKCZOnMhff/1FUlJSlv3R0dG5HoyIiIiIPNi+/PJLIiMjeeaZZ9i7dy8AcXFxfPHFF6SlpdGzZ0+z+zS74B07dizOzs4MGDAAV1dXsx9QRERE5EGgOby58/nnnzNnzhzq1q3Ll19+CUCxYsWYNWsWgwYNyp+CNyYmhu3bt+Pu7m72g4mIiIiI3MmJEycIDQ0FwHDTu4Zq1apx7ty5XPVp9hze4sWLk5aWlqsHExEREXlQOGDIl5u98fDw4OTJk1m2R0VFUbBgwVz1aXbBO2TIECIjI0lISMjVA4qIiIiI3E7r1q0ZPHgwW7ZswWg0sn//fpYuXcqAAQN44oknctWn2VMa5syZw8mTJ/n666/x9vbGwSFzzbxt27ZcDURERETEnmgOb+4MGTKEKVOm0KdPH27cuEG7du0oUKAAHTt2ZPDgwbnq0+yCNywsDCcnp1w9mIiIiIjInTg7OzN27FiGDBnC8ePHMRgMlC1bFjc3t1z3aXbBO2jQoFw/mIiIiMiDwl7XyLW006dPm34uVKgQkL4sWVxcHKmpqZQpU8bsPnP11cLr16/nq6++MlXdFSpUoHPnztSvXz833YmIiIiIANCkSZNMqzPcKjff+WB2wbtmzRqGDBlCvXr1qFevHkajkSNHjvDKK6/wwQcf0LBhQ7MHISIiImJvFPDmzkcffZTp97S0NE6dOsXKlSt55ZVXctWn2QXv/PnzmTZtGo8//nim7StWrGD27NkqeEVEREQk18LCwrLd3qxZM0aMGEHz5s3N7tPsZcmOHz+e7QM9+eSTHD161OwBiIiIiNgjB4MhX24PiqJFi2a7Pm9OmJ3wFipUiPPnz1OyZMlM22NjY3F0dMzVIEREREREAFJSUrJsu379Ops3byYxMTFXfZpd8D766KO8/vrrDBkyhEqVKmEwGPjrr7+YMWMGDRo0yNUgREREROzNAxS+5qng4ODbXrQ2YMCAXPVpdsH7xhtv0L9/f7p06ZJpMI8++igjR47M1SBERERERAAmT56cpeB1cXHh4Ycf5uGHH85Vn2YXvF5eXnz66accOXKEY8eOYTQaqVChApUqVcrVAERERETskdkXSgkAbdu2zfM+zSp4b9y4QZ8+fZg7dy6VK1emcuXKeT4gEREREXmwvP766zluO336dLP7N6vgLVCgAH/99Rdnz56lePHiZj+YiIiIyIPiTl+eIJmdO3fOov2bPaWhZ8+eDBo0iNatW1O6dGmcnZ0z7X/sscfybHAiIiIiYv8WLVqUo3aHDh3KVf9mF7zjx48HYPfu3Vn2GQyGXH3dm4iIiIi9cVDAe0/S0tK4ceOG6fczZ87wwgsvsGPHDrP7Mrvg3bBhg9kPIiIiIiKSE8ePH2fo0KHs37+f1NTUTPtye/2Y2QVvqVKlcvVAIiIiIg8STeHNnYiICDw8PBgzZgwTJ05kwoQJ7Nu3j7/++osPP/wwV32aXfA2adLktpOwU1NT2bx5c64GIiIiIiKyd+9eNmzYgKenJ5MnT+bZZ5/l2Wef5dNPP2X+/PkMHjzY7D7NLngff/zxTAVvWloap0+f5rfffuOFF14wewAiIiIi9kjr8OZOamoq7u7uQPoKYSkpKTg7O9O5c2eaNGmSPwXvkCFDst2+Z88evvrqK7MHICIiIiKSoWLFisydO5eePXtSsmRJfvjhB5566inOnDlDYmJirvrMszcf1atXz3blBhEREZEHkcFgyJebvenTpw+zZ88mMTGRZ599lmHDhvH444/z7LPPEhYWlqs+zU54b+fEiRNcvnw5r7oTERERkQfIjBkz6NKlCw0bNmTLli14enry4osv4uvry+7duylVqhSdO3fOVd9mF7ydOnXKsi0lJYWYmBiaNGmSq0GIiIiI2Butw2ueZcuW8fHHH9OsWTO6du1KkSJFAHjyySd58skn76lvswveChUqZNnm4uJiuoJORERERMRcW7duZc2aNSxdupQuXbpQtWpVunXrxhNPPJHlm33NZXbBGxkZeU8PKCIiIvIgUMBrHmdnZ8LDwwkPD+fQoUN88cUXTJw4kalTp9KhQwc6d+5MsWLFctV3ri5aO336NO+//z4jRowwbdu3b1+uBiAiIiIicjN/f3/efPNNfvrpJ/r06cNPP/1E06ZNc7UkGeSi4P3jjz9o1aoVa9euZfXq1UD6BWtdunRh/fr1uRqEiIiIiL1xMOTPzZ55enrSpUsX3njjDYKDg/n+++9z1Y/ZBe/MmTMZPHgw3377rWkpjDJlyvDWW28xZ86cXA1CRERERORmly5dYv78+TRr1oxXXnmFYsWKsXjx4lz1ZfYc3j///JMFCxYAZFr7rWXLlowaNSpXgxARERGxN/a4Rm5+2Lt3L1988QU//PAD7u7udOjQgeeeey7X83chFwVvWlqa6Svebnb+/HmcnJxyPRAREREReXB99dVXfPHFF0RHR+Pv78/YsWNp06bNPa/QALmY0lCvXj2mTJmS6avdjh49yrBhw6hbt+49D0hERETEHmgOr3nGjRtH6dKlWbRoEV9//TXPPvtsnhS7kIuEd8SIEbz22mvUrFmTtLQ0QkJCSEpK4uGHH9aSZSIiIiKSKxs2bKB48eIW6dvsgrdEiRKsXLmSrVu3EhMTg4ODAxUqVKB+/fqaqyIiIiLyH1VF5rFUsQu5XIfXYDDQoEEDXnjhBbp27cpjjz2mYldERETExp08eZIePXpQvXp16tWrx9SpU0lLS8u27aeffkrjxo0JDg6mffv2HDhwwLQvOTmZsWPHUqdOHUJCQujfvz8XL17Mr8MwW44T3pu/ZOJ2DAYDkydPvqcBiYiIiNgDBxsLA41GI3379qVy5cps2bKF2NhYXn75ZYoUKUL37t0ztV23bh0zZ87kgw8+4JFHHmHBggW8+uqr/Pjjj7i7uzN16lR27drF8uXL8fT0ZMSIEYwcOZIPP/zQSkd3Z7lKeLOzfft2vv7667zqTkRERETyUFRUFIcPH2b06NF4eXlRsWJFevbsybJly7K0/fLLL2nXrh1169bFzc2NPn36YDAY2LBhAzdu3ODrr79m4MCBlClTBh8fH4YOHcqmTZv4999/73mcx44dy3Z7SkoKf/zxR676zHHCe7sL0mJjY5kwYQLx8fGMHj06V4MQERERsTc2FvBy8OBBSpUqhbe3t2lbQEAA//zzDwkJCXh6emZq+/jjj5t+NxgM+Pv7c+DAAapVq0ZCQgLVqlUz7a9YsSJubm4cOHDgntbLBXjqqafYu3dvlu1JSUn07t2bHTt2mN2n2Ret3WzlypVMmTKF4OBgvvvuO0qUKHEv3VnMgsmzrD0Eq6juX5aRzcez8uMl7Dl03NrDsZL+1h6AVZTxdmNkcz9mbD3GiUuJd7+DnZnxpJ+1h2A1GddTDA4rh9FotPJo8t/g1YetPQSreNDPeYAP2le39hBsXlxcHF5eXpm2ZfweFxeXqeCNi4vLVBhntL148SJxcXGZ7puhUKFC9zSPd+3ataxdu5br16/z+uuvZ9l/6tSpXPedq4L37NmzjBkzhqioKIYPH054eHiuByAiIiJijwz38ZvO2y1GcLdFCu5lEYOKFStStGhRjEYj586dy7K/UKFCTJo0KVd9m13wLl68mOnTpxMWFsZ3331HkSJFcvXAIiIiIpJ/ihQpwqVLlzJty0hrCxcunGm7j49Ptm2rVKliqv0uXbqEu7s7kH5B3KVLl+6pLnz44YcZPnw4Z8+eZebMmbnuJzs5Lnj/+ecfRo8ezfHjx3n77bdp1qxZng5ERERExK4Ys1/uy1qCgoI4ffo0cXFx+Pj4ALBv3z4qV66Mh4dHlrb79+83fYqfmprKwYMHadeuHWXKlMHb25sDBw5QsmRJAA4fPkxKSgqBgYH3PM6MYvfixYskJSVl2Z/xmObIccH79NNP4+npyauvvkpCQgIrV67Mtp2mN4iIiIiAwcYK3qpVqxIcHExERATjxo3jzJkzzJs3j969ewPQqlUrIiIiqFWrFp06dWLAgAE0a9aMoKAg5syZg6urK02aNMHR0ZEOHTowc+ZM/P39cXd3JzIyklatWuHr63vP4/zpp58YMWJElvnARqMRg8FAdHS02X3muODNiKgXLlx42zYGg0EFr4iIiIiNevfddxk7dixhYWF4eHjw3HPP8dxzzwEQExPDtWvXAGjQoAFDhw5lxIgRxMbGEhgYyLx583BxcQGgX79+XL16lbZt25Kamkrjxo0ZP358noxx8uTJ1KhRg6efftr0ePcqxwXvxo0b8+QBRURERB4INpbwQvrX986bNy/bfYcPZ17lpHPnznTu3Dnbts7OzowdO5axY8fm+RjPnj3LqlWrcHZ2zrM+8+yLJ0RERERE7lXFihVJSEjI0z5V8IqIiIhYgtGYPzc7M2HCBCIjIzly5AjJycmkpKRkuuXGPX3xhIiIiIjIvfL398+0hq/RaGT16tXZtrXoRWsiIiIiYgYbnMNrqyZPnnxPX1pxNyp4RURERMSq2rZta9H+zS54r169yvLly4mJicl2MeDIyMg8GZiIiIjI/czW1uG9X7zxxhsUKJB9iero6Ejx4sVp1qwZ/v7+Oe7T7IL39ddf55dffqFKlSq4urqae3cRERERkdu6ePEi+/fv59q1a1SoUAGDwUBMTAwFCxakVKlSbNu2jQ8//JBZs2bRpEmTHPVpdsG7Y8cOVq5cSYUKFcw+ABEREZEHhhLeXGnVqhXe3t6MHz+eggULAhAfH8+kSZNo1KgRrVq14tNPP2X27Nk5LnjNXpbM19c3V99hLCIiIiJyNx988AFvvvmmqdgFKFiwIKNHj2bmzJlA+pdiHDt2LMd9ml3wvv7660ycOJEzZ86Ye1cRERGRB4cxLX9udiY+Pp6rV69m2Z6cnMy5c+cAuHLlCo6Ojjnu0+wpDe7u7mzevJnly5dnuz83a6OJiIiIiADUqFGDXr168corr1C2bFkcHBw4efIkn3zyCdWqVSMlJYU+ffpQt27dHPdpdsE7fvx4AgICaNSoEW5ububeXUREROTBYIfpa3548803GTBgAAMHDjStzWs0GqlcuTLvv/8+zs7O+Pr6Mnbs2Bz3aXbBGxcXZ3owEREREZG8VKxYMZYuXcq5c+c4ffo0RqORokWLUqpUKVOb2bNnm9Wn2QVvaGgoMTEx+Pn5mXtXERERkQdHmhLenLp+/TpOTk4ApKSkAODt7Y23t7epTcb23ISuZhe8zZo14/XXX6dx48aULFkSB4fM17117NjR7EGIiIiIyIOrVq1a7N27F4Dg4OA7fs1wbq4XM7vgHT16NABHjhzJss9gMKjgFREREUHftGaON9980/Tz5MmT71jw5obZBe/+/ftv+3VvIiIiIiLmevrpp00/t23bNs/7N3sd3oxi98CBA6xZs4YffviBw4cP5/nARERERO5rWoc319asWUPXrl1p1qwZkD5/d/78+bnuz+yoNjY2lu7du2cqcg0GA9WrV2fevHmZvhVDRERERMQcX375JZGRkTzzzDOmeb1xcXF88cUXpKWl0bNnT7P7NDvhfeutt/Dy8mLJkiX88ssvbN++nU8//RSj0cj06dPNHoCIiIiIXTIa8+dmZz7//HPmzJnDmDFjTNuKFSvGrFmz+PLLL3PVp9kJ786dO1m6dClFixY1batTpw7Tpk2jW7duuRqEiIiIiAjAiRMnCA0NBch08Vq1atVMXy1sLrMT3itXrmRaEy1D0aJFiY2NzdUgREREROyO5vDmioeHBydPnsyyPSoqKtdTZ80ueMuXL8+aNWuybP/+++8pW7ZsrgYhIiIiIgLQunVrBg8ezJYtWzAajezfv5+lS5cyYMAAnnjiiVz1afaUhl69etG3b19WrlxJ5cqVgfQ1eX///XemTp2aq0GIiIiI2Butw5s7Q4YMYcqUKfTp04cbN27Qrl07ChQoQMeOHXn99ddz1afZBW/Tpk1ZtGgRixYt4o8//sBoNFKhQgU+//xzQkJCcjUIEREREXmw7du3j+DgYJydnRk7dixDhgzh+PHjGAwGypYti5ubW677NqvgTUtLY//+/dSqVYtatWrl+kFFRERE7J4SXrN06NCBokWL0qRJE5o1a0ZoaCj+/v550rdZBa+DgwPdunXjjz/+wNHRMU8GICIiIiLyySefsGXLFrZs2cLSpUvx9PSkQYMGNGvWjAYNGuDp6Znrvs2e0tCmTRsWLlxI9+7d8/x7jkVERETshhJes9SrV4969eoxfPhwTpw4webNm9myZQvDhw/HaDRSp04dmjVrRufOnc3u2+yC9/z582zcuJF58+ZRqlQpnJ2dM+1funSp2YMQEREREclQpkwZunbtSteuXUlKSmLdunV8+OGHvPnmm/lT8Pr4+NCgQQOzH0hERETkgaKE957s3buXzZs389NPPxEdHU2ZMmV4/vnnc9WX2QVvZGRkrh5IREREROR24uPj2bp1K1u2bOGnn37i2rVr1K5dm6effpp33nnnnr7vIccFb//+/Zk1a1ambbNmzaJ///65fnARERERe6V1eM1Tr1493NzcaNu2LW+99RZ16tTB1dU1T/rO8TetbdmyJcu2BQsW5MkgREREROTBFh4ejpubG8uWLeOzzz7jf//7H//880+e9G32lIabGY3GPBmEiIiIiN1JU8JrjoiICAAOHjzIpk2bWL16NZGRkZQuXZqGDRvSoEED6tatm2XBhJy4p4JXy5KJiIiISF4KCAggICCAPn36cPHiRbZu3cqvv/7K+PHjiYuLY/fu3Wb3eU8Fr4iIiIjchj4Jv2cXLlzg3LlznDt3jri4uFz3o4JXRERERGxCSkoKv/76K5s3b2bz5s2cOXMGHx8fGjVqxLRp03jsscdy1W+OC97r16/z+uuv33Xb9OnTczUQEREREbuiVRrM0qdPH7Zv305iYiLlypWjZcuWNG3alJo1a97zNNocF7w1a9bk3Llzd90mIiIiImKu8+fP06tXL5o2bUqlSpXytO8cF7yLFi3K0wcWERERsWdah9c8//vf/yzWd47X4RURERERuR/pojURERERS1DCazOU8IqIiIiIXVPCKyIiImIJSnhthhJeEREREbFrSnhFRERELCEt1dojkP8o4RURERERu6aEV0RERMQCjGmaw2srlPCKiIiIiF1TwisiIiJiCZrDazOU8IqIiIiIXVPBe5/q3akpcds/IGX3x5QrUeSObT+PfI0rv87l4DeRdG5dN9O+/l2a89eat7ny61y2fTaa6v5lLTlsuQdX/j3J6ohezO/6KAt7NObXz9/Ndn7Yqf2/M6qFPwaDgVEt/PmwQwgfdgjhl89mmNrsXf05n/duzUddQlkxsivnj0bn56GIGWJiYmjTpg0PPfQQZcuWZfTo0aTdZl7g+vXrCQ0NxdXVlWrVqrF06dJM+9977z38/Pzw8fGhQYMG7N69Oz8OQXJJ57wdSEvNn5vclVUL3iZNmtCwYUOuXbuWafuOHTto0qQJANHR0bz44ovUqlWLunXrMmDAAM6dO2eN4doER0cHfvhwCFMHd+TGjTv/kT/kUxAAdzdnRr77JZfir/HxxJcJqVoOgGea1mTakM78dexfxry/nPKlfFk5ayBurs4WPw4xj9FoZO20wZw9tIcaz/SgVFAd9qxaSNT3S7K0TbkWD8DTTz9N+KCJNHx1DA1fHUPFR1sAcPTX9fzy2XS8SpSjTue+XDl3iu/f6s/15MR8PSa5O6PRSKdOndi+fTtvvPEGjRo1YsaMGcyZMydL21OnTtGhQwfi4+N566238Pb25uWXX2bXrl0ArFy5kmHDhvHwww8zYcIEjh07xrPPPpvl31+xDTrnxRri4uIYNGgQNWrUoHbt2owaNYqkpKTbtv/hhx9o06YNISEhtGjRgmXLlpn2zZo1i6pVqxIUFGS61apVKz8OI1tWT3iTk5Oz/cc7Y1+PHj2oXbs227dvZ/Xq1Vy4cIHx48fn7yBtiKuzE4W9PKjfNYI9h4/fsW2rx4IBmPu/Tby/ZD2vT12Co6MDLzz1GAAvhocB0GPsAt79/Efe/2I9JR/ypvVjQZY9CDHbub8PEHvsLx4Oe5wabXvQpG8EBVxcid74dZa2yQlXgPQ3lDWah1OlwZNUbdqWYpUDAYjetDJ9f583eeTJrgQ9/hzX4i5wfPe2fDseyZmdO3cSFRVFp06dGDp0KPPnz8fd3Z2FCxdmabts2TKSkpIYO3YsAwYMYOrUqaSlpbFo0SIA033mzZtH//796d27N2fPnmXt2rX5eESSUzrn7YMxNTVfbnll5MiRxMbG8uOPP7J69WoOHTrE1KlTs227b98+hg4dyqBBg9i5cyfjxo1j4sSJ7Ny5E4ArV67Qrl07oqKiTLeMfdZg9YK3X79+LF68mJiYmCz7kpKSGDRoEK+++irOzs74+vrSqlUrjhw5YoWR2oZrSSnUe37iXYtdgCrligNw9GR6Ih599DSAKeF9xK8scVeucub8pcz7/cvl9bDlHsX+cxgAnzKVAHAs4IRX8TLEnTzKjZTkTG2T/0t75s+fz/g2ISzoVp81U/qTeOWiqS9nj4J4FC4KQOHSFQG4cPRQvhyL5Ny+ffsACAgIAMDZ2ZlKlSoRHR2dJXW5tW3VqlUB2LNnj2m/t7c3JUuWBMDf3x9A0xpslM55yW+xsbFs2rSJESNG4OvrS7FixRg4cCArVqwgJSUlS/tLly7x2muv0aRJExwdHalfvz5+fn6ZCl4vL6/8PozbsnrBW7lyZTp06EBERESWfV5eXrRv354CBQpgNBo5evQoX3/9Na1bt7bCSG2D0WgkNTVn6/p5eboB6UUyQPy19P9B+np7AlDEy4OEa///D2fG/iLeBfNsvJI3kuIvAeDk6mba5uTqAUYjyQmXM7XNSHscHR1pN3QK5Wo24PiurWydH/lfX5dxcnW/qR/3TI8htuPixfSCxcPDw7TN09MTo9Fo2ndrW0/P9PO7YMH08zg2Nta0/+Z+bt0vtkXnvJ1IS8ufWx44ePAgBQoUwM/Pz7QtICCAa9euZRtKNmjQgN69e5t+v3HjBufOnaNEiRJAesG7d+9ennjiCWrVqkXnzp1Nb8ytwSaWJevXrx+tWrVi3bp1NG/ePMv+U6dO0aJFC1JTU+nYsSMDBgwwq/8KpR7Cq6Db3RveZzzdXQEIqFQKHy+P2+6vUOohqvuXxcFgAMDZqUD67w4OOBVwNF2oVqnMf+/+vTzs5uK1Mt728bofcUk/VQu7u5iOyaVA+vvVkl5uFLrpOIu92Ieur/Sid5NAFu06jX9oIya1r8c/v2+mZEEnDEABBwdTP8meLgB4uhSwm+fL8N/f+v3OaDQC4ODgkOWYbt2WcSFbxjYHBwfT7waDAaPRaPr51nb28nyBzvkH9Zw/cUnzke9VXFwcnp6epn87AFNCe+sb7OxMmzYNFxcXUx330EMP4eLiwqRJkyhcuDCzZ8+mR48erF27lsKFC1vmIO7AJgpeT09PhgwZQmRkJGFhYVn2lypViv3793Ps2DHGjBnDG2+8wfTp03Pc/8FvInF0tHqYbTHfvDfwjvtnDH0u0+/lSvry25LxABT39TL9nKFts1q0bWa9ieWSlW9MVdYvhIZlPRjQPP3d91KH6zg6OjLhmTq4uLhke78edcsD5ZlXpAhnz56lV61ifPCQL4mJiYz8r58V8Qf4BGhZo4ppm9iGjKQkKSkJV9f0N7AJCQk4OjpSsmTJTK97sWLFAEwfPWZMeShWrBiurq74+vqSkJBg6idjf4kSJUzb7IG9/A3rnDdPry/3WHsI2bOxFRRWrlzJyJEjs903adKk297vTm+KjUYj06ZNY82aNXz66ae4u6d/gjBx4sRM7YYMGcK3337L+vXr6dChQy5Gf29souAFCA8PZ9myZcydO5e6detm2W8wGChfvjxDhw6lXbt2jBo1KsfvEAKeHmGXCe8HY16kZkB5nu43kzMXLuHi7ERxXy+Skq/zb+xlBj7fkueeqMfspev59JtthPiXY+64l1iy5hfeWbSW6UM6E1bTj9a9phF7KYEebRvyavvGDJm2hJ/+OGztw8sT4d07W3sIeeLU9fSl5xb/uJ2rAa24npLMoT//omj5KkT+GM3lc2dwcnXFu2hJ1n0yk6O7trFyyWfsSvbh7+OnOXfuPK6ehZi3O5ZCZf04s2Mzw5dupVCRomz8ZjMAMQVKMHmdfbzug8PsYx56YGD6RUdRUVEkJSWRlJTEkSNHCAwM5OrVqxw6dAg3NzfKli1LcHAwS5YsYe/evTzyyCOm1RmqV69OUlISISEhrFmzhpiYGEqUKGGa2xscHHzHq7DvNzO2HrP2EPKEznmxhPDwcMLDw7Pd9/PPPxMfH09qaiqOjo5AeuoLUKRI9sufpqWlMWLECA4cOMCyZctMb9Kz4+joSIkSJTh//vy9HUQu2UzBCzBu3Dg6d+5suqjil19+YcyYMfzwww8UKJA+1IyP7TJejJyIOWWdJ9cSvDzdGNytFQC+Pulz9VrUD+RKfCKxlxOYNqQzW3Yeovkrb7Po25957ol6tGkYwt/Hz9G1TX1Srt8gcv5qDsWcYdrC7wmr6cegrq1Y98t+nm1Wi79PnOODZRu5fpclz+4XNe3lY66ilXioUjV2b1iFg3cxzh89xPXkJKo0a8euP3by7YRXKBFQk6fHzwefEhw/vJ+uXbtSLuwp/tj4HWlpqQQ93oUTlxKp2Lgth3dsZvGUYZR5pB67v15EoWKlca9S224+FsyYCnC/CwkJoWbNmixZsoRy5cqxZ88eEhMT6dmzJ7///jutWrUiLCyMtWvX0rFjRyZNmsSbb75JXFwcCxcuxMnJie7du2M0GunRowdr1qyhZ8+eNGvWjDlz5lChQgVatGhhN88X2NFH2zrn7YLRxhLeOwkICCAtLY3Dhw+bLn7dt28fBQsWpHz58tneZ/LkyRw9epTFixdnukDNaDTy9ttv89RTT5kuoL1+/TonTpygTJkyFj+W7NjU5/z+/v6Eh4fz7rvvAunpRmJiItOnTycxMZGLFy/y3nvvUatWLZu68i8/eRd0Z8QrbRjxShvKlfAFoE+nZox4pQ1xVzKvpxl7OQGA5JTrRA5sj7urM53emMOhmDMAfL9tHwOmfE7ViiWZ2PdZomNOE95/pt0Uu/amxetTKVG1Bju/nMeZ6F3U7tCLqk3bZmnn3+gp2vQdw40bN1j3yTskJ1yh7vODCGnbA4ByNcJ4rPsw4k4e5bcl7+NTugKth8/CsYBTfh+S5MAXX3xB/fr1mTx5Mtu2bWPMmDG89NJLWdoVL16cr776Cnd3d4YOHcq1a9dYvHixaTWG1q1bM336dKKjoxk3bhz+/v6sWLECZ2etu22rdM5LfvLx8aF169ZERkZy4cIFTp06xTvvvEPHjh1xckr/W3nhhRdYs2YNAH/88QfffPMNs2fPzlKTGQwGTp48ycSJE/n333+5evUq06ZNw9nZmWbNmuX7sQEYjFZ8a9+kSRMiIyMJDQ01bbt8+TItW7bE3d2djRs3Eh0dzVtvvcX+/fspUKAAoaGhjBw50jRfLSecQ7pbYvg2r7p/WX5bMp46ncez59DdlzGzRz1G9rf2EKyijLcbI5v7MXnd4QcywZnxpH3MS8wNg8GAq6srSUlJdpXc5tTg1Q/mR/QP+jkP8EH76tYeQhbXd6zMl8dxCg3Pk37i4+MZP348GzduxMnJiTZt2jBs2DDTG+MmTZrwyiuv0LlzZ0aOHMnXX39t+gQ+Q+3atfn444+5fPkykZGRbNmyhdTUVIKCghg1ahQVK1bMk7Gay6pTGjZu3Jhlm5eXF7/++qvp96pVq2a7yLqIiIiI5J2CBQvecVGAm+u2yZMnM3ny5Nu29fLyYsqUKXk6vnthU3N4RUREROzF/TSH197Z1BxeEREREZG8poRXRERExBKU8NoMJbwiIiIiYteU8IqIiIhYwn/fHSDWp4RXREREROyaEl4RERERCzCmag6vrVDCKyIiIiJ2TQmviIiIiCVolQaboYRXREREROyaEl4RERERS1DCazOU8IqIiIiIXVPCKyIiImIBRq3DazOU8IqIiIiIXVPCKyIiImIJmsNrM5TwioiIiIhdU8IrIiIiYglKeG2GEl4RERERsWtKeEVEREQsQKs02A4lvCIiIiJi15TwioiIiFiC5vDaDCW8IiIiImLXlPCKiIiIWIISXpuhgldERETEAoypKnhthaY0iIiIiIhdU8IrIiIiYglalsxmKOEVEREREbumhFdERETEEnTRms1QwisiIiIidk0Jr4iIiIgFGJXw2gwlvCIiIiJi15TwioiIiFiAUas02AwlvCIiIiJi15TwioiIiFiAMVUJr61QwisiIiIidk0Jr4iIiIgFKOG1HUp4RURERMSuKeEVERERsQCt0mA7lPCKiIiIiF1TwisiIiJiAZrDazuU8IqIiIiIXVPCKyIiImIBSnhthxJeEREREbFrSnhFRERELCAtNdXaQ5D/KOEVEREREbumhFdERETEArQOr+1QwisiIiIidk0Jr4iIiIgFaJUG26GEV0RERETsmhJeEREREQtQwms7lPCKiIiIiF1TwisiIiJiAVqlwXYo4RURERERu6aCV0RERMQC0lLT8uWWV+Li4hg0aBA1atSgdu3ajBo1iqSkpGzb/vrrr/j5+REUFJTptm/fPgCMRiPvvPMO9evX55FHHuHFF1/kxIkTeTZWc6ngFRERERFGjhxJbGwsP/74I6tXr+bQoUNMnTo127bx8fGUL1+eqKioTLfg4GAAPv30U5YvX878+fP5+eefKVu2LH369MFoNObnIZmo4BURERGxAGNqWr7c8kJsbCybNm1ixIgR+Pr6UqxYMQYOHMiKFStISUnJ0v7y5ct4eXndtr8vv/ySl19+mapVq+Lp6cmwYcM4evQou3fvzpPxmksFr4iIiMgD7uDBgxQoUAA/Pz/TtoCAAK5du0ZMTEyW9leuXOHy5cs8//zz1KxZkyeeeIKVK1cCkJyczN9//01gYKCpvYeHB2XLluXAgQMWP5bsPBCrNPQY2d/aQ7CKMt5uAIR370zNS4lWHo11zHjS7+6N7JDBYABgcFg5q318ZE2DVx+29hCspoy3GyOb+zFj6zFOPIDnvc75B/Oct1X30zq8cXFxeHp64uDw/1loRoJ78eLFLO3d3d0pWrQoAwYMIDAwkA0bNvDGG29QtGhRKlWqhNFozJIAe3l5ZdtXflDCKyIiIvIAWLlyJQEBAdneUlNTb3u/jDdUN+vUqROLFi2iVq1auLq68sQTT9C8eXOWL19+xzFk11d+eCASXhEREZH8Zmvr8IaHhxMeHp7tvp9//pn4+HhSU1NxdHQE0lNfgCJFiuSo/9KlS7N//358fHxwcHDg0qVLmfbHxcXluK+8poRXRERE5AEXEBBAWloahw///5Swffv2UbBgQcqXL5+l/f/+9z/WrFmTadvRo0cpU6YMzs7OVKlSJdN83UuXLnH8+HGCgoIsdgx3ooJXRERExALup1UafHx8aN26NZGRkVy4cIFTp07xzjvv0LFjR5ycnAB44YUXTEVucnIyERER7N+/n+vXr/Pdd9+xdetWOnfuDEDnzp2ZP38+hw4dIj4+noiICAIDA03LluU3TWkQERERESZMmMD48eNp3rw5Tk5OtGnThgEDBpj2nzhxgsuXLwPw/PPPEx8fT//+/YmLi6NChQrMnj2batWqAelzfM+fP0/37t25evUqoaGhzJo1yyrHBSp4RURERCziflqlAaBgwYJMnz79tvs3btxo+tlgMNC7d2969+592/b9+vWjX79+eTrG3NKUBhERERGxa0p4RURERCwgzcZWaXiQKeEVEREREbumhFdERETEAu63Obz2TAmviIiIiNg1JbwiIiIiFmC8w9f1Sv5SwisiIiIidk0Jr4iIiIgFGLVKg81QwisiIiIidk0Jr4iIiIgFaJUG26GEV0RERETsmhJeEREREQtQwms7lPCKiIiIiF1TwisiIiJiAWlKeG2GEl4RERERsWtKeEVEREQsQOvw2g4lvCIiIiJi15TwioiIiFiAVmmwHUp4RURERMSuKeEVERERsQBjqtHaQ5D/KOEVEREREbumhFdERETEArQOr+1QwisiIiIidk0Jr4iIiIgFGNM0h9dWKOEVEREREbumhFdERETEAtK0SoPNUMIrIiIiInZNCa+IiIiIBeib1myHEl4RERERsWtKeEVEREQsQN+0ZjuU8IqIiIiIXVPCKyIiImIBWqXBdijhFRERERG7poRXRERExAK0SoPtUMIrIiIiInZNCa+IiIiIBaSlaQ6vrVDCKyIiIiJ2TQmviIiIiAVoHV7boYRXREREROyaEl4RERERC0jTKg02QwmviIiIiNg1JbwiIiIiFqA5vLZDCa+IiIiI2DUlvCIiIiIWoITXdijhFRERERG7poRXRERExAK0SoPtUMIrIiIiInZNCa+IiIiIBRjTNIfXVijhFRERERG7poRXRERExALStEqDzVDCe5+58u9JVkf0Yn7XR1nYozG/fv4uxrSsk+JP7f+dUS38MRgMjGrhz4cdQviwQwi/fDbD1Gbv6s/5vHdrPuoSyoqRXTl/NDo/D0XMFBMTQ5s2bXjooYcoW7Yso0ePJi2b1x5g/fr1VK9eHW9vbwIDA1m6dGmm/e+99x5+fn74+PjQoEEDdu/enR+HILlgzjn/YYeQLOe9zvn7l875+58xNS1fbnJ3KnjvI0ajkbXTBnP20B5qPNODUkF12LNqIVHfL8nSNuVaPABPP/004YMm0vDVMTR8dQwVH20BwNFf1/PLZ9PxKlGOOp37cuXcKb5/qz/XkxPz9ZgkZ4xGI506dWL79u288cYbNGrUiBkzZjBnzpwsbU+dOkX79u2Jj48nIiICb29vXn75ZXbt2gXAypUrGTZsGA8//DATJkzg2LFjPPvss1y7di2/D0vuIjfnfNV6Tfnoo49M573O+fuTznmRvGXVgrdJkyY0bNgwy0m3Y8cOmjRpkqX95MmT8fPzy6/h2Zxzfx8g9thfPBz2ODXa9qBJ3wgKuLgSvfHrLG2TE64A6c9xjebhVGnwJFWbtqVY5UAAojetTN/f500eebIrQY8/x7W4CxzfvS3fjkdybufOnURFRdGpUyeGDh3K/PnzcXd3Z+HChVnaLlu2jKSkJCZOnEjfvn2ZOnUqaWlpLFq0CMB0n3nz5tG/f3969+7N2bNnWbt2bT4ekeREbs75itVDeeGFFwhp+rTO+fuYznn7YEw15sstr8TFxTFo0CBq1KhB7dq1GTVqFElJSdm2HT16NEFBQZlu1apVo2vXrgDMmjWLqlWrZtpfq1atPBuruaye8CYnJ2f7jvVW0dHRrFy50vIDsmGx/xwGwKdMJQAcCzjhVbwMcSePciMlOVPb5P/Snvnz5zO+TQgLutVnzZT+JF65aOrL2aMgHoWLAlC4dEUALhw9lC/HIubZt28fAAEBAQA4OztTqVIloqOjs/xjlNG2WrVqAPj7+wOwZ88e035vb29KliyZab8+4rQ9uTnnd/7wFe7u7kx4uobO+fuYznmxhpEjRxIbG8uPP/7I6tWrOXToEFOnTs22bUREBFFRUabb3r17CQwMpE2bNgBcuXKFdu3aZWqzc+fO/DycTKxe8Pbr14/FixcTExNz2zZpaWmMGzeOl156KR9HZnuS4i8B4OTqZtrm5OoBRiPJCZcztc1IexwdHWk3dArlajbg+K6tbJ0f+V9fl3Fydb+pH/dMjyG25eLF9KLFw8PDtM3T0xOj0Wjad2tbT09PAAoWLAhAbGysaf/N/dy6X2xHbs55BwdHPvvsM6rWa6xz/j6mc94+pKUa8+WWF2JjY9m0aRMjRozA19eXYsWKMXDgQFasWEFKSspd779s2TKMRiPt27cH0gteLy+vPBlbXrD6Kg2VK1emQ4cOREREsGDBgmzbLF26FFdXV9q0acPMmTPNfgxfD2fcnBzvcaTWd8Ql/eUq7O5CGe/0/wG6FEh/z1LSy41C3v//P8ViL/ah6yu96N0kkEW7TuMf2ohJ7evxz++bKVnQCQNQwMHB1E+ypwsAni4FTNvsgcFgsPYQ8oTRmP4PmoODQ5ZjunVbxkUtBoMBg8GAg4NDpt+NRqPp54ztt+v7fmUvf8PmnvOtOvegbLHCdA6rwsWiQQxoXUPn/H1K57x5Mp4vyb2DBw9SoECBTFNHAwICuHbtGjExMXecUpqQkMCsWbOYM2eO6W/qypUrnDlzhieeeIJ///2Xhx9+mBEjRhAcHGzxY8mO1QteSE95W7Vqxbp162jevHmmfRcuXGD27NmmuUi5MaF1VRzs4KT2janK+oXQsKwHA5qn/+EtdbiOo6MjE56pg4uLS7b361G3PFCeeUWKcPbsWXrVKsYHD/mSmJjIyP/6WRF/gE+AljWqmLaJ7ShRogQASUlJuLq6Aun/wDg6OlKyZMlMr32xYsUAiI+Px8XFhatXr5q2u7q64uvrS0JCgqmfjI9HS5QoYdp2v7OXv+HcnvMAfZoGEqFz/r6lc948iYm2efFldiuq2Kq4uDg8PT1Nb5gAU0J766cKt1q0aBHBwcGEhISYtj300EO4uLgwadIkChcuzOzZs+nRowdr166lcOHCljmIO7CJgtfT05MhQ4YQGRlJWFhYpn2RkZF06NCBihUrcvLkyVz1P+77aLtIeE9dLwLA4h+3czWgFddTkjn0518ULV+FyB+juXzuDE6urngXLcm6T2ZydNc2Vi75jF3JPvx9/DTnzp3H1bMQ83bHUqisH2d2bGb40q0UKlKUjd9sBiCmQAkmrztsxaPMW4PDyll7CHkiMDD9wqOoqCiSkpJISkriyJEjBAYGcvXqVQ4dOoSbmxtly5YlODiYJUuWcPDgQfz9/U1XalevXp2kpCRCQkJYs2YNMTExlChRwjTPLzg4+LYXJ9xvZmw9Zu0h5Alzz/k/f/+Jl0e/zYQXnuCdNTt1zt/HdM6LJaxcuZKRI0dmu2/SpEm3vd+dPgm4fv06ixcvJjIyMtP2iRMnZvp9yJAhfPvtt6xfv54OHTqYMeq8YRMFL0B4eDjLli1j7ty51K1bF4BffvmF/fv3M3ny5Hvq+8LVu889uS8UrcRDlaqxe8MqHLyLcf7oIa4nJ1GlWTt2/bGTbye8QomAmjw9fj74lOD44f107dqVcmFP8cfG70hLSyXo8S6cuJRIxcZtObxjM4unDKPMI/XY/fUiChUrjXuV2py4ZJvvlHPDXj7mCgkJoWbNmixZsoRy5cqxZ88eEhMT6dmzJ7///jutWrUiLCyMtWvX0rFjRyZNmsTYsWM5deoUn3/+OU5OTnTv3h2j0UiPHj1Ys2YNPXv2pFmzZsyZM4cKFSrQokULu3m+7OZv2Mxz/vSRg3w4bhA+cX8x86NPdc7fx3TO2wdb++KJ8PBwwsPDs933888/Ex8fT2pqKo6O6SFhXFwcAEWKFLltnzt37uT69evUr1//jo/t6OhIiRIlOH/+fO4Gf4+sftHazcaNG8fChQs5fvw4AKtWreLs2bM0aNCA0NBQ2rZtC0BoaCjfffedNYdqNS1en0qJqjXY+eU8zkTvonaHXlRt2jZLO/9GT9Gm7xhu3LjBuk/eITnhCnWfH0RI2x4AlKsRxmPdhxF38ii/LXkfn9IVaD18Fo4FnPL7kCSHvvjiC+rXr8/kyZPZtm0bY8aMyfZCzuLFi7N8+XLc3d0ZNWoUiYmJLF682HRlduvWrZk+fTrR0dGMGzcOf39/VqxYgbOzc34fkuSAOef8Y92Hk5Z6g5EjR3LtymWd8/c5nfOSnwICAkhLS+Pw4f//xGffvn0ULFiQ8uXL3/Z+W7duJTQ0NNNUCKPRyFtvvUV09P9/uc3169c5ceIEZcqUscj478ZgtOLbuyZNmhAZGUloaKhp24QJE1i3bh3Ozs58/fXXmeblnD17lo4dO7Jlyxa8vLxwc8vZhRa9vtyT10O/L5TxdmNkcz8mrztsVwmOOWY8+WDOTTQYDLi6upKUlPRAJjiDV9vPR/TmetDPe53zD+Y5D+S4JshPP1QKuXujPNDq77xZYm7w4MGcP3+ed955h+TkZF599VUaNmzIG2+8AcALL7xAx44defzxx033eemllwgJCaF///6Z+urXrx+xsbG88847eHp6MmvWLL7//nt++OEH3N3dyW82lfACDBw4kBs3bgDpk6WLFy9uuvn6+gLp72Zt8Q9bRERE5H41YcIEihYtSvPmzXnmmWcIDQ1lwIABpv0nTpzg8uXMSyKeP38eb2/vLH1FRERQtmxZwsPDady4MUeOHGHhwoVWKXbBynN4N27cmGWbl5cXv/76a7btS5cunSlqFxEREbFVxtT7Z5UGSF+jefr06bfdn13dtnr16mzbenl5MWXKlDwb272yuYRXRERERCQv2cwqDSIiIiL2xNZWaXiQKeEVEREREbumhFdERETEAoxKeG2GEl4RERERsWtKeEVEREQsIO0BXRPZFinhFRERERG7poRXRERExAJSlfDaDCW8IiIiImLXlPCKiIiIWIAWabAdSnhFRERExK4p4RURERGxAM3htR1KeEVERETErinhFREREbEAzeG1HUp4RURERMSuKeEVERERsQDN4bUdSnhFRERExK4p4RURERGxAM3htR1KeEVERETErinhFREREbEAzeG1HUp4RURERMSuKeEVERERsQDN4bUdSnhFRERExK4p4RURERGxACW8tkMJr4iIiIjYNSW8IiIiIhagVRpshxJeEREREbFrSnhFRERELEBzeG2HEl4RERERsWtKeEVEREQsQHN4bYcSXhERERGxa0p4RURERCxAc3hthxJeEREREbFrSnhFRERELEBzeG2HEl4RERERsWtKeEVEREQsQHN4bYcSXhERERGxa0p4RURERCxAc3hthxJeEREREbFrSnhFRERELCDN2gMQEyW8IiIiImLXlPCKiIiIWIDm8NoOJbwiIiIiYteU8IqIiIhYgNbhtR1KeEVERETErinhFREREbEAzeG1HUp4RURERMSuKeEVERERsQDN4bUdSnhFRERExK4p4RURERGxAM3htR1KeEVERETErinhFREREbEAzeG1HUp4RURERASAqKgomjdvTocOHe7a9rvvvqNly5YEBQXx5JNP8vPPP5v2GY1G3nnnHerXr88jjzzCiy++yIkTJyw59DtSwSsiIiJiAalGY77c8sqqVavo168f5cqVu2vb/fv3M2zYMAYMGMDvv//Oiy++SJ8+fThz5gwAn376KcuXL2f+/Pn8/PPPlC1blj59+mC00rxmFbwiIiIiQnJyMsuWLeORRx65a9vly5fToEEDHn/8cVxdXWnXrh1+fn588803AHz55Ze8/PLLVK1aFU9PT4YNG8bRo0fZvXu3pQ8jWyp4RURERCwg1Zg/t7zSvn17ihUrlqO2Bw8epFq1apm2Va1alQMHDpCcnMzff/9NYGCgaZ+Hhwdly5blwIEDeTdgM6jgFRERERGzxMXF4e3tnWmbl5cXFy9e5NKlSxiNRry8vLLdbw0PxCoNH7Svbu0hWNXI5n7WHoJYiaurq7WHYBUP+jkPOu8fVA/qOW+rPjT+Y+0hZLJy5UpGjhyZ7b7JkycTHh5+z49hMBjuab+lPBAFr4iIiMiDLjw8PE+KWoDChQsTFxeXaVtcXByFCxfGx8cHBwcHLl26lGV/kSJF8uTxzaUpDSIiIiJilqCgoCzzcaOioggODsbZ2ZkqVapk2n/p0iWOHz9OUFBQfg8VUMErIiIiIjnwwgsvsGbNGiD9Areff/6ZNWvWkJSUxKJFizh+/LgpQe7cuTPz58/n0KFDxMfHExERQWBgIMHBwVYZu6Y0iIiIiAgtW7bk9OnTpKamkpaWZkpjf/jhB0qVKsWJEye4fPkyAFWqVGHatGlMnz6dYcOGUalSJebOnYuvry8AnTp14vz583Tv3p2rV68SGhrKrFmzrHZsBqO1VgAWEREREckHmtIgIiIiInZNBa+IiIiI2DUVvCIiIiJi11TwioiIiIhdU8FrB5o0aULDhg25du1apu07duygSZMmpt+3bt3Ko48+yqBBg/J7iGIBOXndT548Sa9evahTpw716tVj6NChpits5f6Vk9c+OjqaF198kVq1alG3bl0GDBjAuXPnrDFcySM5/bc+w+TJk/Hz0zfuiYAKXruRnJzMnDlzbrv/o48+IiIignLlyuXjqMTS7va69+rVC29vbzZt2sSqVauIiYnh7bffzscRiqXc6bVPTk6mR48e1K5dm+3bt7N69WouXLjA+PHj83eQkufuds5niI6OZuXKlZYfkMh9QgWvnejXrx+LFy8mJiYm2/0uLi589dVXKnjtzJ1e9/j4eAIDAxkyZAgeHh489NBDhIeHs3PnTiuMVPLanV77pKQkBg0axKuvvoqzszO+vr60atWKI0eOWGGkkpfu9m89QFpaGuPGjeOll17Kx5GJ2DYVvHaicuXKdOjQgYiIiGz3d+vWjYIFC+bzqMTS7vS6FyxYkMjIyEzfW37q1ClKlCiRn0MUC7nTa+/l5UX79u0pUKAARqORo0eP8vXXX9O6dWsrjFTy0t3+rQdYunQprq6utGnTJh9HJmLbVPDakX79+nH48GHWrVtn7aFIPsrp6x4VFcXixYuV+tiRu732p06dIjAwkMcff5ygoCAGDBiQzyMUS7jT637hwgVmz56t6Ssit1DBa0c8PT0ZMmQIkZGRJCUlWXs4kk9y8rr/8ccf9OjRg6FDh9KwYcN8HqFYyt1e+1KlSrF//35++OEHjh49yhtvvGGFUUpeu9PrHhkZSYcOHahYsaKVRidim1Tw2pnw8HCKFSvG3LlzrT0UyUd3et03bdrEq6++yrhx4+jSpYsVRieWdLdz3mAwUL58eYYOHcrq1au5ePFiPo9QLCG71/2XX35h//79vPbaa1YcmYhtUsFrh8aNG8fChQs5fvy4tYci+Si7133Xrl0MGzaMWbNm8cQTT1hxdGJJt772v/zyC82aNePGjRumNmlpaQA4OjpaZYyS92593VetWsXZs2dp0KABoaGhtG3bFoDQ0FC+++47aw5VxOoKWHsAkvf8/f0JDw/n3XffxdnZ2drDkXxy6+t+48YNRo8ezYABA3j00UetPTyxoFtf+8DAQBITE5k+fTr9+/cnMTGR9957j1q1auHl5WXt4UoeufV1Hz58eKZ52mfPnqVjx4588803et3lgaeE104NHDgwU7oTFBREUFAQ33zzDT/88IPpd7EvN7/ue/bs4e+//2bKlCmm1zvjdurUKSuPVPLaza99wYIFmT9/PtHR0YSFhfH444/j4eHBjBkzrDxKyWs3v+5eXl4UL17cdPP19QWgePHiuLm5WXOYIlZnMBqNRmsPQkRERETEUpTwioiIiIhdU8ErIiIiInZNBa+IiIiI2DUVvCIiIiJi11TwioiIiIhdU8ErIiIiInZNBa+IiIiI2DUVvCIiIiJi11TwioiIiIhdU8ErInli9OjRmb6+2M/Pj2rVqmXaZq4VK1bg5+fHiBEjsuw7efIkfn5+nDx5Mi+GLyIidkwFr4jkiYiICKKiokw3SC+Cb91mLg8PD9asWcPPP/+cl8O9K6PRSGpqar4+poiIWIYKXhHJN3/88QddunShTp06PPbYY/Tu3Zvjx4/f8T7e3t706dOHMWPGcO3atTu2Xbp0KS1btiQ4OJhmzZrx1ltvkZiYCPx/IvzTTz+Z2icnJ+Pn58eKFSsAGD58OK+++iqjR4/mkUceMRXpGzZs4Nlnn6VmzZo0atSIN954gwsXLgBw/fp1/Pz8WLlyJYMHD6ZmzZqEhoYyceJEjEZjrp8rERHJOyp4RSRfHD9+nBdeeIHHHnuMTZs28e233+Lq6spzzz1310K2R48eeHt7M2PGjNu2+eGHH5gxYwYRERHs3r2buXPnsm3bNiIjI80a5759+yhbtix//PEHjzzyCDt37qRPnz506dKF7du3s2zZMs6ePUv37t0xGo04OTkB8MEHH9C+fXt+//13xo8fz+eff56puBYREetRwSsi+WLJkiUUL16c1157DQ8PD3x8fBg2bBjnz5+/a2Ho6OjI5MmTWbZsGbt27cq2zccff8yzzz5L7dq1cXR0pFKlSvTp04fly5dz/fr1HI8zNTWVHj164OTkhMFg4LPPPqNOnTq0bdsWFxcXihUrxqBBgzh8+DD79+833a9hw4bUq1cPBwcHWrduTcGCBfnzzz9z/LgiImI5Baw9ABF5MBw/fpxKlSphMBhM24oVK4a7u/tdpzUA+Pv70717d0aNGsU333yTZf/Ro0c5cOAAn3/+eabtRqORM2fO4OCQs/f3JUuWxNHRMdO4a9SokalN+fLlTfsyLsYrV65cpjYuLi4kJSXl6DFFRMSylPCKSL4wGo2Zit3c6NOnDwaDgdmzZ2fZ5+DgwMCBAzNdJBcVFcXBgwcpW7Zstv2lpaVl2ebs7Jzjcd+8PacFtYiI5D/9Cy0i+aJChQocOXIk07ZTp05x7do1KlasmKM+nJ2diYiIYMGCBRw6dChL/wcOHMi07fLly1y+fBkAV1dXgEzTG06fPp2jcf/111+ZtmUcR4UKFXI0bhERsS4VvCKSLzp37sy///7L3LlzSUxM5MKFC0yaNIlSpUoRFhaW435q1KhBp06dmDhxYqbtL730EuvWrePbb78lJSWFf//9l0GDBvH6668DUKRIEXx8fNi8eTMAKSkpzJ8/P9P0hex069aN33//nZUrV5KSksLJkyeZMWMG1atXp2rVquY9CSIiYhUqeEUkX5QuXZoPP/yQdevWERoayjPPPIOLiwtffPEFLi4uZvU1ePDgLIVqq1atGDlyJO+//z41a9YkPDycYsWKMX36dCB9+sHkyZP57bffaNy4MZ07d+bJJ5/E1dX1juvt1qhRg6lTpzJ//nxq1qxJ165dqVKlCvPmzTP/SRAREaswGLVQpIiIiIjYMSW8IiIiImLXVPCKiIiIiF1TwSsiIiIidk0Fr4iIiIjYNRW8IiIiImLXVPCKiIiIiF1TwSsiIiIidk0Fr4iIiIjYNRW8IiIiImLXVPCKiIiIiF1TwSsiIiIidk0Fr4iIiIjYtf8DGSV6MUaeorYAAAAASUVORK5CYII=", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化函数\n", "def visualize_patterns(patterns, corrupted_input, title=\"Pattern Visualization\"):\n", "    \"\"\"可视化模式\"\"\"\n", "    fig, axes = plt.subplots(1, len(patterns) + 1, figsize=(15, 3))\n", "    \n", "    # 绘制记忆的模式\n", "    for i, pattern in enumerate(patterns):\n", "        pattern_array = np.array(pattern).reshape(1, -1)\n", "        im = axes[i].imshow(pattern_array, cmap='RdBu', vmin=-1, vmax=1, aspect='auto')\n", "        axes[i].set_title(f'Pattern {i+1}')\n", "        axes[i].set_xticks(range(len(pattern)))\n", "        axes[i].set_xticklabels([f'N{j+1}' for j in range(len(pattern))])\n", "        axes[i].set_yticks([])\n", "        \n", "        # 在每个格子中显示数值\n", "        for j, val in enumerate(pattern):\n", "            axes[i].text(j, 0, str(val), ha='center', va='center', \n", "                        color='white' if abs(val) > 0.5 else 'black', fontweight='bold')\n", "    \n", "    # 绘制损坏的输入\n", "    corrupted_array = np.array(corrupted_input).reshape(1, -1)\n", "    im = axes[-1].imshow(corrupted_array, cmap='RdBu', vmin=-1, vmax=1, aspect='auto')\n", "    axes[-1].set_title('Corrupted Input')\n", "    axes[-1].set_xticks(range(len(corrupted_input)))\n", "    axes[-1].set_xticklabels([f'N{j+1}' for j in range(len(corrupted_input))])\n", "    axes[-1].set_yticks([])\n", "    \n", "    # 在每个格子中显示数值\n", "    for j, val in enumerate(corrupted_input):\n", "        axes[-1].text(j, 0, str(val), ha='center', va='center', \n", "                     color='white' if abs(val) > 0.5 else 'black', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle(title, y=1.05, fontsize=14, fontweight='bold')\n", "    \n", "    # 添加颜色条\n", "    cbar = plt.colorbar(im, ax=axes, orientation='horizontal', \n", "                       fraction=0.1, pad=0.2, aspect=50)\n", "    cbar.set_label('Neuron State')\n", "    \n", "    plt.show()\n", "\n", "def visualize_weights(weights, title=\"Weight Matrix\"):\n", "    \"\"\"可视化权重矩阵\"\"\"\n", "    fig, ax = plt.subplots(figsize=(8, 6))\n", "    \n", "    im = ax.imshow(weights, cmap='RdBu', vmin=-np.max(np.abs(weights)), \n", "                   vmax=np.max(np.abs(weights)))\n", "    \n", "    # 添加数值标签\n", "    for i in range(weights.shape[0]):\n", "        for j in range(weights.shape[1]):\n", "            text = ax.text(j, i, f'{weights[i, j]:.2f}', \n", "                          ha=\"center\", va=\"center\", \n", "                          color=\"white\" if abs(weights[i, j]) > np.max(np.abs(weights))*0.5 else \"black\",\n", "                          fontweight='bold')\n", "    \n", "    ax.set_xticks(range(weights.shape[1]))\n", "    ax.set_yticks(range(weights.shape[0]))\n", "    ax.set_xticklabels([f'N{i+1}' for i in range(weights.shape[1])])\n", "    ax.set_yticklabels([f'N{i+1}' for i in range(weights.shape[0])])\n", "    \n", "    plt.title(title, fontweight='bold')\n", "    plt.xlabel('To Neuron')\n", "    plt.ylabel('From Neuron')\n", "    \n", "    # 添加颜色条\n", "    cbar = plt.colorbar(im)\n", "    cbar.set_label('Weight Value')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 可视化初始设置\n", "visualize_patterns(patterns, corrupted_input, \"Hopfield Network: Stored Patterns and Input\")\n", "visualize_weights(hopfield.weights, \"Hopfield Network: Weight Matrix\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "使用异步更新进行模式恢复\n", "============================================================\n", "Converged after 2 iterations\n", "初始输入: [1, -1, -1, 1]\n", "最终恢复: [ 1. -1.  1.  1.]\n", "总迭代次数: 2\n", "初始能量: 1.0000\n", "最终能量: -1.0000\n", "\n", "与存储模式的相似度:\n", "Pattern 1: 相关性 = 1.000, 汉明距离 = 0\n", "Pattern 2: 相关性 = 0.000, 汉明距离 = 2\n", "Pattern 3: 相关性 = 0.500, 汉明距离 = 1\n", "Pattern 4: 相关性 = -0.500, 汉明距离 = 3\n", "\n", "✓ 成功恢复到 Pattern 1!\n", "\n", "异步更新状态序列 (前10步):\n", "Step 0: [ 1. -1. -1.  1.] (Energy: 1.0000)\n", "Step 1: [ 1. -1.  1.  1.] (Energy: -1.0000)\n", "Step 2: [ 1. -1.  1.  1.] (Energy: -1.0000)\n"]}], "source": ["# 执行模式恢复（异步更新）\n", "print(\"=\" * 60)\n", "print(\"使用异步更新进行模式恢复\")\n", "print(\"=\" * 60)\n", "\n", "np.random.seed(42)  # 设置相同的随机种子\n", "final_state_async, state_history_async, energy_history_async = hopfield.recall(\n", "    corrupted_input, max_iterations=50, async_update=True\n", ")\n", "\n", "print(f\"初始输入: {corrupted_input}\")\n", "print(f\"最终恢复: {final_state_async}\")\n", "print(f\"总迭代次数: {len(state_history_async) - 1}\")\n", "print(f\"初始能量: {energy_history_async[0]:.4f}\")\n", "print(f\"最终能量: {energy_history_async[-1]:.4f}\")\n", "\n", "# 计算与每个存储模式的相似度\n", "print(\"\\n与存储模式的相似度:\")\n", "for i, pattern in enumerate(patterns):\n", "    similarity = np.dot(final_state_async, pattern) / len(pattern)\n", "    hamming_distance = np.sum(final_state_async != np.array(pattern))\n", "    print(f\"Pattern {i+1}: 相关性 = {similarity:.3f}, 汉明距离 = {hamming_distance}\")\n", "\n", "# 检查是否恢复到完全匹配的模式\n", "for i, pattern in enumerate(patterns):\n", "    if np.array_equal(final_state_async, pattern):\n", "        print(f\"\\n✓ 成功恢复到 Pattern {i+1}!\")\n", "        break\n", "else:\n", "    print(f\"\\n× 没有恢复到任何存储的模式\")\n", "\n", "# 显示前10步的状态变化\n", "print(\"\\n异步更新状态序列 (前10步):\")\n", "for i, state in enumerate(state_history_async[:11]):\n", "    print(f\"Step {i}: {state} (Energy: {energy_history_async[i]:.4f})\")\n"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}