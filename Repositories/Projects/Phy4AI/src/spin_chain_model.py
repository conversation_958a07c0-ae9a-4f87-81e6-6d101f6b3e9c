import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from itertools import product
import netket as nk

class SpinChain:
    """一维周期自旋链模型，自旋用0和1表示，相邻自旋之间有随机相互作用"""
    
    def __init__(self, n_spins, seed=None, random_interactions=True, disorder_type='uniform'):
        """
        初始化自旋链
        
        参数:
        n_spins: 自旋链的长度
        seed: 随机数种子
        random_interactions: 是否使用随机相互作用，如果为False则使用均匀的相互作用
        disorder_type: 无序类型，可选 'uniform'(均匀分布), 'strong'(强无序分布)
        """
        self.n_spins = n_spins
        
        # 初始化随机数发生器
        self.rng = np.random.RandomState(seed)
        
        # 初始化自旋状态（随机）
        self.spins = self.rng.randint(0, 2, size=n_spins)
        
        # 初始化相互作用强度
        if random_interactions:
            if disorder_type == 'uniform':
                # 生成均匀分布的正相互作用（只考虑铁磁耦合）
                self.interactions = self.rng.uniform(0.1, 1.0, size=n_spins)
            elif disorder_type == 'strong':
                # 生成强无序分布的正相互作用（对数正态分布）
                mu, sigma = 0, 2.0  # 均值和标准差
                self.interactions = np.exp(self.rng.normal(mu, sigma, size=n_spins))
                # 归一化，使相互作用平均值接近1
                self.interactions = self.interactions / np.mean(self.interactions)
            else:
                raise ValueError(f"不支持的无序类型: {disorder_type}")
        else:
            # 生成均匀的正相互作用（铁磁）
            self.interactions = np.ones(n_spins)
        
        # 创建图表示
        self._create_graph()
    
    def _create_graph(self):
        """创建表示自旋链的图"""
        self.graph = nx.Graph()
        
        # 添加节点
        for i in range(self.n_spins):
            self.graph.add_node(i, spin=self.spins[i])
        
        # 添加边（相互作用）
        for i in range(self.n_spins):
            j = (i + 1) % self.n_spins  # 周期边界条件
            self.graph.add_edge(i, j, weight=self.interactions[i])
    
    def update_graph(self):
        """更新图中的自旋状态"""
        for i in range(self.n_spins):
            if i < len(self.spins):  # 确保索引有效
                self.graph.nodes[i]['spin'] = self.spins[i]
    
    def calculate_energy(self):
        """计算系统总能量"""
        energy = 0
        for i in range(self.n_spins):
            j = (i + 1) % self.n_spins
            # 相邻自旋相互作用能量
            # 将自旋值0,1转换为-1,+1进行计算
            s_i = 2*self.spins[i] - 1
            s_j = 2*self.spins[j] - 1
            # J_ij * s_i * s_j 计算能量贡献
            energy -= self.interactions[i] * s_i * s_j
        return energy
    
    def exact_diagonalization(self):
        """
        使用NetKet的Lanczos精确对角化方法找到基态
        
        返回:
        ground_state: 基态自旋构型
        ground_energy: 基态能量
        """
        # 对于较大的系统，精确对角化计算量很大，这里设置一个上限
        if self.n_spins > 20:
            print(f"警告: 系统尺寸 {self.n_spins} 太大，精确对角化计算量过大")
            return None, None
        
        # 创建一维超立方体图，带周期边界条件
        g = nk.graph.Hypercube(length=self.n_spins, n_dim=1, pbc=True)
        
        # 定义Hilbert空间：自旋1/2
        hi = nk.hilbert.Spin(s=1/2, N=g.n_nodes)
        
        # 创建自定义哈密顿量
        ha = nk.operator.LocalOperator(hi)
        
        # 添加相互作用项
        for i in range(self.n_spins):
            j = (i + 1) % self.n_spins  # 周期边界条件
            # 添加 -J_ij * σ^z_i * σ^z_j 相互作用
            # 注意：NetKet中的泡利矩阵与自旋1/2算符有一个因子2的差别
            ha += -self.interactions[i] * nk.operator.spin.sigmaz(hi, i) * nk.operator.spin.sigmaz(hi, j)
        
        # 使用Lanczos算法计算基态能量
        evals = nk.exact.lanczos_ed(ha, k=1)  # 只需要计算最低能量
        ground_energy = evals[0]
        
        # 获取基态波函数
        _, ground_state_vector = nk.exact.lanczos_ed(ha, k=1, compute_eigenvectors=True)
        ground_state_vector = ground_state_vector[:, 0]  # 取第一个特征向量
        
        # 找到最大概率振幅对应的基态构型
        max_prob_idx = np.argmax(np.abs(ground_state_vector)**2)
        
        # 将索引转换为自旋构型（二进制表示）
        binary_repr = format(max_prob_idx, f'0{self.n_spins}b')
        ground_state = np.array([int(bit) for bit in binary_repr])
        
        # 验证能量
        original_spins = self.spins.copy()
        self.spins = ground_state
        calculated_energy = self.calculate_energy()
        self.spins = original_spins
        
        print(f"Lanczos基态能量: {ground_energy:.6f}")
        print(f"从基态构型计算的能量: {calculated_energy:.6f}")
        
        return ground_state, ground_energy
    
    def exact_diagonalization_brute_force(self):
        """
        使用暴力枚举方法找到基态（用于与Lanczos方法比较或作为备用）
        
        返回:
        ground_state: 基态自旋构型
        ground_energy: 基态能量
        """
        # 对于较大的系统，枚举计算量很大，这里设置一个上限
        if self.n_spins > 16:
            print(f"警告: 系统尺寸 {self.n_spins} 太大，暴力枚举计算量过大")
            return None, None
        
        # 枚举所有可能的自旋构型
        all_states = list(product([0, 1], repeat=self.n_spins))
        
        # 计算每个构型的能量
        energies = []
        for state in all_states:
            # 保存当前自旋状态
            original_spins = self.spins.copy()
            
            # 设置新构型
            self.spins = np.array(state)
            
            # 计算能量
            energy = self.calculate_energy()
            energies.append(energy)
            
            # 恢复原始自旋状态
            self.spins = original_spins
        
        # 找到能量最低的构型
        min_idx = np.argmin(energies)
        ground_energy = energies[min_idx]
        ground_state = np.array(all_states[min_idx])
        
        print(f"暴力枚举基态能量: {ground_energy:.6f}")
        
        return ground_state, ground_energy
    
    def plot_chain(self, ax=None, title=None):
        """绘制自旋链状态，使用环形结构表示周期边界条件"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(8, 8))
        
        # 创建环形布局
        G = self.graph.copy()
        
        # 计算节点的圆形布局位置
        n_spins = len(self.spins)
        radius = 3  # 环的半径
        node_positions = {}
        
        for i in range(n_spins):
            # 计算角度和位置
            angle = 2 * np.pi * i / n_spins
            x = radius * np.cos(angle)
            y = radius * np.sin(angle)
            node_positions[i] = (x, y)
        
        # 设置节点颜色 - 0(蓝色)表示自旋向上，1(红色)表示自旋向下
        node_colors = ['blue' if s == 0 else 'red' for s in self.spins]
        
        # 绘制节点
        nx.draw_networkx_nodes(G, node_positions, node_color=node_colors, 
                              node_size=500, alpha=0.8, ax=ax)
        
        # 绘制边，使用颜色表示相互作用强度
        edge_colors = []
        edge_widths = []
        
        for i in range(n_spins):
            j = (i + 1) % n_spins
            # 相互作用强度决定颜色和线宽
            interaction = self.interactions[i]
            # 对于纯铁磁系统，使用颜色深浅表示耦合强度
            # 使用从浅橙色到深红色的渐变
            intensity = min(1.0, interaction / max(self.interactions))
            color = (1.0, 0.5 * (1 - intensity), 0)  # 从橙色到红色
            # 线宽与相互作用强度成正比
            width = 1 + 3 * intensity
            
            edge_colors.append(color)
            edge_widths.append(width)
        
        # 绘制边
        edges = [(i, (i+1) % n_spins) for i in range(n_spins)]
        nx.draw_networkx_edges(G, node_positions, edgelist=edges, 
                              width=edge_widths, edge_color=edge_colors, 
                              alpha=0.7, ax=ax)
        
        # 添加节点标签
        labels = {i: f"{i}\n{'+' if s == 0 else '-'}" for i, s in enumerate(self.spins)}
        nx.draw_networkx_labels(G, node_positions, labels=labels, 
                               font_size=10, font_color='white', ax=ax)
        
        # 添加边标签（显示相互作用强度）
        edge_labels = {(i, (i+1) % n_spins): f"{self.interactions[i]:.2f}" 
                      for i in range(n_spins)}
        nx.draw_networkx_edge_labels(G, node_positions, edge_labels=edge_labels, 
                                    font_size=8, ax=ax)
        
        # 设置标题
        if title:
            ax.set_title(title)
        
        # 设置轴范围，使图形居中显示
        margin = 1.2
        ax.set_xlim(-radius*margin, radius*margin)
        ax.set_ylim(-radius*margin, radius*margin)
        
        # 关闭坐标轴
        ax.axis('off')
        
        return ax 