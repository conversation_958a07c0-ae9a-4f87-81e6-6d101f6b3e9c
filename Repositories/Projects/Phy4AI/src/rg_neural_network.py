import numpy as np
import matplotlib.pyplot as plt
import copy
import networkx as nx
from matplotlib.patches import Circle

class RGSpinNeuralNetwork:
    """RG Neural Network where each layer is a graph with learnable spins and interaction strengths"""
    
    def __init__(self, spin_chain, learning_rate=0.01, seed=42):
        """
        Initialize RG neural network with graph structure at each layer
        
        Parameters:
        spin_chain: SpinChain object
        learning_rate: Learning rate
        seed: Random seed
        """
        self.original_chain = spin_chain
        self.chain = copy.deepcopy(spin_chain)
        self.n_spins = self.chain.n_spins
        self.lr = learning_rate
        self.seed = seed
        
        # Set random seed
        np.random.seed(seed)
        
        # Calculate network layers: reduce 2 spins per layer until 2 remain
        self.layer_sizes = self._calculate_layer_sizes()
        self.n_layers = len(self.layer_sizes) - 1
        
        # Initialize learnable parameters for each layer
        self.layer_spins = []      # Learnable spin states for each layer
        self.layer_interactions = []  # Learnable interaction strengths for each layer
        self.layer_interaction_masks = []  # Masks to indicate which interactions are learnable
        
        self._initialize_layer_parameters()
        
        # Neural network weights for transforming between layers
        self.weights = []
        self.biases = []
        self._initialize_transformation_parameters()
        
        # Training history
        self.training_history = []
        self.energies = []
        
        print(f"RG Neural Network structure: {self.layer_sizes}")
        print(f"Layer 0 (input): spins learnable, interactions fixed - MAIN OPTIMIZATION TARGET")
        for i in range(1, len(self.layer_sizes)):
            print(f"Layer {i}: spins and interactions both learnable - auxiliary representation")
    
    def _calculate_layer_sizes(self):
        """Calculate number of spins in each layer"""
        sizes = [self.n_spins]
        current_size = self.n_spins
        
        while current_size > 2:
            current_size = max(2, current_size - 2)
            sizes.append(current_size)
        
        return sizes
    
    def _initialize_layer_parameters(self):
        """Initialize learnable spin states and interaction strengths for each layer"""
        
        for layer_idx, layer_size in enumerate(self.layer_sizes):
            if layer_idx == 0:
                # Input layer: spins learnable, interactions fixed
                # Initialize spins from original chain (convert 0/1 to -1/+1)
                initial_spins = 2 * np.array(self.chain.spins, dtype=float) - 1
                self.layer_spins.append(initial_spins.copy())
                
                # Interactions are fixed from original chain
                self.layer_interactions.append(self.chain.interactions.copy())
                
                # Mask: interactions not learnable (all False)
                self.layer_interaction_masks.append(np.zeros(layer_size, dtype=bool))
                
            else:
                # Hidden/output layers: both spins and interactions learnable
                # Initialize spins randomly
                initial_spins = np.random.choice([-1, 1], size=layer_size).astype(float)
                self.layer_spins.append(initial_spins)
                
                # Initialize interactions randomly
                initial_interactions = np.random.uniform(0.1, 1.0, size=layer_size)
                self.layer_interactions.append(initial_interactions)
                
                # Mask: interactions are learnable (all True)
                self.layer_interaction_masks.append(np.ones(layer_size, dtype=bool))
    
    def _initialize_transformation_parameters(self):
        """Initialize neural network transformation parameters between layers"""
        for i in range(self.n_layers):
            input_size = self.layer_sizes[i]
            output_size = self.layer_sizes[i + 1]
            
            # Weight matrix for transforming spins between layers
            weight = np.random.normal(0, 0.1, (output_size, input_size))
            self.weights.append(weight)
            
            # Bias for spin transformation
            bias = np.random.normal(0, 0.1, output_size)
            self.biases.append(bias)
    
    def _spin_activation(self, x):
        """Spin activation function: output -1 or +1"""
        return np.where(x >= 0, 1, -1)
    
    def _forward_pass(self):
        """Forward pass using current learnable layer parameters"""
        # The forward pass simply returns the current learnable spin states
        # Transformations between layers are handled during training
        layer_outputs = []
        
        for layer_idx in range(len(self.layer_sizes)):
            layer_outputs.append(self.layer_spins[layer_idx].copy())
        
        return layer_outputs
    
    def _calculate_layer_energy(self, layer_idx):
        """Calculate energy of a specific layer using its spins and interactions"""
        spins = self.layer_spins[layer_idx]
        interactions = self.layer_interactions[layer_idx]
        n_spins = len(spins)
        
        if n_spins < 2:
            return 0.0
        
        energy = 0.0
        # Ising Hamiltonian: H = -sum_i J_i * s_i * s_{i+1}
        for i in range(n_spins):
            j = (i + 1) % n_spins  # Periodic boundary conditions
            energy -= interactions[i] * spins[i] * spins[j]
        
        return energy
    
    def _calculate_loss(self):
        """Loss function: energy of input layer (Layer 0) with learnable spins and fixed interactions"""
        return self._calculate_layer_energy(0)  # Energy of input layer
    
    def _update_layer_through_network(self, from_layer_idx, to_layer_idx):
        """Update layer spins using neural network transformation"""
        if from_layer_idx >= len(self.layer_spins) or to_layer_idx >= len(self.layer_spins):
            return
        
        input_spins = self.layer_spins[from_layer_idx]
        weight_idx = from_layer_idx  # weights index corresponds to transformation from layer i to i+1
        
        if weight_idx < len(self.weights):
            # Neural network transformation
            linear_output = np.dot(self.weights[weight_idx], input_spins) + self.biases[weight_idx]
            new_spins = self._spin_activation(linear_output)
            
            # Update target layer spins
            self.layer_spins[to_layer_idx] = new_spins.copy()
    
    def _backward_pass(self):
        """Backward pass to update learnable parameters, focusing on input layer optimization"""
        # Calculate gradients for input layer (main optimization target)
        input_spins = self.layer_spins[0]
        input_interactions = self.layer_interactions[0]
        n_spins = len(input_spins)
        
        if n_spins < 2:
            return
        
        # Calculate energy gradients for input layer spins
        # E = -sum_i J_i * s_i * s_{i+1}
        # dE/ds_i = -J_{i-1} * s_{i-1} - J_i * s_{i+1}
        
        input_energy = self._calculate_layer_energy(0)
        
        # Optimize input layer spins using energy gradient
        for spin_idx in range(n_spins):
            # Calculate energy gradient for this spin
            prev_idx = (spin_idx - 1) % n_spins
            next_idx = (spin_idx + 1) % n_spins
            
            # Energy contribution from this spin with its neighbors
            J_prev = input_interactions[prev_idx]  # Interaction with previous spin
            J_curr = input_interactions[spin_idx]  # Interaction with next spin
            s_prev = input_spins[prev_idx]
            s_next = input_spins[next_idx]
            
            # Gradient: dE/ds_i = -J_{i-1} * s_{i-1} - J_i * s_{i+1}
            grad_spin = -J_prev * s_prev - J_curr * s_next
            
            # Update spin (small step)
            self.layer_spins[0][spin_idx] -= self.lr * grad_spin * 0.1
            
            # Apply spin constraint (keep as ±1)
            if self.layer_spins[0][spin_idx] > 0:
                self.layer_spins[0][spin_idx] = 1.0
            else:
                self.layer_spins[0][spin_idx] = -1.0
        
        # Alternative optimization: try spin flips for input layer
        original_energy = self._calculate_layer_energy(0)
        
        for spin_idx in range(n_spins):
            # Try flipping this spin
            original_spin = self.layer_spins[0][spin_idx]
            self.layer_spins[0][spin_idx] = -original_spin
            
            new_energy = self._calculate_layer_energy(0)
            
            # Keep the flip if it reduces energy, otherwise revert
            if new_energy >= original_energy:
                # Revert the flip
                self.layer_spins[0][spin_idx] = original_spin
            # If new_energy < original_energy, keep the flip
        
        # Update other layers as auxiliary representations
        for layer_idx in range(1, len(self.layer_spins)):
            # Update spins (simple gradient descent on layer energy)
            layer_energy = self._calculate_layer_energy(layer_idx)
            
            # Try spin flips for better layer configuration
            for spin_idx in range(len(self.layer_spins[layer_idx])):
                original_spin = self.layer_spins[layer_idx][spin_idx]
                
                # Try flipping spin
                self.layer_spins[layer_idx][spin_idx] = -original_spin
                new_energy = self._calculate_layer_energy(layer_idx)
                
                energy_diff = new_energy - layer_energy
                
                # Keep flip if it reduces energy, otherwise revert
                if energy_diff >= 0:
                    self.layer_spins[layer_idx][spin_idx] = original_spin
            
            # Update interactions (if learnable)
            if np.any(self.layer_interaction_masks[layer_idx]):
                for int_idx in range(len(self.layer_interactions[layer_idx])):
                    if self.layer_interaction_masks[layer_idx][int_idx]:
                        # Gradient for interaction
                        spins = self.layer_spins[layer_idx]
                        n_spins_layer = len(spins)
                        i = int_idx
                        j = (i + 1) % n_spins_layer
                        
                        # dE/dJ_i = -s_i * s_j
                        grad_J_i = -spins[i] * spins[j]
                        
                        self.layer_interactions[layer_idx][int_idx] -= self.lr * grad_J_i
                        # Keep interaction positive
                        self.layer_interactions[layer_idx][int_idx] = max(0.01, self.layer_interactions[layer_idx][int_idx])
        
        # Update neural network weights between layers (maintain connectivity)
        for weight_idx in range(len(self.weights)):
            from_layer = weight_idx
            to_layer = weight_idx + 1
            
            if to_layer < len(self.layer_spins):
                # Update weights to better correlate input and output
                input_spins = self.layer_spins[from_layer]
                output_spins = self.layer_spins[to_layer]
                
                # Update weights based on correlation
                for i in range(len(output_spins)):
                    for j in range(len(input_spins)):
                        # Encourage correlation between layers
                        correlation = output_spins[i] * input_spins[j]
                        self.weights[weight_idx][i, j] += self.lr * correlation * 0.005
                
                # Update biases
                for i in range(len(output_spins)):
                    self.biases[weight_idx][i] += self.lr * output_spins[i] * 0.005
    
    def train_step(self):
        """Single training step"""
        # Forward pass (just get current layer states)
        layer_outputs = self._forward_pass()
        
        # Calculate loss
        loss = self._calculate_loss()
        
        # Backward pass
        self._backward_pass()
        
        # Update layers through neural network transformations
        for layer_idx in range(self.n_layers):
            self._update_layer_through_network(layer_idx, layer_idx + 1)
        
        return loss, layer_outputs
    
    def train(self, n_iterations=1000, save_interval=50):
        """Train the neural network"""
        print(f"Starting RG neural network training for {n_iterations} iterations...")
        
        for iteration in range(n_iterations):
            # Training step
            loss, layer_outputs = self.train_step()
            
            # Record training history
            if iteration % save_interval == 0:
                self.energies.append(loss)
                
                # Save current state
                current_state = {
                    'iteration': iteration,
                    'energy': loss,
                    'layer_outputs': [output.copy() for output in layer_outputs],
                    'layer_spins': [spins.copy() for spins in self.layer_spins],
                    'layer_interactions': [ints.copy() for ints in self.layer_interactions],
                    'weights': [w.copy() for w in self.weights],
                    'biases': [b.copy() for b in self.biases]
                }
                self.training_history.append(current_state)
                
                print(f"Iteration {iteration}: Energy = {loss:.6f}")
        
        print("Training completed!")
    
    def plot_rg_structure(self, state_index=-1, figsize=(15, 10)):
        """Plot RG network structure with spins and interaction strengths"""
        if not self.training_history:
            print("No training history to plot")
            return None
        
        state = self.training_history[state_index]
        layer_spins = state['layer_spins']
        layer_interactions = state['layer_interactions']
        iteration = state['iteration']
        energy = state['energy']
        
        n_layers = len(layer_spins)
        
        # Create subplots for each layer
        fig, axes = plt.subplots(1, n_layers, figsize=figsize)
        if n_layers == 1:
            axes = [axes]
        
        for layer_idx in range(n_layers):
            ax = axes[layer_idx]
            spins = layer_spins[layer_idx]
            interactions = layer_interactions[layer_idx]
            n_spins_layer = len(spins)
            
            # Create circular layout
            if n_spins_layer == 1:
                positions = [(0, 0)]
            elif n_spins_layer == 2:
                positions = [(-0.5, 0), (0.5, 0)]
            else:
                angles = np.linspace(0, 2*np.pi, n_spins_layer, endpoint=False)
                radius = 1.0
                positions = [(radius * np.cos(angle), radius * np.sin(angle)) 
                           for angle in angles]
            
            # Draw connections with interaction strengths
            if n_spins_layer >= 2:
                for i in range(n_spins_layer):
                    j = (i + 1) % n_spins_layer
                    x1, y1 = positions[i]
                    x2, y2 = positions[j]
                    
                    # Get interaction strength
                    interaction = interactions[i] if i < len(interactions) else 1.0
                    
                    # Line thickness based on interaction strength
                    linewidth = 1 + 3 * min(interaction, 2.0)
                    color = 'red' if interaction > 1.0 else 'blue'
                    
                    ax.plot([x1, x2], [y1, y2], color=color, linewidth=linewidth, alpha=0.7)
                    
                    # Add interaction strength label
                    mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
                    ax.text(mid_x, mid_y, f'{interaction:.2f}', 
                           ha='center', va='center', fontsize=8,
                           bbox=dict(boxstyle="round,pad=0.1", facecolor="white", alpha=0.8))
            
            # Draw spin nodes
            for i, (x, y) in enumerate(positions):
                color = 'red' if spins[i] == 1 else 'blue'
                label_char = '↑' if spins[i] == 1 else '↓'
                
                circle = Circle((x, y), 0.1, color=color, alpha=0.8)
                ax.add_patch(circle)
                ax.text(x, y, label_char, ha='center', va='center', 
                       fontsize=12, fontweight='bold', color='white')
                
                # Add node index
                ax.text(x, y-0.25, str(i), ha='center', va='center', fontsize=8)
            
            # Set plot properties
            ax.set_xlim(-1.5, 1.5)
            ax.set_ylim(-1.5, 1.5)
            ax.set_aspect('equal')
            
            # Layer title with learnable info
            if layer_idx == 0:
                input_energy = self._calculate_layer_energy(0)
                title = f'Layer {layer_idx} (INPUT - TARGET)\n{n_spins_layer} spins\nSpins: learnable, J: fixed\nEnergy: {input_energy:.3f}'
            else:
                layer_energy = self._calculate_layer_energy(layer_idx)
                title = f'Layer {layer_idx} (Auxiliary)\n{n_spins_layer} spins\nSpins & J: learnable\nEnergy: {layer_energy:.3f}'
            ax.set_title(title, fontsize=10)
            ax.axis('off')
        
        # Get input layer energy for main title
        input_energy = self._calculate_layer_energy(0)
        plt.suptitle(f'RG Neural Network - Iteration {iteration}\nInput Layer Energy (Target): {input_energy:.4f}', 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        return fig
    
    def plot_training_evolution(self, figsize=(18, 12)):
        """Plot network state evolution during training with interaction strengths"""
        if len(self.training_history) < 2:
            print("Insufficient training history for evolution plot")
            return None
        
        n_snapshots = min(6, len(self.training_history))
        indices = np.linspace(0, len(self.training_history) - 1, n_snapshots, dtype=int)
        
        max_layers = max(len(s['layer_spins']) for s in self.training_history)
        
        fig, axes = plt.subplots(n_snapshots, max_layers, figsize=figsize)
        if n_snapshots == 1:
            axes = axes.reshape(1, -1)
        if max_layers == 1:
            axes = axes.reshape(-1, 1)
        
        for row, state_idx in enumerate(indices):
            state = self.training_history[state_idx]
            layer_spins = state['layer_spins']
            layer_interactions = state['layer_interactions']
            iteration = state['iteration']
            energy = state['energy']
            
            for col in range(max_layers):
                if col < len(layer_spins):
                    ax = axes[row, col]
                    spins = layer_spins[col]
                    interactions = layer_interactions[col]
                    n_spins_layer = len(spins)
                    
                    # Create layout
                    if n_spins_layer == 1:
                        positions = [(0, 0)]
                    elif n_spins_layer == 2:
                        positions = [(-0.5, 0), (0.5, 0)]
                    else:
                        angles = np.linspace(0, 2 * np.pi, n_spins_layer, endpoint=False)
                        radius = 0.8
                        positions = [(radius * np.cos(angle), radius * np.sin(angle)) 
                                   for angle in angles]
                    
                    # Draw connections with interaction strengths
                    if n_spins_layer >= 2:
                        for i in range(n_spins_layer):
                            j = (i + 1) % n_spins_layer
                            x1, y1 = positions[i]
                            x2, y2 = positions[j]
                            
                            interaction = interactions[i] if i < len(interactions) else 1.0
                            linewidth = 1 + 2 * min(interaction, 2.0)
                            color = 'red' if interaction > 1.0 else 'blue'
                            
                            ax.plot([x1, x2], [y1, y2], color=color, linewidth=linewidth, alpha=0.6)
                            
                            # Add interaction label
                            mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
                            ax.text(mid_x, mid_y, f'{interaction:.1f}', 
                                   ha='center', va='center', fontsize=6,
                                   bbox=dict(boxstyle="round,pad=0.1", facecolor="white", alpha=0.7))
                    
                    # Draw spins
                    for i, (x, y) in enumerate(positions):
                        color = 'red' if spins[i] == 1 else 'blue'
                        circle = Circle((x, y), 0.1, color=color, alpha=0.9)
                        ax.add_patch(circle)
                        ax.text(x, y, f'{int(spins[i]):+d}', ha='center', va='center',
                               fontsize=8, fontweight='bold', color='white')
                    
                    ax.set_xlim(-1.2, 1.2)
                    ax.set_ylim(-1.2, 1.2)
                    ax.set_aspect('equal')
                    if row == 0:
                        ax.set_title(f'Layer {col}\n{n_spins_layer} spins', fontsize=10)
                    ax.set_xticks([])
                    ax.set_yticks([])
                    if col == 0:
                        ax.text(-1.8, 0, f'Iter {iteration}\nE={energy:.3f}', ha='center', va='center', 
                               fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
                else:
                    axes[row, col].axis('off')
        
        plt.suptitle('RG Neural Network Training Evolution (Spins and Interactions)', fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_energy_convergence(self, figsize=(10, 6)):
        """Plot energy convergence curve"""
        if not self.energies:
            print("No energy history to plot")
            return None
        
        iterations = [state['iteration'] for state in self.training_history]
        
        fig, ax = plt.subplots(figsize=figsize)
        ax.plot(iterations, self.energies, 'b-', linewidth=2, marker='o', markersize=4)
        ax.set_xlabel('Training Iterations')
        ax.set_ylabel('Energy (Input Layer - Target)')
        ax.set_title('RG Neural Network: Input Layer Energy Convergence')
        ax.grid(True, alpha=0.3)
        
        # Show final energy
        if self.energies:
            final_energy = self.energies[-1]
            ax.axhline(y=final_energy, color='r', linestyle='--', alpha=0.7)
            ax.text(0.7, 0.9, f'Final Input Energy: {final_energy:.4f}', 
                   transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        plt.tight_layout()
        return fig
    
    def get_final_state(self):
        """Get final network state, focusing on input layer optimization"""
        if not self.training_history:
            return None
        
        final_state = self.training_history[-1]
        input_layer_energy = self._calculate_layer_energy(0)
        
        return {
            'input_spins': final_state['layer_spins'][0],  # Main result: optimized input spins
            'input_interactions': final_state['layer_interactions'][0],  # Fixed input interactions
            'input_energy': input_layer_energy,  # Main optimization target
            'final_spins': final_state['layer_spins'][-1],  # Final layer spins (auxiliary)
            'final_interactions': final_state['layer_interactions'][-1],  # Final layer interactions
            'energy': final_state['energy'],  # Same as input_energy
            'all_layer_spins': final_state['layer_spins'],
            'all_layer_interactions': final_state['layer_interactions']
        } 