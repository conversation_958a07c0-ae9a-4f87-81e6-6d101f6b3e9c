import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from src.spin_chain_model import SpinChain

class SDRG:
    """实现强无序重整化群(SDRG)算法"""
    
    def __init__(self, spin_chain):
        """
        初始化SDRG算法
        
        参数:
        spin_chain: SpinChain对象
        """
        self.original_chain = spin_chain
        self.n_spins = spin_chain.n_spins
        self.interactions = spin_chain.interactions.copy()
        
        # 存储自旋和耦合
        self.spins = [i for i in range(self.n_spins)]  # 自旋索引，代表活跃自旋
        self.couplings = self.interactions.copy()  # 当前耦合
        self.energy = 0.0  # 基态能量累积
        self.spin_config = [None] * self.n_spins  # 存储最终自旋构型
        self.history = []  # 记录去角化步骤，用于回溯
        
        # 存储RG步骤
        self.rg_steps = []
        self.energies = []
        
        # 保存初始状态
        self._save_current_state()
    
    def _save_current_state(self):
        """保存当前状态"""
        state = {
            'active_spins': self.spins.copy(),
            'couplings': self.couplings.copy(),
            'energy': self.energy,
            'spin_config': self.spin_config.copy() if self.spin_config else None
        }
        self.rg_steps.append(state)
        self.energies.append(self.energy)
    
    def perform_rg_step(self):
        """执行一步SDRG算法"""
        if len(self.spins) <= 2:
            return False  # 不能再执行RG步骤
        
        # 找到最大绝对耦合
        J_abs = np.abs(self.couplings)
        k = np.argmax(J_abs)
        J_k = self.couplings[k]
        
        # 确定要消除的自旋
        i, j = self.spins[k], self.spins[(k + 1) % len(self.spins)]  # 考虑周期性
        
        # 对于纯铁磁系统，所有耦合都是正的
        # 铁磁：sigma_i = sigma_j
        self.history.append((i, j, 1, abs(J_k)))
        
        # 能量贡献
        self.energy -= abs(J_k)
        
        # 计算有效耦合
        left_idx = (k - 1) % len(self.couplings)  # 左边耦合
        right_idx = (k + 1) % len(self.couplings)  # 右边耦合
        
        # 对于纯铁磁系统，有效耦合计算简化为乘积除以强耦合
        J_eff = self.couplings[left_idx] * self.couplings[right_idx] / abs(J_k) if abs(J_k) > 1e-10 else 0
        
        # 更新耦合和自旋列表
        self.couplings[left_idx] = J_eff  # 更新左边耦合
        
        # 删除k和右边耦合（需要考虑周期性）
        if k == len(self.couplings) - 1:  # 如果k是最后一个元素
            self.couplings = np.delete(self.couplings, [k, 0])
        else:
            self.couplings = np.delete(self.couplings, [k, (k + 1) % len(self.couplings)])
        
        # 删除自旋（需要考虑周期性）
        if k == len(self.spins) - 1:  # 如果k是最后一个元素
            self.spins.pop(k)
            self.spins.pop(0)
        else:
            self.spins.pop((k + 1) % len(self.spins))
            self.spins.pop(k % len(self.spins))
        
        # 更新周期性边界
        if len(self.spins) > 2 and k == 0:  # 更新首尾耦合
            self.couplings[-1] = J_eff
        
        # 保存当前状态
        self._save_current_state()
        
        return True
    
    def solve_final_system(self):
        """处理剩余的自旋系统"""
        # 处理剩余2个自旋
        if len(self.spins) == 2:
            J_last = self.couplings[0]
            # 对于纯铁磁系统，最后两个自旋应该对齐
            self.spin_config[self.spins[0]] = 1
            self.spin_config[self.spins[1]] = 1
            
            # 能量贡献
            self.energy -= abs(J_last)
            
            # 保存最终状态
            self._save_current_state()
            
        # 处理剩余1个自旋的情况
        elif len(self.spins) == 1:
            # 只有一个自旋，可以任意取值，这里设为1
            self.spin_config[self.spins[0]] = 1
            
            # 保存最终状态
            self._save_current_state()
    
    def reconstruct_ground_state(self):
        """回溯构造全链基态构型"""
        # 回溯构造全链构型
        for i, j, sign, _ in reversed(self.history):
            if self.spin_config[i] is not None:
                self.spin_config[j] = sign * self.spin_config[i]
            elif self.spin_config[j] is not None:
                self.spin_config[i] = self.spin_config[j] / sign
            else:
                # 如果两个自旋都未确定，设置第一个为1
                self.spin_config[i] = 1
                self.spin_config[j] = sign * 1
        
        # 转换为0/1表示（与原始SpinChain一致）
        for i in range(len(self.spin_config)):
            if self.spin_config[i] == -1:
                self.spin_config[i] = 0
            elif self.spin_config[i] == 1:
                self.spin_config[i] = 1
        
        return np.array(self.spin_config)
    
    def run_until_ground_state(self, max_steps=100):
        """运行SDRG算法直到找到基态"""
        step = 0
        while step < max_steps and len(self.spins) > 2:
            if not self.perform_rg_step():
                break
            step += 1
        
        # 处理最后剩余的自旋
        self.solve_final_system()
        
        # 重构基态
        ground_state = self.reconstruct_ground_state()
        
        # 输出最终能量
        final_energy = self.energy
        print(f"SDRG过程完成，最终能量: {final_energy:.6f}")
        
        return ground_state, self.energies
    
    def plot_rg_process(self, figsize=(15, 10)):
        """绘制整个RG过程"""
        n_steps = len(self.rg_steps)
        fig, axes = plt.subplots(n_steps, 1, figsize=figsize)
        
        if n_steps == 1:
            axes = [axes]
        
        for i, state in enumerate(self.rg_steps):
            ax = axes[i]
            
            # 创建临时图用于绘制
            G = nx.Graph()
            
            # 添加活跃的自旋节点
            active_spins = state['active_spins']
            n_active = len(active_spins)
            
            if n_active == 0:
                # 如果没有活跃自旋，显示最终构型
                if state['spin_config'] is not None:
                    self._plot_final_config(ax, state['spin_config'], state['energy'])
                continue
            
            # 计算环形布局
            radius = 3
            node_positions = {}
            
            for idx, spin_idx in enumerate(active_spins):
                # 计算角度和位置
                angle = 2 * np.pi * idx / n_active
                x = radius * np.cos(angle)
                y = radius * np.sin(angle)
                node_positions[spin_idx] = (x, y)
                G.add_node(spin_idx)
            
            # 添加边
            couplings = state['couplings']
            for idx in range(n_active):
                i_node = active_spins[idx]
                j_node = active_spins[(idx + 1) % n_active]
                coupling = couplings[idx]
                G.add_edge(i_node, j_node, weight=coupling)
            
            # 确定节点颜色
            node_colors = []
            # 如果有spin_config，使用它来确定颜色
            if state['spin_config'] is not None:
                for spin_idx in active_spins:
                    if state['spin_config'][spin_idx] is not None:
                        # 自旋为0时为蓝色，自旋为1时为红色
                        color = 'blue' if state['spin_config'][spin_idx] == 0 else 'red'
                    else:
                        # 如果自旋状态未确定，使用浅蓝色
                        color = 'lightblue'
                    node_colors.append(color)
            else:
                # 如果没有spin_config，使用浅蓝色
                node_colors = ['lightblue' for _ in active_spins]
            
            # 绘制节点
            nx.draw_networkx_nodes(G, node_positions, node_color=node_colors, 
                                  node_size=500, alpha=0.8, ax=ax)
            
            # 绘制边，使用颜色表示相互作用强度
            edge_colors = []
            edge_widths = []
            
            for idx in range(n_active):
                coupling = couplings[idx]
                # 对于纯铁磁系统，使用颜色深浅表示耦合强度
                max_coupling = max(couplings) if len(couplings) > 0 else 1.0
                intensity = min(1.0, coupling / max_coupling)
                color = (1.0, 0.5 * (1 - intensity), 0)  # 从橙色到红色
                
                # 线宽与相互作用强度成正比
                width = 1 + 3 * intensity
                
                edge_colors.append(color)
                edge_widths.append(width)
            
            # 绘制边
            edges = [(active_spins[idx], active_spins[(idx+1) % n_active]) for idx in range(n_active)]
            nx.draw_networkx_edges(G, node_positions, edgelist=edges, 
                                  width=edge_widths, edge_color=edge_colors, 
                                  alpha=0.7, ax=ax)
            
            # 添加节点标签
            labels = {spin_idx: f"{spin_idx}" for spin_idx in active_spins}
            nx.draw_networkx_labels(G, node_positions, labels=labels, 
                                   font_size=10, font_color='black', ax=ax)
            
            # 添加边标签（显示相互作用强度）
            edge_labels = {(active_spins[idx], active_spins[(idx+1) % n_active]): f"{couplings[idx]:.2f}" 
                          for idx in range(n_active)}
            nx.draw_networkx_edge_labels(G, node_positions, edge_labels=edge_labels, 
                                        font_size=8, ax=ax)
            
            # 设置标题 - 步骤从1开始而不是0
            title = f"Step {i+1}, Energy = {state['energy']:.4f}, Active Spins = {n_active}"
            ax.set_title(title)
            
            # 设置轴范围，使图形居中显示
            margin = 1.2
            ax.set_xlim(-radius*margin, radius*margin)
            ax.set_ylim(-radius*margin, radius*margin)
            
            # 关闭坐标轴
            ax.axis('off')
        
        plt.tight_layout()
        return fig
    
    def _plot_final_config(self, ax, spin_config, energy):
        """绘制最终的自旋构型"""
        # 创建临时自旋链对象用于绘图
        temp_chain = SpinChain(len(spin_config))
        temp_chain.spins = np.array(spin_config)
        temp_chain.interactions = self.original_chain.interactions.copy()
        
        # 绘制最终构型
        temp_chain.plot_chain(ax=ax, title=f"Final Configuration, Energy = {energy:.4f}")
        
        return ax 