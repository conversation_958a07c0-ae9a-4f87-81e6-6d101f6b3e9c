# ViT_SSM

基于视觉Transformer的沙斯特里-萨瑟兰模型(Shastry-Sutherland Model)研究框架。

## 项目结构

项目结构已按照标准Python项目布局进行了组织：

```
ViT_SSM/
├── configs/              # 配置文件
│   ├── __init__.py
│   └── config.py         # 主配置文件
├── jobs/                 # 作业脚本
│   └── job.pbs           # PBS作业脚本
├── notebooks/            # Jupyter笔记本
│   └── plots/            # 图像输出
├── reference/            # 参考文档和代码
├── results/              # 模拟结果
│   └── L={L}/
│       └── J2={J2}/
│           └── J1={J1}/
│               ├── analysis/  # 分析结果
│               └── training/  # 训练结果
├── scripts/              # 运行脚本
│   └── main.py           # 主运行脚本
├── src/                  # 源代码
│   ├── __init__.py
│   ├── analysis/         # 分析工具
│   │   └── __init__.py
│   ├── models/           # 机器学习模型
│   │   ├── __init__.py
│   │   ├── CTWF.py
│   │   ├── ViT.py
│   │   └── ...
│   ├── physics/          # 物理模型
│   │   ├── __init__.py
│   │   └── shastry_sutherland.py
│   ├── runner.py         # 运行器模块
│   └── utils/            # 工具函数
│       ├── __init__.py
│       └── FE_VMC_SRt.py
├── run.py                # 主入口点
└── README.md             # 本文件
```

## 使用方法

### 直接运行单个模拟

```bash
python run.py <L> <J2> <J1>
```

例如：
```bash
python run.py 4 0.05 0.03
```

### 使用PBS提交作业

```bash
cd ViT_SSM
qsub jobs/job.pbs
```

### 使用Python API

```python
from src.runner import ViTSSMRunner

# 运行单个模拟
runner = ViTSSMRunner(L=4, J2=0.05, J1=0.03)
runner.setup_model()
runner.run()

# 或者使用静态方法
ViTSSMRunner.run_simulation(L=4, J2=0.05, J1=0.03)
```

## 配置

项目的主要配置在`configs/config.py`文件中：

- `ModelConfig`: 模型参数（层数、特征维度等）
- `TrainingConfig`: 训练参数（学习率、样本数等）
- `SystemConfig`: 系统参数（J1、J2列表等）

## 结果目录结构

模拟结果将保存在以下格式的目录中：

```
results/L={L}/J2={J2:.2f}/J1={J1:.2f}/
├── training/             # 训练相关文件
│   ├── energy_*.log      # 训练能量日志
│   ├── parameters_*.txt  # 训练参数配置
│   └── ViTNQS_*.pkl     # 训练后的模型参数
└── analysis/             # 分析相关文件
    └── (待添加分析脚本输出)
``` 