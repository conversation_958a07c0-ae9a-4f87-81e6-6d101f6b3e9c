#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ViT_SSM运行器模块
提供了一种更面向对象的方式来进行模拟
"""

import os
import sys
import time
import json
import numpy as np
import jax
import netket as nk

from configs.config import ModelConfig, TrainingConfig, SystemConfig
from src.models.CTWF import CTWFNQS
from src.utils.FE_VMC_SRt import CustomFreeEnergyVMC_SRt, log_message
from src.physics.shastry_sutherland import (
    shastry_sutherland_lattice, 
    shastry_sutherland_hamiltonian, 
    shastry_sutherland_all_symmetries
)

class ViTSSMRunner:
    """ViT_SSM运行器类，提供面向对象的接口"""
    
    def __init__(self, L, J2, J1, result_dir=None):
        """初始化运行器
        
        Args:
            L: 系统大小
            J2: J2参数值
            J1: J1参数值
            result_dir: 结果保存目录，如果为None则自动生成
        """
        self.L = L
        self.J2 = J2
        self.J1 = J1
        self.Q = 1.00 - J2
        
        # 创建结果目录，参考Shastry-Sutherland_model结构
        if result_dir is None:
            self.result_dir = f"results/L={L}/J2={J2:.2f}/J1={J1:.2f}"
        else:
            self.result_dir = result_dir
            
        self.training_dir = os.path.join(self.result_dir, "training")
        self.analysis_dir = os.path.join(self.result_dir, "analysis")
        
        # 创建所有必要的目录
        os.makedirs(self.training_dir, exist_ok=True)
        os.makedirs(self.analysis_dir, exist_ok=True)
        
        # 设置日志文件 - 训练日志保存在training目录
        self.energy_log = os.path.join(self.training_dir, f"energy_L={L}_J2={J2:.2f}_J1={J1:.2f}.log")
        
        # 初始化物理系统
        self.lattice = shastry_sutherland_lattice(L, L)
        self.N = self.lattice.n_nodes
        
        # 记录开始信息
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Study of the Shastry-Sutherland Model")
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"System parameters: L={L}, N={self.N}")
        log_message(self.energy_log, f"  - System size: L={L}, N={self.N}")
        log_message(self.energy_log, f"  - System parameters: J1={J1}, J2={J2}, Q={self.Q}")
    
    def setup_model(self):
        """设置模型和优化器"""
        # 创建Hamiltonian
        self.ha, self.hi = shastry_sutherland_hamiltonian(
            lattice=self.lattice,
            J1=self.J1,
            J2=self.J2,
            spin=0.5,
            Q=self.Q,
            total_sz=0
        )
        
        # 设置采样器
        self.sampler = nk.sampler.MetropolisExchange(
            hilbert=self.hi, 
            graph=self.lattice, 
            n_chains=TrainingConfig.N_samples, 
            d_max=2,
            n_chains_per_rank=None
        )
        
        # 设置模型
        model_no_symm = CTWFNQS(
            num_layers=ModelConfig.num_layers,
            d_model=ModelConfig.d_model,
            heads=ModelConfig.heads,
            n_sites=self.lattice.n_nodes,
            patch_size=ModelConfig.patch_size,
        )
        
        # 获取对称性
        symmetries = shastry_sutherland_all_symmetries(self.lattice)
        
        # 使用对称性
        self.model = nk.nn.blocks.SymmExpSum(
            module=model_no_symm, 
            symm_group=symmetries, 
            character_id=None
        )
        
        # 初始化参数
        key = jax.random.PRNGKey(TrainingConfig.seed)
        key, subkey = jax.random.split(key)
        
        # 创建变分量子态
        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=TrainingConfig.N_samples,
            n_samples_per_rank=None,
            n_discard_per_chain=TrainingConfig.N_discard,
            chunk_size=TrainingConfig.chunk_size,
            sampler_seed=subkey,
        )
        
        # 记录模型参数
        n_params = nk.jax.tree_size(self.vqs.parameters)
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Model parameters:")
        log_message(self.energy_log, f"  - Number of layers = {ModelConfig.num_layers}")
        log_message(self.energy_log, f"  - d_model = {ModelConfig.d_model}")
        log_message(self.energy_log, f"  - Number of heads = {ModelConfig.heads}")
        log_message(self.energy_log, f"  - Patch size = {ModelConfig.patch_size}")
        log_message(self.energy_log, f"  - Symmetries used: {len(symmetries)}")
        log_message(self.energy_log, f"  - Total parameters = {n_params}")
        
        # 记录训练参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Training parameters:")
        log_message(self.energy_log, f"  - Learning rate: {TrainingConfig.learning_rate}")
        log_message(self.energy_log, f"  - Total annealing steps: {TrainingConfig.N_iters}")
        log_message(self.energy_log, f"  - Samples: {TrainingConfig.N_samples}")
        log_message(self.energy_log, f"  - Discarded samples: {TrainingConfig.N_discard}")
        log_message(self.energy_log, f"  - Chunk size: {TrainingConfig.chunk_size}")
        
        # 添加检查Sharding状态的日志
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Device status:")
        log_message(self.energy_log, f"  - Number of devices: {len(jax.devices())}")
        log_message(self.energy_log, f"  - Sharding: {nk.config.netket_experimental_sharding}")
        
        # 保存参数
        params_dict = {
            "seed": TrainingConfig.seed,
            "diag_shift": TrainingConfig.diag_shift,
            "learning_rate": TrainingConfig.learning_rate,
            "N_opt": TrainingConfig.N_iters,
            "N_samples": TrainingConfig.N_samples,
            "N_discard": TrainingConfig.N_discard,
            "d_model": ModelConfig.d_model,
            "heads": ModelConfig.heads,
            "num_layers": ModelConfig.num_layers,
            "patch_size": ModelConfig.patch_size,
            "L": self.L,
            "N": self.N,
            "J1": self.J1,
            "J2": self.J2,
            "Q": self.Q,
            "lattice_extent": [self.L, self.L],
            "lattice_pbc": [True, True],
            "total_sz": 0
        }
        
        params_file = os.path.join(self.training_dir, f"parameters_L={self.L}_J2={self.J2:.2f}_J1={self.J1:.2f}.txt")
        with open(params_file, "w") as f_out:
            json.dump(params_dict, f_out, indent=4)
        
        # 设置优化器
        optimizer = nk.optimizer.Sgd(learning_rate=TrainingConfig.learning_rate)
        
        # 创建VMC
        self.vmc = CustomFreeEnergyVMC_SRt(
            reference_energy=SystemConfig.reference_energy,
            temperature=TrainingConfig.temperature,
            hamiltonian=self.ha,
            optimizer=optimizer,
            diag_shift=TrainingConfig.diag_shift,
            variational_state=self.vqs
        )
        
        return self
    
    def run(self, n_iter=None):
        """运行模拟
        
        Args:
            n_iter: 迭代次数，如果为None则使用配置中的值
        """
        if n_iter is None:
            n_iter = TrainingConfig.N_iters
            
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Start training...")
        
        # 记录时间
        start = time.time()
        self.vmc.run(n_iter=n_iter, energy_log=self.energy_log)
        end = time.time()
        
        runtime = end - start
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Training finished, total running time = {runtime:.2f} seconds")
        
        # 保存状态到training目录
        import pickle
        state_file = os.path.join(self.training_dir, f"ViTNQS_L={self.L}_J2={self.J2:.2f}_J1={self.J1:.2f}.pkl")
        with open(state_file, "wb") as f_state:
            pickle.dump(self.vqs.parameters, f_state)
        log_message(self.energy_log, f"The trained quantum state parameters have been saved to: {state_file}")
        log_message(self.energy_log, "="*50)
        
        return self
    
    @classmethod
    def run_simulation(cls, L, J2, J1):
        """静态方法：运行单个模拟
        
        Args:
            L: 系统大小
            J2: J2参数值
            J1: J1参数值
        """
        runner = cls(L, J2, J1)
        runner.setup_model()
        runner.run()
        return runner 