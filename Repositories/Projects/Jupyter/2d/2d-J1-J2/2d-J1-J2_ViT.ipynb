{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sun Feb 23 15:01:34 2025       \n", "+---------------------------------------------------------------------------------------+\n", "| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |\n", "|-----------------------------------------+----------------------+----------------------+\n", "| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |\n", "|                                         |                      |               MIG M. |\n", "|=========================================+======================+======================|\n", "|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:03:00.0 Off |                    0 |\n", "| N/A   43C    P0              60W / 400W |      0MiB / 40960MiB |      0%      Default |\n", "|                                         |                      |             Disabled |\n", "+-----------------------------------------+----------------------+----------------------+\n", "                                                                                         \n", "+---------------------------------------------------------------------------------------+\n", "| Processes:                                                                            |\n", "|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |\n", "|        ID   ID                                                             Usage      |\n", "|=======================================================================================|\n", "|  No running processes found                                                           |\n", "+---------------------------------------------------------------------------------------+\n"]}], "source": ["!nvidia-smi  # 查看GPU信息"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from flax import linen as nn\n", "\n", "from typing import Sequence\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "from jax._src import dtypes\n", "\n", "# 正负对称的初始化\n", "def custom_uniform(scale=1e-2, dtype=jnp.float_):\n", "    def init(key, shape, dtype=dtype):\n", "        dtype = dtypes.canonicalize_dtype(dtype)\n", "        return jax.random.uniform(key, shape, dtype, minval=-scale, maxval=scale)\n", "    return init #返回的init输入符合jax接口规范\n", "\n", "@jax.jit\n", "def log_cosh(x):\n", "    sgn_x = -2 * jnp.signbit(x.real) + 1\n", "    x = x * sgn_x\n", "    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)\n", "\n", "@jax.jit\n", "def attention(J, values, shift):\n", "    values = jnp.roll(values, shift, axis=0)\n", "    return jnp.sum(J * values, axis=0)\n", "\n", "class EncoderBlock(nn.Module):\n", "    d_model: int\n", "    num_heads: int\n", "    num_patches: int\n", "    patch_size: int\n", "\n", "    def setup(self):\n", "        scale = (3.0 * 0.7 / self.num_patches) ** 0.5\n", "\n", "        self.v_projR = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.1, \"fan_in\", \"uniform\"), # 梯度缩放，越小越不容易爆炸，越容易消失\n", "                                bias_init=nn.initializers.zeros)\n", "        self.v_projI = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.1, \"fan_in\", \"uniform\"),\n", "                                bias_init=nn.initializers.zeros)\n", "        \n", "        self.JR = self.param(\"JR\", custom_uniform(scale=scale), (self.num_patches, self.num_heads, 1), jnp.float64)\n", "        self.JI = self.param(\"JI\", custom_uniform(scale=scale), (self.num_patches, self.num_heads, 1), jnp.float64)\n", "        \n", "        self.W0R = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "        self.W0I = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "\n", "    def __call__(self, x):\n", "        J = self.JR + 1j * self.JI\n", "        x = self.v_projR(x).reshape(self.num_patches, self.num_heads, -1) + 1j * self.v_projI(x).reshape(self.num_patches, self.num_heads, -1)\n", "        x = jax.vmap(attention, (None, None, 0))(J, x, jnp.arange(self.num_patches))\n", "        x = x.reshape(self.num_patches, -1)\n", "        x = self.W0R(x) + 1j * self.W0I(x)\n", "        return log_cosh(x)\n", "\n", "class Transformer_Enc(nn.Module):\n", "    d_model: int\n", "    num_heads: int\n", "    num_patches: int\n", "    patch_size: int\n", "    n_layers: int  # added parameter to specify number of layers\n", "\n", "    def setup(self):\n", "        self.encoders = [\n", "            EncoderBlock(self.d_model, self.num_heads, self.num_patches, self.patch_size)\n", "            for _ in range(self.n_layers)\n", "        ]\n", "\n", "    def __call__(self, x):\n", "        x = x.reshape(x.shape[0], -1, self.patch_size)\n", "        for encoder in self.encoders:\n", "            x = jax.vmap(encoder)(x)\n", "        return jnp.sum(x, axis=(1, 2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import time\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "from netket.nn.blocks import SymmExpSum\n", "\n", "\n", "import os\n", "# Set CUDA environment variables\n", "# os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "# os.environ[\"XLA_PYTHON_CLIENT_ALLOCATOR\"] = \"platform\"\n", "\n", "# 设置物理系统参数与配置\n", "seed = 0  # 随机数生成器的种子，用于保证结果的可重复性\n", "\n", "\n", "\n", "# 设置优化参数\n", "diag_shift = 1e-3  # 对角线偏移量，用于稳定优化过程\n", "eta = 0.005  # 学习率，控制每次参数更新的步长\n", "N_opt = 10000  # 优化迭代次数，即优化过程中的最大迭代次数\n", "N_samples = 2**12 # 采样数量，即每次迭代中生成的样本数量\n", "N_discard = 0  # 丢弃的采样数量，用于热化过程\n", "\n", "# 设置波函数参数\n", "f = 6  # 缩放因子，用于控制嵌入维度\n", "heads = 12 # 注意力头的数量，即多头注意力机制中的头数\n", "d_model = f * heads  # 嵌入维度，即每个注意力头的维度\n", "patch_size = 4  # 块大小，用于将输入序列分块处理\n", "n_layers = 1  # 编码器层数，即Transformer中的层数"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置2D晶格的参数\n", "L = 8 # 晶格长度，即晶格中包含的节点数\n", "N = L * L  # 晶格中的节点总数\n", "J2 = 0.55  # 下一个最近邻相互作用的耦合常数，表示相邻节点之间的相互作用强度\n", "\n", "# 创建2D晶格\n", "lattice = nk.graph.Grid(extent=[L, L], pbc=[True, True], max_neighbor_order=2)\n", "\n", "lattice.draw() \n", "\n", "# 创建<PERSON><PERSON>空间\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 创建哈密顿量\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of parameters =  11616\n"]}], "source": ["import netket as nk\n", "from netket.utils.group.planar import D\n", "\n", "## 选择对称群：如使用晶格平移或点群对称性\n", "# symm_group = lattice.space_group(D(4))\n", "symm_group = lattice.point_group()\n", "\n", "# 构造原始Transformer模型\n", "wf_no_symm = Transformer_Enc(d_model=d_model,\n", "                              num_heads=heads,\n", "                              num_patches=lattice.n_nodes // patch_size,\n", "                              patch_size=patch_size,\n", "                              n_layers=n_layers)\n", "\n", "# 选择对称群：如使用晶格平移或点群对称性\n", "# 例如：使用点群 (mirror + rotations) 对称性\n", "\n", "# 使用 SymmExpSum 对 transformer 进行对称化\n", "from netket.nn.blocks import SymmExpSum\n", "wf_sym = SymmExpSum(module=wf_no_symm, symm_group=symm_group, character_id=None)\n", "\n", "\n", "# 初始化随机数生成器\n", "key = jax.random.PRNG<PERSON><PERSON>(seed)  # 创建一个随机数生成器的主键\n", "key, subkey = jax.random.split(key, num=2)  # 分割主键，生成子键\n", "\n", "# 初始化变分波函数的参数\n", "params = wf_sym.init(subkey, jnp.zeros((1, lattice.n_nodes)))  # 使用子键初始化变分波函数的参数\n", "init_samples = jnp.zeros((1,))  # 初始化采样数组\n", "\n", "\n", "# 创建Metropolis局部采样器\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hilbert, \n", "                                        graph=lattice, \n", "                                        d_max=2, \n", "                                        n_chains=N_samples, \n", "                                        sweep_size=lattice.n_nodes\n", "                                        )  # 创建一个Metropolis局部采样器，用于生成样本\n", "\n", "# 初始化采样器的随机数生成器\n", "key, subkey = jax.random.split(key, 2)  # 再次分割主键，生成新的子键\n", "sampler_seed = subkey  # 将子键赋值给采样器的随机数生成器\n", "\n", "# 创建变分量子态\n", "vstate = nk.vqs.MCState(sampler=sampler, \n", "                        model=wf_sym, \n", "                        sampler_seed=sampler_seed, \n", "                        n_samples=N_samples, \n", "                        n_discard_per_chain=N_discard, \n", "                        variables=params,\n", "                        chunk_size=2**10\n", "                        )  # 创建一个变分量子态，用于存储变分波函数和采样器的状态\n", "\n", "print('Number of parameters = ', nk.jax.tree_size(vstate.parameters))  # 输出变分波函数的参数数量"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# # 创建变分蒙特卡洛驱动器\n", "import optax\n", "\n", "# 创建梯度裁剪器\n", "clipper = optax.clip_by_global_norm(1.0)\n", "\n", "# 创建优化器\n", "optimizer = optax.chain(\n", "    clipper,\n", "    optax.sgd(learning_rate=eta)\n", ")\n", "\n", "# optimizer = nk.optimizer.Adam(learning_rate=eta)  # 创建一个Adam优化器\n", "vmc = VMC_SRt(hamiltonian=hamiltonian, optimizer=optimizer, diag_shift=diag_shift, variational_state=vstate)  # 创建一个变分蒙特卡洛驱动器，用于优化变分波函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "697b687ac22949eda7e85940047d75c7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 17\u001b[0m\n\u001b[1;32m     15\u001b[0m start \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m# 注意：不再传入 out 参数，即不进行磁盘写入，仅调试使用\u001b[39;00m\n\u001b[0;32m---> 17\u001b[0m \u001b[43mvmc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn_iter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mN_opt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallback\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcheck_energy\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     18\u001b[0m end \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     20\u001b[0m last_energy \u001b[38;5;241m=\u001b[39m energy_list[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m] \u001b[38;5;28;01mif\u001b[39;00m energy_list \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/driver/abstract_variational_driver.py:347\u001b[0m, in \u001b[0;36mAbstractVariationalDriver.run\u001b[0;34m(self, n_iter, out, obs, step_size, show_progress, save_params_every, write_every, callback, timeit)\u001b[0m\n\u001b[1;32m    344\u001b[0m old_step \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_count\n\u001b[1;32m    345\u001b[0m first_step \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m--> 347\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m step \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39miter(n_iter, step_size):\n\u001b[1;32m    348\u001b[0m     log_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mestimate(obs)\n\u001b[1;32m    349\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_log_additional_data(log_data, step)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/driver/abstract_variational_driver.py:232\u001b[0m, in \u001b[0;36mAbstractVariationalDriver.iter\u001b[0;34m(self, n_steps, step)\u001b[0m\n\u001b[1;32m    230\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, n_steps, step):\n\u001b[1;32m    231\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, step):\n\u001b[0;32m--> 232\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_forward_and_backward\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    233\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    234\u001b[0m             \u001b[38;5;28;01myield\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_count\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/experimental/driver/vmc_srt.py:275\u001b[0m, in \u001b[0;36mVMC_SRt._forward_and_backward\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    272\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mreset()\n\u001b[1;32m    274\u001b[0m \u001b[38;5;66;03m# Compute the local energy estimator and average Energy\u001b[39;00m\n\u001b[0;32m--> 275\u001b[0m local_energies \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlocal_estimators\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_ham\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    277\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_loss_stats \u001b[38;5;241m=\u001b[39m nkstats\u001b[38;5;241m.\u001b[39mstatistics(local_energies)\n\u001b[1;32m    279\u001b[0m samples \u001b[38;5;241m=\u001b[39m _flatten_samples(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39msamples)\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:614\u001b[0m, in \u001b[0;36mMCState.local_estimators\u001b[0;34m(self, op, chunk_size)\u001b[0m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;129m@timing\u001b[39m\u001b[38;5;241m.\u001b[39mtimed\n\u001b[1;32m    592\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mlocal_estimators\u001b[39m(\u001b[38;5;28mself\u001b[39m, op: AbstractOperator, \u001b[38;5;241m*\u001b[39m, chunk_size: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    593\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    594\u001b[0m \u001b[38;5;124;03m    Compute the local estimators for the operator :code:`op` (also known as local energies\u001b[39;00m\n\u001b[1;32m    595\u001b[0m \u001b[38;5;124;03m    when :code:`op` is the Hamiltonian) at the current configuration samples :code:`self.samples`.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    612\u001b[0m \u001b[38;5;124;03m            of the model. (Default: :code:`self.chunk_size`)\u001b[39;00m\n\u001b[1;32m    613\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 614\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlocal_estimators\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunk_size\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:782\u001b[0m, in \u001b[0;36mlocal_estimators\u001b[0;34m(state, op, chunk_size)\u001b[0m\n\u001b[1;32m    781\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mlocal_estimators\u001b[39m(state: MCState, op: AbstractOperator, \u001b[38;5;241m*\u001b[39m, chunk_size: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m--> 782\u001b[0m     s, extra_args \u001b[38;5;241m=\u001b[39m \u001b[43mget_local_kernel_arguments\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    784\u001b[0m     shape \u001b[38;5;241m=\u001b[39m s\u001b[38;5;241m.\u001b[39mshape\n\u001b[1;32m    785\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m jnp\u001b[38;5;241m.\u001b[39mndim(s) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m2\u001b[39m:\n\u001b[1;32m    786\u001b[0m         \u001b[38;5;66;03m# jit for gda\u001b[39;00m\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/expect.py:61\u001b[0m, in \u001b[0;36mget_local_kernel_arguments\u001b[0;34m(vstate, Ô)\u001b[0m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;129m@dispatch\u001b[39m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_local_kernel_arguments\u001b[39m(vstate: MCState, Ô: DiscreteOperator):  \u001b[38;5;66;03m# noqa: F811\u001b[39;00m\n\u001b[1;32m     59\u001b[0m     check_hilbert(vstate\u001b[38;5;241m.\u001b[39m<PERSON><PERSON>, Ô\u001b[38;5;241m.\u001b[39mhilbert)\n\u001b[0;32m---> 61\u001b[0m     σ \u001b[38;5;241m=\u001b[39m \u001b[43mvstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msamples\u001b[49m\n\u001b[1;32m     62\u001b[0m     σp, mels \u001b[38;5;241m=\u001b[39m Ô\u001b[38;5;241m.\u001b[39mget_conn_padded(σ)\n\u001b[1;32m     63\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m σ, (σp, mels)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:570\u001b[0m, in \u001b[0;36mMCState.samples\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    559\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    560\u001b[0m \u001b[38;5;124;03mR<PERSON>urns the set of cached samples.\u001b[39;00m\n\u001b[1;32m    561\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    567\u001b[0m \u001b[38;5;124;03m:meth:`~MCState.reset` or :meth:`~MCState.sample`.\u001b[39;00m\n\u001b[1;32m    568\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    569\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m:\n\u001b[0;32m--> 570\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    571\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:549\u001b[0m, in \u001b[0;36mMCState.sample\u001b[0;34m(self, chain_length, n_samples, n_discard_per_chain)\u001b[0m\n\u001b[1;32m    546\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m timer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    547\u001b[0m             _\u001b[38;5;241m.\u001b[39mblock_until_ready()\n\u001b[0;32m--> 549\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msampler_state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    550\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sampler_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    551\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sampler_variables\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    552\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msampler_state\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    553\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchain_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchain_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    554\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    555\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/base.py:292\u001b[0m, in \u001b[0;36mSampler.sample\u001b[0;34m(sampler, machine, parameters, state, chain_length)\u001b[0m\n\u001b[1;32m    289\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m state \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    290\u001b[0m     state \u001b[38;5;241m=\u001b[39m sampler\u001b[38;5;241m.\u001b[39mreset(machine, parameters)\n\u001b[0;32m--> 292\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sample_chain\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    293\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwrap_afun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmachine\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchain_length\u001b[49m\n\u001b[1;32m    294\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/utils/struct/pytree.py:371\u001b[0m, in \u001b[0;36mPytree._pytree__unflatten\u001b[0;34m(cls, static_fields, node_values)\u001b[0m\n\u001b[1;32m    364\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    365\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnexpected fields in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(all_vars\u001b[38;5;241m.\u001b[39mkeys())\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    366\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYou cannot add new fields to a Pytree after it has been initialized.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    367\u001b[0m         )\n\u001b[1;32m    369\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m node_values, MappingProxyType(static)\n\u001b[0;32m--> 371\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[1;32m    372\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_pytree__unflatten\u001b[39m(\n\u001b[1;32m    373\u001b[0m     \u001b[38;5;28mcls\u001b[39m: \u001b[38;5;28mtype\u001b[39m[P],\n\u001b[1;32m    374\u001b[0m     static_fields: tp\u001b[38;5;241m.\u001b[39mMapping[\u001b[38;5;28mstr\u001b[39m, tp\u001b[38;5;241m.\u001b[39mAny],\n\u001b[1;32m    375\u001b[0m     node_values: \u001b[38;5;28mtuple\u001b[39m[tp\u001b[38;5;241m.\u001b[39mAny, \u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m],\n\u001b[1;32m    376\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m P:\n\u001b[1;32m    377\u001b[0m     pytree \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mobject\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__new__\u001b[39m(\u001b[38;5;28mcls\u001b[39m)\n\u001b[1;32m    378\u001b[0m     pytree\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__dict__\u001b[39m\u001b[38;5;241m.\u001b[39mupdate(\u001b[38;5;28mzip\u001b[39m(static_fields[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_pytree__node_fields\u001b[39m\u001b[38;5;124m\"\u001b[39m], node_values))\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import numpy as np\n", "import time\n", "\n", "energy_list = []\n", "def check_energy(step, log_data, driver):\n", "    energy = driver.state.expect(hamiltonian).mean\n", "    energy_list.append(energy)\n", "    if np.isnan(energy):\n", "        print(f\"NaN energy detected at step {step}\")\n", "        driver.stop_training = True\n", "        return False\n", "    return True\n", "\n", "try:\n", "    start = time.time()\n", "    # 注意：不再传入 out 参数，即不进行磁盘写入，仅调试使用\n", "    vmc.run(n_iter=N_opt, callback=check_energy)\n", "    end = time.time()\n", "    \n", "    last_energy = energy_list[-1] if energy_list else None\n", "    if last_energy is not None:\n", "        print(f'The optimized energy is E0 = {last_energy / (4*N)}')\n", "    print(f'The calculation took {end - start} seconds')\n", "    \n", "except Exception as e:\n", "    print(f\"Error during optimization: {str(e)}\")\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 使用 energy_list 中的数据绘制能量迭代图\n", "iters_ViT = list(range(len(energy_list)))\n", "# 因为之前保存的 energy 为总能量，所以进行归一化处理\n", "energy_ViT = [e / (4*N) for e in energy_list]\n", "\n", "print('J2 = ', J2)\n", "print('The optimized ViT energy is E0 = ', np.real(energy_ViT[-1]))\n", "\n", "if L <= 4:\n", "    # 计算基态能量（仅在小系统下计算）\n", "    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)\n", "    exact_gs_energy = evals[0] / (4*N)\n", "    print('Exact ground-state energy E0 = ', exact_gs_energy)\n", "\n", "    relative_errors = [abs((e - exact_gs_energy) / exact_gs_energy) for e in energy_ViT]\n", "    final_relative_error = relative_errors[-1]\n", "    print('Final relative error = ', final_relative_error)\n", "\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_ViT, energy_ViT, color='C8', label='ViT')\n", "ax1.set_ylabel('E/N')\n", "ax1.set_xlabel('Iteration')\n", "ax1.set_title(f'Energy iteration (L={N})')\n", "\n", "if 'exact_gs_energy' in globals():\n", "    # 绘制参考的基态能量横线\n", "    ax1.axhline(y=exact_gs_energy, color='k', linewidth=2, linestyle='--', label='Exact')\n", "\n", "ax1.legend()\n", "ax1.relim()\n", "ax1.autoscale_view()  # 自动调整上下限\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}