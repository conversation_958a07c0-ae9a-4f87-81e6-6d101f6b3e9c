{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground state energy: -0.490552\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# System parameters\n", "Nx, Ny = 3, 3  # 3x3 lattice\n", "N = Nx * Ny\n", "J1, J2 = 1, 0.2  # coupling constants\n", "\n", "# Pauli matrices\n", "Sx = 0.5 * np.array([[0, 1], [1, 0]])\n", "Sy = 0.5 * np.array([[0, -1j], [1j, 0]])\n", "Sz = 0.5 * np.array([[1, 0], [0, -1]])\n", "I = np.eye(2)\n", "\n", "def build_interaction(i, j, op):\n", "    \"\"\"Construct the interaction term between sites i and j\"\"\"\n", "    ops = [I] * N  # Initialize as a list of identity matrices\n", "    ops[i] = ops[j] = op  # Set the i-th and j-th positions to the operator op\n", "    term = ops[0]\n", "    for op in ops[1:]:\n", "        term = np.kron(term, op)  # Compute the Kronecker product\n", "    return term\n", "\n", "# Initialize the Hamiltonian\n", "H = np.zeros((2**N, 2**N), dtype=complex)\n", "\n", "# J1 term (nearest neighbor interaction)\n", "nn_pairs = []\n", "for x in range(Nx):\n", "    for y in range(Ny):\n", "        if x < Nx - 1:\n", "            nn_pairs.append((x + y * Nx, (x + 1) + y * Nx))\n", "        if y < Ny - 1:\n", "            nn_pairs.append((x + y * Nx, x + (y + 1) * Nx))\n", "\n", "for i, j in nn_pairs:\n", "    for S in [Sx, Sy, Sz]:\n", "        H += J1 * build_interaction(i, j, S)\n", "\n", "# J2 term (next-nearest neighbor interaction)\n", "nnn_pairs = []\n", "for x in range(Nx):\n", "    for y in range(Ny):\n", "        if x < Nx - 1 and y < Ny - 1:\n", "            nnn_pairs.append((x + y * Nx, (x + 1) + (y + 1) * Nx))\n", "        if x > 0 and y < Ny - 1:\n", "            nnn_pairs.append((x + y * Nx, (x - 1) + (y + 1) * Nx))\n", "\n", "for i, j in nnn_pairs:\n", "    for S in [Sx, Sy, Sz]:\n", "        H += J2 * build_interaction(i, j, S)\n", "\n", "# Solve for the ground state\n", "E, V = np.linalg.eigh(H)\n", "ground_state = V[:, 0]\n", "print(f\"Ground state energy: {E[0]/N:.6f}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact ground-state energy E0 =  -0.5278763469892256\n"]}], "source": ["import netket as nk\n", "import numpy as np\n", "\n", "L = 4  # 晶格长度\n", "N = L * L  # 晶格中的格点数\n", "J2 = 0.505  # 下一个最近邻相互作用的耦合常数\n", "\n", "# 创建2D晶格\n", "lattice = nk.graph.Grid(extent=[L, L], pbc=[True, True], max_neighbor_order=2)\n", "\n", "# 创建<PERSON><PERSON>空间\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)\n", "\n", "# 创建Heisenberg自旋哈密顿量\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])\n", "\n", "# 使用Lanczos算法计算哈密顿量的本征值和本征向量\n", "# evals, evecs = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=True)\n", "evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)\n", "\n", "# 输出基态能量\n", "print('Exact ground-state energy E0 = ', evals[0] / (4*N))  # 基态能量除以四是因为netket里用的泡利矩，缺少1/2常数，两个相互作用就是1/4"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}