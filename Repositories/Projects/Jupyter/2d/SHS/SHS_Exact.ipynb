{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Exact ground-state energy E0 =  -7.425772452645837\n"]}], "source": ["import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 定义Shastry-<PERSON>晶格\n", "Lx = 2\n", "Ly = 2\n", "\n", "N=4*Lx*Ly\n", "\n", "J1 = 0.8\n", "J2 = 1.0\n", "h = 0.0\n", "\n", "# 定义自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建Shastry-<PERSON>晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 绘制并可视化晶格图\n", "lattice.draw(edge_color='red', curvature=0.1)\n", "plt.show()\n", "\n", "# 定义<PERSON><PERSON>空间，自旋基和总磁化为零的扇区\n", "hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 定义自旋-1/2矩阵\n", "sigmax = [[0, 0.5], [0.5, 0]]\n", "sigmay = [[0, -0.5j], [0.5j, 0]]\n", "sigmaz = [[0.5, 0], [0, -0.5]]\n", "unitm = [[1.0, 0.0], [0.0, 1.0]]\n", "\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "\n", "SiSj = sxsx + sysy + szsz\n", "\n", "\n", "# 定义键和位点算符\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "\n", "bond_color = [0, 1]\n", "\n", "site_operator = [(-h * np.array(sigmaz)).tolist()]\n", "\n", "# 构建Hamiltonian\n", "H = nk.operator.GraphOperator(hi, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color, site_ops=site_operator)\n", "\n", "\n", "\n", "# 使用Lanczos算法计算哈密顿量的本征值和本征向量\n", "# evals, evecs = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=True)\n", "evals = nk.exact.lanczos_ed(H, compute_eigenvectors=False)\n", "\n", "# 输出基态能量\n", "print('Exact ground-state energy E0 = ', evals[0])  # 基态能量除以四是因为netket里用的泡利矩，缺少1/2常数，两个相互作用就是1/4"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}