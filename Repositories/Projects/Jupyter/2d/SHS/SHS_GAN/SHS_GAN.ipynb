{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available devices: [CudaDevice(id=0), CudaDevice(id=1)]\n"]}, {"ename": "TypeError", "evalue": "cannot unpack non-iterable function object", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 134\u001b[0m\n\u001b[1;32m    129\u001b[0m \u001b[38;5;66;03m#####################################\u001b[39;00m\n\u001b[1;32m    130\u001b[0m \u001b[38;5;66;03m# 构造联合变分态 vqs_joint\u001b[39;00m\n\u001b[1;32m    131\u001b[0m \u001b[38;5;66;03m#####################################\u001b[39;00m\n\u001b[1;32m    132\u001b[0m sampler_joint \u001b[38;5;241m=\u001b[39m nk\u001b[38;5;241m.\u001b[39msampler\u001b[38;5;241m.\u001b[39mMetropolisExchange(hilbert\u001b[38;5;241m=\u001b[39mhi, graph\u001b[38;5;241m=\u001b[39mlattice,\n\u001b[1;32m    133\u001b[0m                                               n_chains\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m12\u001b[39m, d_max\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n\u001b[0;32m--> 134\u001b[0m vqs_joint \u001b[38;5;241m=\u001b[39m \u001b[43mnk\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvqs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mMCState\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    135\u001b[0m \u001b[43m    \u001b[49m\u001b[43msampler\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msampler_joint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    136\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel_joint\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mparams\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams_joint\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    137\u001b[0m \u001b[43m    \u001b[49m\u001b[43mn_samples\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m12\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    138\u001b[0m \u001b[43m    \u001b[49m\u001b[43mn_samples_per_rank\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    139\u001b[0m \u001b[43m    \u001b[49m\u001b[43mn_discard_per_chain\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    140\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunk_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m8\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    141\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtraining_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mholomorphic\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m}\u001b[49m\n\u001b[1;32m    142\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m    143\u001b[0m \u001b[38;5;66;03m# 注意：vqs_joint 保存的是联合模型参数\u001b[39;00m\n\u001b[1;32m    144\u001b[0m \n\u001b[1;32m    145\u001b[0m \u001b[38;5;66;03m#########################################\u001b[39;00m\n\u001b[1;32m    146\u001b[0m \u001b[38;5;66;03m# 定义联合网络损失与局部耦合损失\u001b[39;00m\n\u001b[1;32m    147\u001b[0m \u001b[38;5;66;03m#########################################\u001b[39;00m\n\u001b[1;32m    148\u001b[0m gamma \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.1\u001b[39m           \u001b[38;5;66;03m# coupling strength\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:217\u001b[0m, in \u001b[0;36mMCState.__init__\u001b[0;34m(self, sampler, model, n_samples, n_samples_per_rank, n_discard_per_chain, chunk_size, variables, init_fun, apply_fun, seed, sampler_seed, mutable, training_kwargs)\u001b[0m\n\u001b[1;32m    211\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m model \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    212\u001b[0m     \u001b[38;5;66;03m# extract init and apply functions\u001b[39;00m\n\u001b[1;32m    213\u001b[0m     \u001b[38;5;66;03m# Wrap it in an HashablePartial because if two instances of the same model are provided,\u001b[39;00m\n\u001b[1;32m    214\u001b[0m     \u001b[38;5;66;03m# model.apply and model2.apply will be different methods forcing recompilation, but\u001b[39;00m\n\u001b[1;32m    215\u001b[0m     \u001b[38;5;66;03m# model and model2 will have the same hash.\u001b[39;00m\n\u001b[1;32m    216\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_model_framework \u001b[38;5;241m=\u001b[39m model_frameworks\u001b[38;5;241m.\u001b[39midentify_framework(model)\n\u001b[0;32m--> 217\u001b[0m     _maybe_unwrapped_variables, model \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_model_framework\u001b[38;5;241m.\u001b[39mwrap(model)\n\u001b[1;32m    219\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m variables \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    220\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m _maybe_unwrapped_variables \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[0;31mTypeError\u001b[0m: cannot unpack non-iterable function object"]}], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\n", "import os\n", "import jax\n", "import jax.numpy as jnp\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from jax import grad, jit, value_and_grad\n", "from functools import partial\n", "from tqdm.notebook import tqdm\n", "import netket as nk\n", "import flax.linen as nn\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '1'\n", "print(\"Available devices:\", jax.devices())\n", "\n", "###########################\n", "# 构造物理晶格和 Hilbert 空间\n", "###########################\n", "Lx = 3\n", "Ly = 3\n", "# 注意：custom_edges 格式为 (i, j, distance_vector, color)\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "# 构建晶格，其中 custom_edges 参数必须为数值型\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors = [[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "# 将自定义边信息保存到其他属性（避免覆盖内部 edges 方法）\n", "lattice.edge_info = custom_edges\n", "\n", "# Hilbert 空间，定义自旋1/2和总磁量为0的空间\n", "hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "#####################################\n", "# 构造联合神经网络：model_joint\n", "#####################################\n", "class JointNet(nn.Module):\n", "    d_model: int\n", "    num_layers: int\n", "    patch_size: int = 1\n", "    n_sites: int = 4\n", "    \n", "    @nn.compact\n", "    def __call__(self, x):\n", "        # 将输入 x reshape 成 [batch, n_sites, 1]\n", "        x = x.reshape(x.shape[0], self.n_sites, 1)\n", "        x = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                     dtype=jnp.float64)(x)\n", "        # 多层简单残差结构\n", "        for _ in range(self.num_layers):\n", "            x_ln = nn.LayerNorm(dtype=jnp.float64)(x)\n", "            x = x + nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                             dtype=jnp.float64)(x_ln)\n", "        # 输出 Dense 层映射到 2 个通道：\n", "        # 通道 0 对应 J1, 通道 1 对应 J2\n", "        x = nn.Dense(2, kernel_init=nn.initializers.xavier_uniform(),\n", "                     dtype=jnp.float64)(x)\n", "        return x  # 输出 shape 为 [batch, n_sites, 2]\n", "\n", "# 初始化联合模型\n", "d_model = 48\n", "num_layers = 4\n", "model_joint = JointNet(d_model=d_model, num_layers=num_layers, n_sites=lattice.n_nodes)\n", "dummy_input = jnp.zeros((1, lattice.n_nodes))\n", "key = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "params_joint = model_joint.init(key, dummy_input)[\"params\"]\n", "\n", "#########################################\n", "# 构造哈密顿量 (J1, J2 部分及完整系统)\n", "#########################################\n", "# 构造自旋-1/2算子\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# J1 部分哈密顿量\n", "bond_operator_J1 = [(0.8 * SiSj).tolist()]\n", "bond_color_J1 = [0]\n", "H_J1 = nk.operator.GraphOperator(hi, graph=lattice,\n", "                                 bond_ops=bond_operator_J1,\n", "                                 bond_ops_colors=bond_color_J1)\n", "\n", "# J2 部分哈密顿量\n", "bond_operator_J2 = [(1.0 * SiSj).tolist()]\n", "bond_color_J2 = [1]\n", "H_J2 = nk.operator.GraphOperator(hi, graph=lattice,\n", "                                 bond_ops=bond_operator_J2,\n", "                                 bond_ops_colors=bond_color_J2)\n", "\n", "# 完整系统哈密顿量（合并 J1 和 J2）\n", "bond_operator = [\n", "    (0.8 * SiSj).tolist(),\n", "    (1.0 * SiSj).tolist()\n", "]\n", "bond_color = [0, 1]\n", "H_full = nk.operator.GraphOperator(hi, graph=lattice,\n", "                                   bond_ops=bond_operator,\n", "                                   bond_ops_colors=bond_color)\n", "\n", "# 转换为 JAX operator\n", "ha_J1 = H_J1.to_jax_operator()\n", "ha_J2 = H_J2.to_jax_operator()\n", "ha_full = H_full.to_jax_operator()\n", "\n", "#####################################\n", "# 构造联合变分态 vqs_joint\n", "#####################################\n", "sampler_joint = nk.sampler.MetropolisExchange(hilbert=hi, graph=lattice,\n", "                                              n_chains=2**12, d_max=2)\n", "vqs_joint = nk.vqs.MCState(\n", "    sampler=sampler_joint,\n", "    model=lambda x: model_joint.apply({\"params\": params_joint}, x),\n", "    n_samples=2**12,\n", "    n_samples_per_rank=None,\n", "    n_discard_per_chain=0,\n", "    chunk_size=2**8,\n", "    training_kwargs={\"holomorphic\": False}\n", ")\n", "# 注意：vqs_joint 保存的是联合模型参数\n", "\n", "#########################################\n", "# 定义联合网络损失与局部耦合损失\n", "#########################################\n", "gamma = 0.1           # coupling strength\n", "target_coupling = 0.0 # 目标局部交叉相关（根据物理要求调整）\n", "\n", "# 联合模型调用接口\n", "def joint_model_apply(params, samples):\n", "    return model_joint.apply({\"params\": params}, samples)\n", "\n", "# 定义局部耦合损失函数\n", "def local_coupling_loss(outputs, lattice):\n", "    # outputs shape 为 [batch, n_sites, 2]\n", "    loss = 0.0\n", "    count = 0\n", "    # 这里使用 lattice.edge_info 而非 lattice.edges\n", "    for (i, j, _) in lattice.edge_info:\n", "        psi1 = outputs[:, i, 0]\n", "        psi2 = outputs[:, j, 1]\n", "        psi1b = outputs[:, j, 0]\n", "        psi2b = outputs[:, i, 1]\n", "        local_err = jnp.mean((jnp.real(psi1 * jnp.conjugate(psi2)) - target_coupling)**2) \\\n", "                    + jnp.mean((jnp.real(psi1b * jnp.conjugate(psi2b)) - target_coupling)**2)\n", "        loss += local_err\n", "        count += 1\n", "    return loss / (count + 1e-8)\n", "\n", "# 定义联合 Loss 函数\n", "# 注意：下面 expect_branch 与 expect_state 接口为伪代码，需要根据实际情况实现\n", "def joint_loss(params, samples, lattice):\n", "    outputs = joint_model_apply(params, samples)  # 输出 shape [batch, n_sites, 2]\n", "    psi_J1 = outputs[..., 0]\n", "    psi_J2 = outputs[..., 1]\n", "    # 伪接口：请根据 NetKet 实现期望能量的接口\n", "    E_J1_obj = vqs_joint.expect_branch(psi_J1, ha_J1)  # 返回含 .mean 属性的对象\n", "    E_J2_obj = vqs_joint.expect_branch(psi_J2, ha_J2)\n", "    L_coupling = local_coupling_loss(outputs, lattice)\n", "    loss1 = E_J1_obj.mean + gamma * L_coupling\n", "    loss2 = E_J2_obj.mean + gamma * L_coupling\n", "    total_loss = loss1 + loss2\n", "    return total_loss, (E_J1_obj.mean, E_J2_obj.mean, L_coupling)\n", "\n", "# jit 加速联合梯度计算\n", "joint_loss_and_grad = jit(value_and_grad(joint_loss, has_aux=True))\n", "\n", "#########################################\n", "# 定义联合优化器及 QuantumGAN 类\n", "#########################################\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "class QuantumGAN:\n", "    def __init__(self, vqs_joint, ha_J1, ha_J2, ha_full, optimizer, lattice,\n", "                 diag_shift=0.01, temperature=1.0, reference_energy=-16.2618):\n", "        self.vqs_joint = vqs_joint\n", "        self.ha_J1 = ha_J1\n", "        self.ha_J2 = ha_J2\n", "        self.ha_full = ha_full\n", "        self.optimizer = optimizer\n", "        self.lattice = lattice\n", "        self.diag_shift = diag_shift\n", "        self.temperature = temperature\n", "        self.init_temperature = temperature\n", "        self.reference_energy = reference_energy\n", "        \n", "        self.energy_history = []\n", "        self.J1_energy_history = []\n", "        self.J2_energy_history = []\n", "        self.coupling_history = []\n", "    \n", "    def _update_temperature(self, iteration):\n", "        self.temperature = self.init_temperature * (jnp.exp(-iteration / 50.0) / 2.0)\n", "        return self.temperature\n", "    \n", "    def _evaluate_full_energy(self):\n", "        # 使用联合模型输出两个通道合成的波函数计算完整哈密顿量的期望\n", "        outputs = joint_model_apply(self.vqs_joint.parameters, self.vqs_joint.samples)\n", "        psi = (outputs[...,0] + outputs[...,1]) / 2.0\n", "        E_full_obj = vqs_joint.expect_state(psi, self.ha_full)\n", "        return E_full_obj.mean\n", "    \n", "    def run(self, n_iter):\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Quantum GAN Training: Lattice extent {self.lattice.extent}\")\n", "        params = self.vqs_joint.parameters\n", "        \n", "        for i in range(n_iter):\n", "            self._update_temperature(i)\n", "            samples = self.vqs_joint.samples\n", "            (loss, (E1, E2, Lcoup)), grads = joint_loss_and_grad(params, samples, self.lattice)\n", "            params = self.optimizer.update(grads, params)\n", "            self.vqs_joint.replace(parameters=params)\n", "            \n", "            self.energy_history.append(np.real(self._evaluate_full_energy()))\n", "            self.J1_energy_history.append(np.real(E1))\n", "            self.J2_energy_history.append(np.real(E2))\n", "            self.coupling_history.append(np.real(Lcoup))\n", "            \n", "            relative_error = abs((np.real(self.energy_history[-1]) - self.reference_energy) / self.reference_energy) * 100\n", "            outer_pbar.set_postfix({\n", "                'E_full': f'{np.real(self.energy_history[-1]):.6f}',\n", "                'J1_E': f'{np.real(E1):.6f}',\n", "                'J2_E': f'{np.real(E2):.6f}',\n", "                'Coupling': f'{np.real(Lcoup):.6f}',\n", "                'RelErr(%)': f'{relative_error:.4f}'\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "\n", "#########################################\n", "# 初始化并运行 QuantumGAN 优化\n", "#########################################\n", "quantum_gan = QuantumGAN(\n", "    vqs_joint = vqs_joint,\n", "    ha_J1 = ha_J1,\n", "    ha_J2 = ha_J2,\n", "    ha_full = ha_full,\n", "    optimizer = optimizer,\n", "    lattice = lattice,\n", "    diag_shift = 0.01,\n", "    temperature = 1.0,\n", "    reference_energy = -16.2618\n", ")\n", "\n", "import time\n", "start = time.time()\n", "quantum_gan.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"Total time: {end - start:.2f} sec\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}