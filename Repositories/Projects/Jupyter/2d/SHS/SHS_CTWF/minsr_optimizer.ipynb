{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import grad, vmap\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "\n", "\n", "# 假设你的模型对象为 model，且其 .apply 方法接收 {\"params\": params} 以及输入样本\n", "# 下面计算单个样本下的log(ψ)梯度，返回展平后的梯度向量（形状为 (Np,)）\n", "def compute_log_derivative(params, sample):\n", "    # 这里定义 log_psi，为避免log(0)加一个小常数\n", "    def log_psi(p, x):\n", "        psi = model.apply({\"params\": p}, x)  # 模型输出复数\n", "        return jnp.log(jnp.abs(psi) + 1e-12)\n", "    flat_grad, _ = jax.flatten_util.ravel_pytree(grad(log_psi)(params, sample))\n", "    return flat_grad\n", "\n", "# 批量计算log-derivatives，返回矩阵 O 形状 (N_samples, Np)\n", "def compute_log_derivative_batch(params, samples):\n", "    return vmap(lambda x: compute_log_derivative(params, x))(samples)\n", "\n", "# 定义MinSR驱动（继承自 VMC_SRt），实现公式 δθ = O† · (T⁻¹ · ϵ)，T = O_centered · O_centered†\n", "class MinSR_VMC(VMC_SRt):\n", "    def __init__(self, reference_energy, delta_tau, **kwargs):\n", "        \"\"\"\n", "        delta_tau: imaginary-time步长，用于构造ϵ = -delta_tau*(Eloc-<E>)/sqrt(N_samples)\n", "        其他参数传递给父类VMC_SRt\n", "        \"\"\"\n", "        super().__init__(**kwargs)\n", "        self.delta_tau = delta_tau\n", "        self.reference_energy = reference_energy\n", "\n", "    def _step_with_state(self, state):\n", "        # state.parameters: 当前模型参数（PyTree格式）\n", "        # state.samples: 一批 Monte Carlo 采样，形状 (N_samples, ...)\n", "        # 假设 state.energy 提供局部能量 state.energy.local (形状 (N_samples,))\n", "        # 以及能量均值 state.energy.mean\n", "        \n", "        N_samples = state.samples.shape[0]\n", "        # 计算 log-derivatives 矩阵 O，形状 (N_samples, Np)\n", "        O = compute_log_derivative_batch(state.parameters, state.samples)\n", "        # 中心化：每列减去均值\n", "        O_mean = jnp.mean(O, axis=0, keepdims=True)\n", "        O_centered = O - O_mean\n", "\n", "        # 构造 ϵ = -delta_tau*(Eloc - <E>)/sqrt(N_samples)\n", "        eps = - self.delta_tau * (state.energy.local - state.energy.mean) / jnp.sqrt(N_samples)\n", "\n", "        # 构造 T = O_centered · O_centered†，形状 (N_samples, N_samples)\n", "        T = O_centered @ jnp.conjugate(O_centered.T)\n", "        # 计算 T 的伪逆（可根据需要调整 rcond 参数，此处设为 1e-12 ）\n", "        T_inv = jnp.linalg.pinv(T, rcond=1e-12)\n", "        \n", "        # 计算更新向量 δθ = O_centered† · (T⁻¹ · eps)\n", "        delta_theta = jnp.conjugate(O_centered.T) @ (T_inv @ eps)  # 形状 (Np,)\n", "\n", "        # 更新参数：此处使用预先设置的优化器（如 Sgd）\n", "        new_params = self.optimizer.update(delta_theta, state.parameters)\n", "        new_state = state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"\n", "        运行优化，并使用 tqdm 进度条显示当前进度，\n", "        显示内容包括迭代次数、当前能量均值和能量误差等信息。\n", "        \"\"\"\n", "        pbar = tqdm(total=n_iter, desc=\"MinSR Optimization\", leave=True)\n", "        for i in range(n_iter):\n", "            self.advance(1)\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_err  = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "            pbar.set_postfix({\n", "                'Energy': f'{energy_mean:.6f}',\n", "                'E_err': f'{energy_err:.6f}',\n", "                'E_var': f'{energy_var:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            pbar.update(1)\n", "        pbar.close()\n", "        return self\n", "\n", "# ----- 以下为示例调用 -----\n", "# 假设已有以下变量：\n", "# ha: 已定义的哈密顿量（JAX operator 格式）\n", "# optimizer: 已定义的优化器（例如 nk.optimizer.Sgd(learning_rate=0.05)）\n", "# vqs: 通过 nk.vqs.MCState 构造的变分量子态\n", "# model: 你的 neural network 模型（MinSR驱动内部需要调用 model.apply）\n", "reference_energy = -16.2618\n", "delta_tau = 1e-3  # 设定 imaginary-time 步长，根据需要调整\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "minsr_driver = MinSR_VMC(\n", "    delta_tau=delta_tau,\n", "    reference_energy=reference_energy,\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=0.01,\n", "    variational_state=vqs\n", ")\n", "\n", "start_time = time.time()\n", "minsr_driver.run(n_iter=1000)\n", "end_time = time.time()\n", "print(f\"MinSR Optimization time: {end_time - start_time:.2f} seconds\")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}