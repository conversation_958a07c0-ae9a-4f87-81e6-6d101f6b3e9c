{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： False\n", "可用设备： [CudaDevice(id=0)]\n"]}], "source": ["# %%\n", "import os\n", "import jax\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '0'\n", "\n", "import netket as nk\n", "import jax\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# %%\n", "import os\n", "import logging\n", "import sys\n", "import jax\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from functools import partial\n", "from netket.nn import log_cosh\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator\n", "from netket.vqs import VariationalState\n", "from scipy import sparse as _sparse\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.operator import LocalOperator as _LocalOperator\n", "from tqdm.notebook import tqdm\n", "from jax import tree"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-15 11:21:43.818100: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-04-15 11:21:43.818142: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n"]}], "source": ["# 设置模型和哈密顿参数\n", "N_features = 4\n", "N_layers = 4\n", "J1 = 0.8\n", "J2 = 1.0\n", "Q = 1.0 - J2\n", "Lx = 3\n", "Ly = 3\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "lattice.draw()\n", "\n", "# %%\n", "# Hilbert空间定义\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# 定义(Si·Sj - 1/4)算符\n", "ProjOp = jnp.array(SiSj) - 0.25 * jnp.array(umum)\n", "ProjOp2 = jnp.kron(ProjOp, ProjOp)\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hilbert, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hilbert, dtype=jnp.complex128)\n", "\n", "# 获取晶格尺寸\n", "Lx, Ly = lattice.extent[0], lattice.extent[1]\n", "\n", "# 遍历所有单元格\n", "for x in range(Lx):\n", "    for y in range(Ly):\n", "        # 计算当前单元格的基本索引\n", "        base = 4 * (y + x * Ly)\n", "        \n", "        # 当前单元格内的四个格点\n", "        site0 = base      # 左下角 (0.5, 0.5)\n", "        site1 = base + 1  # 右下角 (1.5, 0.5)\n", "        site2 = base + 2  # 右上角 (1.5, 1.5)\n", "        site3 = base + 3  # 左上角 (0.5, 1.5)\n", "        \n", "        # 找到相邻单元格（考虑周期性边界条件）\n", "        right_x = (x + 1) % Lx\n", "        right_base = 4 * (y + right_x * Ly)\n", "        \n", "        left_x = (x - 1 + Lx) % Lx\n", "        left_base = 4 * (y + left_x * Ly)\n", "        \n", "        up_y = (y + 1) % Ly\n", "        up_base = 4 * (up_y + x * Ly)\n", "        \n", "        down_y = (y - 1 + Ly) % Ly\n", "        down_base = 4 * (down_y + x * Ly)\n", "        \n", "        # 1. 单元格内部的水平方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site1, site3, site2]])\n", "        \n", "        # 2. 单元格内部的垂直方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site3, site1, site2]])\n", "        \n", "        # 3. 与右侧单元格形成的水平plaquette（处理x方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site1, right_base, site2, right_base + 3]])\n", "        \n", "        # 4. 与上方单元格形成的垂直plaquette（处理y方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site3, up_base, site2, up_base + 1]])\n", "        \n", "        # # 5. 与左侧单元格形成的水平plaquette（处理x方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, left_base + 1, site3, left_base + 2]])\n", "        \n", "        # # 6. 与下方单元格形成的垂直plaquette（处理y方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, down_base + 3, site1, down_base + 2]])\n", "\n", "# 合并两部分哈密顿量\n", "hamiltonian = H_J + 2*H_Q\n", "hamiltonian = hamiltonian.to_jax_operator()\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# ----------------- 配置采样器 -----------------\n", "sampler = nk.sampler.MetropolisExchange(\n", "    hilbert=hilbert, \n", "    graph=lattice, \n", "    n_chains=2**12, \n", "    d_max=2)\n", "\n", "# ----------------- 定义局部集群和掩码 -----------------\n", "local_cluster = jnp.arange(Lx * Ly * 4).tolist()\n", "mask = jnp.zeros(lattice.n_nodes, dtype=bool)\n", "for i in local_cluster:\n", "    mask = mask.at[i].set(True)\n", "\n", "# ----------------- 定义晶格对称性 -----------------\n", "nc = 4\n", "cyclic_4 = PointGroup(\n", "    [Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],\n", "    ndim=2,\n", ")\n", "C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4\n", "symmetries = lattice.space_group(C4v)\n", "sgb = lattice.space_group_builder(point_group=C4v)\n", "\n", "momentum = [0.0, 0.0]\n", "chi = sgb.space_group_irreps(momentum)[0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["参数数量: 7084\n"]}], "source": ["# 配置群等变卷积神经网络(GCNN)模型\n", "model = nk.models.GCNN(\n", "    symmetries=symmetries,\n", "    layers=N_layers,\n", "    param_dtype=np.complex128,\n", "    features=N_features,\n", "    equal_amplitudes=False,\n", "    parity=1,\n", "    input_mask=mask,\n", "    characters=chi\n", ")\n", "\n", "# 初始化变分量子态\n", "vqs = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model,\n", "    n_samples=2**12,\n", "    n_discard_per_chain=0,\n", "    chunk_size=2**10,\n", ")\n", "\n", "# 设置优化参数\n", "n_ann = 2000  # 温度退火步数\n", "n_train = 1   # 每个温度的训练步数\n", "lr = 0.05     # 学习率\n", "temperature = 1.0  # 初始温度\n", "n_params = nk.jax.tree_size(vqs.parameters)\n", "\n", "print(f\"参数数量: {n_params}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import netket.optimizer as nk_opt\n", "\n", "optimizer = nk_opt.Sgd(learning_rate=0.05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import tree_util\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 定义熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # 记录初始温度，用于后续温度递减计算\n", "        self.init_temperature = temperature\n", "        self.temperature = temperature\n", "\n", "    def _step_with_state(self, state):\n", "        # 基础能量梯度更新步骤\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "        \n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "        \n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "        \n", "        # 更新参数\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "# 添加进度条以及温度递减方案\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"运行优化并在 tqdm 进度条中显示 Temperature，Energy，E_var，E_err 和 Rel_err(%)\"\"\"\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 更新温度：使用初始温度乘以递减因子\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0) / 2.0)\n", "            self.advance(1)\n", "\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}', \n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "\n", "\n", "temperature_original = 1.0  # 初始温度\n", "reference_energy = -16.2618\n", "\n", "vmc = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    temperature=temperature_original,\n", "    hamiltonian=hamiltonian,\n", "    optimizer=optimizer,\n", "    diag_shift=0.01,\n", "    variational_state=vqs\n", ")\n", "\n", "start = time.time()\n", "vmc.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"优化耗时: {end - start:.2f}秒\")\n", "# Lx=3, Ly=3: 100% 1000/1000 [24:22<00:00, 1.46s/it, Temp=0.0000, Energy=-14.404755+0.002588j, E_var=3.695185, E_err=0.030036, Rel_err(%)=11.4197]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAMWCAYAAAAgRDUeAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3gUVd/G8Xs3vZCEkkIg9CZSpUkTBCSAoojSlBJEHhAsgNgVsKJYHn2U1y5gx4oVEJCigAgiKFVAegskJiGB1J33jzFLlhTSyGTD93Nd4+7MnJ357W5mSG7PnLEZhmEIAAAAAAAAKEN2qwsAAAAAAADAxYdQCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAlIm5c+fKZrPlO/3yyy9Wl3jBpaam6r///a86dOig4OBg+fr6qlGjRrr99tv1119/WV0eAABAmfK0ugAAAHBxeeyxx1S3bt1cyxs0aGBBNWXn5MmT6tOnj3777Tddc801uummmxQYGKidO3fq448/1htvvKH09HSrywQAACgzhFIAAKBM9e3bV23btrW6DKWkpCggIKDM9hcTE6Pff/9dn332mW644QaXdY8//rgeeuihMqvlQjAMQ6mpqfLz87O6FAAA4Ca4fA8AAJQr+/btk81m03PPPac33nhD9evXl4+Pj9q1a6f169fnar9jxw7deOONqlKlinx9fdW2bVt9/fXXLm2yLx1cuXKlJkyYoLCwMNWsWdO5fvbs2apXr578/PzUvn17/fTTT+revbu6d+8uSUpOTlZAQIDuuuuuXPs/dOiQPDw8NHPmzHzf07p16/Tdd99pzJgxuQIpSfLx8dFzzz3nsuzHH39U165dFRAQoJCQEF133XXavn27S5sZM2bIZrNp9+7diomJUUhIiIKDgzV69GidPn3a2a5Zs2a68sorc+3X4XCoRo0auvHGG12Wvfjii7r00kvl6+ur8PBwjRs3Tv/884/La+vUqaNrrrlGixcvVtu2beXn56fXX39dkrR//35de+21CggIUFhYmCZPnqzFixfLZrNpxYoVuT6bPn36KDg4WP7+/urWrZtWr15drPeZ7f3331f79u3l7++vypUr64orrtAPP/zg0mbhwoXOz7dSpUq6+uqrtXXr1lzbAgAAFw6hFAAAKFOJiYk6efKkyxQXF5er3Ycffqhnn31W48aN0xNPPKF9+/Zp4MCBysjIcLbZunWrLr/8cm3fvl3333+/nn/+eQUEBGjAgAH68ssvc21zwoQJ2rZtm6ZNm6b7779fkvTqq6/q9ttvV82aNTVr1ix17dpVAwYM0KFDh5yvCwwM1PXXX6/58+crKyvLZZsfffSRDMPQzTffnO97zg7JRowYUajPaOnSpYqOjlZsbKxmzJihKVOmaM2aNercubP27duXq/3gwYN16tQpzZw5U4MHD9bcuXP16KOPOtcPGTJEq1at0rFjx1xe9/PPP+vIkSMaOnSoc9m4ceN0zz33qHPnznrppZc0evRoffDBB4qOjnb57CVp586dGjZsmK666iq99NJLatWqlVJSUtSjRw8tXbpUd955px566CGtWbNG9913X666f/zxR11xxRVKSkrS9OnT9dRTTykhIUE9evTQr7/+WuT3KUmPPvqoRowYIS8vLz322GN69NFHFRUVpR9//NHZ5r333tPVV1+twMBAPfPMM3rkkUe0bds2denSJc/PFwAAXCAGAABAGZgzZ44hKc/Jx8fH2W7v3r2GJKNq1apGfHy8c/lXX31lSDK++eYb57KePXsazZs3N1JTU53LHA6H0alTJ6Nhw4a59t2lSxcjMzPTuTwtLc2oWrWq0a5dOyMjI8O5fO7cuYYko1u3bs5lixcvNiQZCxcudHlfLVq0cGmXl+uvv96QZPzzzz/n/ZwMwzBatWplhIWFGXFxcc5lmzdvNux2uzFy5EjnsunTpxuSjFtuuSXX/qpWreqc37lzpyHJePnll13aTZgwwQgMDDROnz5tGIZh/PTTT4Yk44MPPnBpt2jRolzLa9eubUgyFi1a5NL2+eefNyQZCxYscC47c+aM0aRJE0OSsXz5csMwzO+pYcOGRnR0tOFwOJxtT58+bdStW9e46qqrivw+d+3aZdjtduP66683srKyXNpm7+PUqVNGSEiIMXbsWJf1x44dM4KDg3MtBwAAFw49pQAAQJmaPXu2lixZ4jItXLgwV7shQ4aocuXKzvmuXbtKkv7++29JUnx8vH788Udn75mcva6io6O1a9cuHT582GWbY8eOlYeHh3N+w4YNiouL09ixY+XpeXaozZtvvtll35LUq1cvRUZG6oMPPnAu27Jli/744w8NHz68wPeclJQkSapUqVKB7STp6NGj2rRpk2JiYlSlShXn8hYtWuiqq67S999/n+s148ePd5nv2rWr4uLinPtt1KiRWrVqpfnz5zvbZGVl6bPPPlP//v2d40B9+umnCg4O1lVXXeXSk61NmzYKDAzU8uXLXfZTt25dRUdHuyxbtGiRatSooWuvvda5zNfXV2PHjnVpt2nTJu3atUs33XST4uLinPtKSUlRz549tWrVKjkcjiK9zwULFsjhcGjatGmy211/zbXZbJKkJUuWKCEhQcOGDXN5jx4eHurQoUOu9wgAAC4cBjoHAABlqn379oUa6LxWrVou89khUfbYRrt375ZhGHrkkUf0yCOP5LmN2NhY1ahRwzl/7l3/9u/fLyn3nf88PT1Vp04dl2V2u10333yzXn31VZ0+fVr+/v764IMP5Ovrq0GDBhX4XoKCgiRJp06dUkhISIFts2tq3LhxrnWXXHKJFi9enGuQ9oI+q+x9DxkyRA8++KAOHz6sGjVqaMWKFYqNjdWQIUOcr9u1a5cSExMVFhaWZ22xsbEu83ndRXH//v2qX7++MwTKdu5nvGvXLknSqFGj8tyXZF7qmTMcPN/73LNnj+x2u5o2bZrvNrP326NHjzzXZ39eAADgwiOUAgAA5VLOHk05GYYhSc5eNFOnTs3VWyfbuUFISe8MN3LkSD377LNasGCBhg0bpg8//FDXXHONgoODC3xdkyZNJEl//vmns8dXaTrfZyWZodQDDzygTz/9VJMmTdInn3yi4OBg9enTx9nG4XAoLCzMpTdYTqGhoS7zJfk8s7+/Z599Vq1atcqzTWBgoMt8Yd5nYff73nvvKSIiItf6nD3mAADAhcW/ugAAwC3Vq1dPkuTl5aVevXoVaxu1a9eWZPa6ynl3uszMTO3bt08tWrRwad+sWTO1bt1aH3zwgWrWrKkDBw7o5ZdfPu9++vfvr5kzZ+r9998/byiVXdPOnTtzrduxY4eqVavm0kuqsOrWrav27dtr/vz5uv322/XFF19owIAB8vHxcbapX7++li5dqs6dOxc7cKpdu7a2bdsmwzBcekvt3r3bpV39+vUlmT2Tivv9nat+/fpyOBzatm1bvkFX9n7DwsJKbb8AAKB4GFMKAAC4pbCwMHXv3l2vv/66jh49mmv9iRMnzruNtm3bqmrVqnrzzTeVmZnpXP7BBx84LxM814gRI/TDDz/oxRdfVNWqVdW3b9/z7qdjx47q06eP3nrrLS1YsCDX+vT0dE2dOlWSVL16dbVq1Urz5s1TQkKCs82WLVv0ww8/qF+/fufdX36GDBmiX375Re+8845OnjzpcumeZN7dLisrS48//niu12ZmZrrUk5/o6GgdPnzYecdBSUpNTdWbb77p0q5NmzaqX7++nnvuOSUnJ+faTmG+v3MNGDBAdrtdjz32WK7xqLJ7U0VHRysoKEhPPfVUrrsJFne/AACgeOgpBQAAytTChQu1Y8eOXMs7derk7P1UWLNnz1aXLl3UvHlzjR07VvXq1dPx48e1du1aHTp0SJs3by7w9d7e3poxY4buuOMO9ejRQ4MHD9a+ffs0d+7cPMdFkqSbbrpJ9957r7788kvddttt8vLyKlSt7777rnr37q2BAweqf//+6tmzpwICArRr1y59/PHHOnr0qJ577jlJ5iVtffv2VceOHTVmzBidOXNGL7/8soKDgzVjxowifUY5DR48WFOnTtXUqVNVpUqVXD2FunXrpnHjxmnmzJnatGmTevfuLS8vL+3atUuffvqpXnrpJd14440F7mPcuHF65ZVXNGzYMN11112qXr26c+wt6eyA43a7XW+99Zb69u2rSy+9VKNHj1aNGjV0+PBhLV++XEFBQfrmm2+K9P4aNGighx56SI8//ri6du2qgQMHysfHR+vXr1dkZKRmzpypoKAgvfrqqxoxYoQuu+wyDR06VKGhoTpw4IC+++47de7cWa+88kqR9gsAAIqHUAoAAJSpadOm5bl8zpw5RQ6lmjZtqg0bNujRRx/V3LlzFRcXp7CwMLVu3Trf/Zzr9ttvl2EYev755zV16lS1bNlSX3/9te68805nkJJTeHi4evfure+//14jRowodK2hoaFas2aN/u///k/z58/XQw89pPT0dNWuXVvXXnut7rrrLmfbXr16adGiRZo+fbqmTZsmLy8vdevWTc8880yeg4sXVs2aNdWpUyetXr1at956a56B2muvvaY2bdro9ddf14MPPugc9H348OHq3LnzefcRGBioH3/8UXfccYdeeuklBQYGauTIkerUqZNuuOEGl8+0e/fuWrt2rR5//HG98sorSk5OVkREhDp06KBx48YV6z0+9thjqlu3rl5++WU99NBD8vf3V4sWLVy+q5tuukmRkZF6+umn9eyzzyotLU01atRQ165dNXr06GLtFwAAFJ3NKMrIkAAAABcBh8Oh0NBQDRw4MNdlZ5J0/fXX688//8w1ThLy9+KLL2ry5Mk6dOiQyx0RAQDAxYsxpQAAwEUtNTU1193b3n33XcXHx6t79+652h89elTfffddkXpJXWzOnDnjMp+amqrXX39dDRs2JJACAABOXL4HAAAuar/88osmT56sQYMGqWrVqtq4caPefvttNWvWTIMGDXK227t3r1avXq233npLXl5exb687GIwcOBA1apVS61atVJiYqLef/997dixQx988IHVpQEAgHKEUAoAAFzU6tSpo6ioKP3vf/9TfHy8qlSpopEjR+rpp5+Wt7e3s93KlSs1evRo1apVS/PmzVNERISFVZdv0dHReuutt/TBBx8oKytLTZs21ccff5zrbn8AAODixphSAAAAAAAAKHOMKQUAAAAAAIAyRygFAAAAAACAMseYUgVwOBw6cuSIKlWqJJvNZnU5AAAAAAAA5Z5hGDp16pQiIyNlt+ffH4pQqgBHjhxRVFSU1WUAAAAAAAC4nYMHD6pmzZr5rnfbUGrmzJn64osvtGPHDvn5+alTp0565pln1LhxY2eb1NRU3X333fr444+Vlpam6Oho/d///Z/Cw8MLtY9KlSpJMj/EoKCgC/I+AAAAAAAAKpKkpCRFRUU5c5X8uO3d9/r06aOhQ4eqXbt2yszM1IMPPqgtW7Zo27ZtCggIkCTddttt+u677zR37lwFBwfr9ttvl91u1+rVqwu1j6SkJAUHBysxMZFQCgAAAAAAoBAKm6e4bSh1rhMnTigsLEwrV67UFVdcocTERIWGhurDDz/UjTfeKEnasWOHLrnkEq1du1aXX375ebdJKAUAAAAAAFA0hc1TKszd9xITEyVJVapUkST99ttvysjIUK9evZxtmjRpolq1amnt2rWW1AgAAAAAAACT244plZPD4dCkSZPUuXNnNWvWTJJ07NgxeXt7KyQkxKVteHi4jh07lud20tLSlJaW5pxPSkq6YDUDAAAAAABczCpEKDVx4kRt2bJFP//8c4m2M3PmTD366KOlVBUAAAAAoLzLyspSRkaG1WUAbsXLy0seHh4l3o7bh1K33367vv32W61atcrlNoMRERFKT09XQkKCS2+p48ePKyIiIs9tPfDAA5oyZYpzPnu0eAAAAABAxWIYho4dO6aEhASrSwHcUkhIiCIiImSz2Yq9DbcNpQzD0B133KEvv/xSK1asUN26dV3Wt2nTRl5eXlq2bJluuOEGSdLOnTt14MABdezYMc9t+vj4yMfH54LXDgAAAACwVnYgFRYWJn9//xL9YQ1cTAzD0OnTpxUbGytJql69erG35bah1MSJE/Xhhx/qq6++UqVKlZzjRAUHB8vPz0/BwcEaM2aMpkyZoipVqigoKEh33HGHOnbsWKg77wEAAAAAKqasrCxnIFW1alWrywHcjp+fnyQpNjZWYWFhxb6Uz21DqVdffVWS1L17d5flc+bMUUxMjCTpv//9r+x2u2644QalpaUpOjpa//d//1fGlQIAAAAAypPsMaT8/f0trgRwX9nHT0ZGxsUXShmGcd42vr6+mj17tmbPnl0GFQEAAAAA3AmX7AHFVxrHj70U6gAAAAAAAACKhFDqInD0qPTww9L//id99JG0dKn0xx9SfLzVlQEAAAAArFSnTh29+OKLF3w/3bt316RJky74fuBe3PbyPRTerl3Sk0/mva5JE+mKK6Ru3czHmjXLtjYAAAAAwPmd71Kp6dOna8aMGUXe7vr16xUQEFDMqkrP3LlzNXr06FzLfXx8lJqaakFFKAuEUheB0FBp/Hhp926zd1RCgvTPP+a0Y4c5vfGG2faWW8znxRyjDAAAAABwARw9etT5fP78+Zo2bZp27tzpXBYYGOh8bhiGsrKy5Ol5/j/5Q0NDS7fQEggKCnJ5T9KFH/crPT1d3t7eF3QfyB+X710ELrlEevVVackS6bffzHAqNlbat0967TXpppvMHlM2m/TOO9KgQdK/N6MAAAAAAJQDERERzik4OFg2m805v2PHDlWqVEkLFy5UmzZt5OPjo59//ll79uzRddddp/DwcAUGBqpdu3ZaunSpy3bPvXzPZrPprbfe0vXXXy9/f381bNhQX3/9tctrtmzZor59+yowMFDh4eEaMWKETp486VyfkpKikSNHKjAwUNWrV9fzzz9fqPeY8z1lT+Hh4c713bt315133ql7771XVapUUURERK7eYQkJCbr11lsVGhqqoKAg9ejRQ5s3b3aunzFjhlq1aqW33npLdevWla+vryRpx44d6tKli3x9fdW0aVMtXbpUNptNCxYskCT16NFDt99+u8u+Tpw4IW9vby1btqxQ7w+5EUpdhGw2ydNTql1bGjdO+uADaft26d13zR5SX34pXXutlJ5udaUAAAAAcOEZhpSSYs1UiBvLF9r999+vp59+Wtu3b1eLFi2UnJysfv36admyZfr999/Vp08f9e/fXwcOHChwO48++qgGDx6sP/74Q/369dPNN9+s+H8HJU5ISFCPHj3UunVrbdiwQYsWLdLx48c1ePBg5+vvuecerVy5Ul999ZV++OEHrVixQhs3biyV9zhv3jwFBARo3bp1mjVrlh577DEtWbLEuX7QoEGKjY3VwoUL9dtvv+myyy5Tz549nfVL0u7du/X555/riy++0KZNm5SVlaUBAwbI399f69at0xtvvKGHHnrIZb+33nqrPvzwQ6WlpTmXvf/++6pRo4Z69OhRKu/tomQgX4mJiYYkIzEx0epSyswXXxiGl5dhSIbRvbthnD5tdUUAAAAAULrOnDljbNu2zThz5oxhGIaRnGz+DWTFlJxc9PrnzJljBAcHO+eXL19uSDIWLFhw3tdeeumlxssvv+ycr127tvHf//7XOS/JePjhh53zycnJhiRj4cKFhmEYxuOPP2707t3bZZsHDx40JBk7d+40Tp06ZXh7exuffPKJc31cXJzh5+dn3HXXXQW+J0lGQECAy9SnTx9nm27duhldunRxeV27du2M++67zzAMw/jpp5+MoKAgIzU11aVN/fr1jddff90wDMOYPn264eXlZcTGxjrXL1y40PD09DSOHj3qXLZkyRJDkvHll18ahmH+zFSuXNmYP3++s02LFi2MGTNm5PueKrpzj6OcCpunMKYUXFx/vdlT6oYbpBUrpOhoaeVKs3cVAAAAAKD8atu2rct8cnKyZsyYoe+++05Hjx5VZmamzpw5c96eUi1atHA+DwgIUFBQkGJjYyVJmzdv1vLly13GsMq2Z88enTlzRunp6erQoYNzeZUqVdS4cePz1l+pUqVcPar8/PzyrU2Sqlev7lJbcnKyqlat6tLmzJkz2rNnj3O+du3aLmNp7dy5U1FRUYqIiHAua9++vcs2fH19NWLECL3zzjsaPHiwNm7cqC1btuS6tBFFQyiFXK6+Wvr2W/Pxp5+ktWulTp2srgoAAAAALgx/fyk52bp9l5Zz76I3depULVmyRM8995waNGggPz8/3XjjjUo/z1gtXl5eLvM2m00Oh0OSGXT1799fzzzzTK7XVa9eXbt37y52/Xa7XQ0aNChRbdWrV9eKFStyvS4kJMT5vLh3G7z11lvVqlUrHTp0SHPmzFGPHj1Uu3btYm0LJkIp5KlXL6l9e+nnn6XlywmlAAAAAFRcNptUzJyiXFu9erViYmJ0/fXXSzJDm3379pVom5dddpk+//xz1alTJ8+7+9WvX19eXl5at26datWqJUn6559/9Ndff6lbt24l2ndhajt27Jg8PT1Vp06dQr+ucePGOnjwoI4fP+4cWH39+vW52jVv3lxt27bVm2++qQ8//FCvvPJKaZV+0WKgc+Tr8svNx99/t7YOAAAAAEDRNWzY0DmY9+bNm3XTTTc5exUV18SJExUfH69hw4Zp/fr12rNnjxYvXqzRo0crKytLgYGBGjNmjO655x79+OOP2rJli2JiYmS3nz9+MAxDx44dyzUVtuZevXqpY8eOGjBggH744Qft27dPa9as0UMPPaQNGzbk+7qrrrpK9evX16hRo/THH39o9erVevjhhyWZPbFyuvXWW/X000/LMAxn2IfiI5RCvrIvod2yxdo6AAAAAABF98ILL6hy5crq1KmT+vfvr+joaF122WUl2mZkZKRWr16trKws9e7dW82bN9ekSZMUEhLiDJ6effZZde3aVf3791evXr3UpUsXtWnT5rzbTkpKUvXq1XNN2WNGnY/NZtP333+vK664QqNHj1ajRo00dOhQ7d+/39kDKi8eHh5asGCBkpOT1a5dO916663Ou+/5+vq6tB02bJg8PT01bNiwXOtQdDbDKM0bUFYsSUlJCg4OVmJiooKCgqwup8zt2yfVrSt5ekonT0rBwVZXBAAAAAAll5qaqr1796pu3boEC8jT6tWr1aVLF+3evVv169d3Lt+3b5/q16+v9evXlzjgc3cFHUeFzVMYUwr5ql1bqlbNDKR++km65hqrKwIAAAAAoPR9+eWXCgwMVMOGDbV7927ddddd6ty5szOQysjIUFxcnB5++GFdfvnlF30gVVq4fA/5stmkdu3M5z/9ZG0tAAAAAABcKKdOndLEiRPVpEkTxcTEqF27dvrqq6+c61evXq3q1atr/fr1eu211yystGKhpxQK1KGDtHCh9OefVlcCAAAAAMCFMXLkSI0cOTLf9d27dxejH5U+ekqhQNmDnW/dKnH8AQAAAACA0kIohQJlX7534IB05Ii1tQAAAAAAgIqDUAoFqlZNqlPHfL5ypaWlAAAAAACACoRQCueVfQnfmjXW1gEAAAAAACoOQimcV3YoxWDnAAAAAACgtBBK4byyQ6nt26WsLGtrAQAAAAAAFQOhFM7rsssku106cULaudPqagAAAAAAJbF69Wo1b95cXl5eGjBggNXl4CJGKIXzCgiQLrnEfM5g5wAAAABgjZiYGNlsNtlsNnl5ealu3bq69957lZqaWqTtTJkyRa1atdLevXs1d+7cUq1xxowZzhpzTk2aNCnV/ZQ3Tz75pDp16iR/f3+FhITk227u3Llq0aKFfH19FRYWpokTJ+bbNj4+XnfccYcaN24sPz8/1apVS3feeacSExOLtN3U1FTFxMSoefPm8vT0zDeI/OCDD9SyZUv5+/urevXquuWWWxQXF1foz6A4PC/o1lFhdOggbd0qrV8v3Xab1dUAAAAAwMWpT58+mjNnjjIyMvTbb79p1KhRstlseuaZZwq9jT179mj8+PGqWbNmsetIT0+Xt7d3nusuvfRSLV261GWZp+eFjR8KqqcspKena9CgQerYsaPefvvtPNu88MILev755/Xss8+qQ4cOSklJ0b59+/Ld5pEjR3TkyBE999xzatq0qfbv36/x48fryJEj+uyzzwq93aysLPn5+enOO+/U559/nue+Vq9erZEjR+q///2v+vfvr8OHD2v8+PEaO3asvvjii2J9JoVBTykUCoOdAwAAAID1fHx8FBERoaioKA0YMEC9evXSkiVLnOsdDodmzpypunXrys/PTy1btnQGGPv27ZPNZlNcXJxuueUW2Ww2Z0+pLVu2qG/fvgoMDFR4eLhGjBihkydPOrfbvXt33X777Zo0aZKqVaum6OjofGv09PRURESEy1StWjXn+jp16uipp57SLbfcokqVKqlWrVp64403XLZx8OBBDR48WCEhIapSpYquu+46l6AlJiZGAwYM0JNPPqnIyEg1btxYkrRmzRq1atVKvr6+atu2rRYsWCCbzaZNmzbJMAw1aNBAzz33nMu+Nm3aJJvNpt27dxfty8jh0Ucf1eTJk9W8efM81//zzz96+OGH9e677+qmm25S/fr11aJFC1177bX5brNZs2b6/PPP1b9/f9WvX189evTQk08+qW+++UaZmZmF3m5AQIBeffVVjR07VhEREXnua+3atapTp47uvPNO1a1bV126dNG4ceP066+/FvszKQxCKRRKdii1Y4dUxJ6hAAAAAOAeUlLyn879Q6igtmfOFK5tCW3ZskVr1qxx6SE0c+ZMvfvuu3rttde0detWTZ48WcOHD9fKlSsVFRWlo0ePKigoSC+++KKOHj2qIUOGKCEhQT169FDr1q21YcMGLVq0SMePH9fgwYNd9jdv3jx5e3tr9erVeu2110pU+/PPP6+2bdvq999/14QJE3Tbbbdp57+DGGdkZCg6OlqVKlXSTz/9pNWrVyswMFB9+vRRenq6cxvLli3Tzp07tWTJEn377bdKSkpS//791bx5c23cuFGPP/647rvvPmd7m82mW265RXPmzHGpZc6cObriiivUoEEDxcTEqHv37iV6b3lZsmSJHA6HDh8+rEsuuUQ1a9bU4MGDdfDgwSJtJzExUUFBQc6eZ6W13Y4dO+rgwYP6/vvvZRiGjh8/rs8++0z9+vUr0naKzEC+EhMTDUlGYmKi1aVYLj3dMHx9DUMyjF9+sboaAAAAACi+M2fOGNu2bTPOnDnjukLKf+rXz7Wtv3/+bbt1c21brVre7Ypo1KhRhoeHhxEQEGD4+PgYkgy73W589tlnhmEYRmpqquHv72+sWbPG5XVjxowxhg0b5pwPDg425syZ45x//PHHjd69e7u85uDBg4YkY+fOnYZhGEa3bt2M1q1bn7fG6dOnG3a73QgICHCZxo0b52xTu3ZtY/jw4c55h8NhhIWFGa+++qphGIbx3nvvGY0bNzYcDoezTVpamuHn52csXrzY+VmEh4cbaWlpzjavvvqqUbVqVZfv9c033zQkGb///rthGIZx+PBhw8PDw1i3bp1hGIaRnp5uVKtWzZg7d65hGIZx//33GyNGjDjv+8zPnDlzjODg4FzLZ86caXh5eRmNGzc2Fi1aZKxdu9bo2bOn0bhxY5f3UJATJ04YtWrVMh588MFib3fUqFHGddddl+f2P/nkEyMwMNDw9PQ0JBn9+/c30tPT860n3+PIKHyewphSKBQvL6llS2ndOmnFCnOMKQAAAABA2bryyiv16quvKiUlRf/973/l6empG264QZK0e/dunT59WldddZXLa9LT09W6det8t7l582YtX75cgYGBudbt2bNHjRo1kiS1adOmUDU2btxYX3/9tcuyoKAgl/kWLVo4n9tsNkVERCg2NtZZz+7du1WpUiWX16SmpmrPnj3O+ebNm7v0Etu5c6dzsO9s7bMv+/lXZGSkrr76ar3zzjtq3769vvnmG6WlpWnQoEGSzJ5mBRk/frzef/9953xycnKB7bM5HA5lZGTof//7n3r37i1J+uijjxQREaHly5cXeDmkJCUlJenqq69W06ZNNWPGjFLbbrZt27bprrvu0rRp0xQdHa2jR4/qnnvu0fjx4/MdI6s0uHUotWrVKj377LP67bffdPToUX355Zcuo8jHxMRo3rx5Lq+Jjo7WokWLyrjSiuGyy8xQ6t8elQAAAABQsRQUMHh4uM7/G6DkyX7OSDkFDGZdVAEBAWrQoIEk6Z133lHLli319ttva8yYMc6A5LvvvlONGjVcXufj45PvNpOTk9W/f/88B0uvXr26y74Lw9vb21ljfry8vFzmbTabHA6Hs542bdrogw8+yPW60NDQItdzrltvvVUjRozQf//7X82ZM0dDhgyRv79/oV772GOPaerUqUXeZ/bn2LRpU+ey0NBQVatWTQcOHCjwtadOnVKfPn1UqVIlffnlly6fXUm2m9PMmTPVuXNn3XPPPZLM0DAgIEBdu3bVE0884fJzUJrcOpRKSUlRy5Ytdcstt2jgwIF5tsm+M0G2gg5EFCw83HxMSLC0DAAAAAC4MIoSclyotkVgt9v14IMPasqUKbrpppvUtGlT+fj46MCBA+rWrVuht3PZZZfp888/V506dS74XfIKW8/8+fMVFhaWq4dVQRo3bqz3339faWlpzr/9169fn6tdv379nIN/L1q0SKtWrSr0PsLCwhQWFlbo9tk6d+4syezNlX3Xw/j4eJ08eVK1a9fO93VJSUmKjo6Wj4+Pvv76a5deYCXZ7rlOnz6d67v3+DeINQyj0NspKrce6Lxv37564okndP311+fbJvvOBNlT5cqVy7DCiqVKFfMxKcnaOgAAAAAApkGDBsnDw0OzZ89WpUqVNHXqVE2ePFnz5s3Tnj17tHHjRr388su5riLKaeLEiYqPj9ewYcO0fv167dmzR4sXL9bo0aOVlZVV5JoyMzN17Ngxl+n48eOFfv3NN9+satWq6brrrtNPP/2kvXv3asWKFbrzzjt16NChfF930003yeFw6D//+Y+2b9+uxYsXO++0Z7PZnO08PDwUExOjBx54QA0bNlTHjh2d6x544AGNHDmyyO/5wIED2rRpkw4cOKCsrCxt2rRJmzZtcvZea9Soka677jrdddddWrNmjbZs2aJRo0apSZMmuvLKKyVJhw8fVpMmTZx3vEtKSlLv3r2VkpKit99+W0lJSc7PM/t7Kcx2JfPyvE2bNik+Pl6JiYnO+rL1799fX3zxhV599VX9/fffWr16te688061b99ekZGRRf48Csv6CPQCW7FihcLCwlS5cmX16NFDTzzxhKpWrWp1WW4pO5RKTLS2DgAAAACAydPTU7fffrtmzZql2267TY8//rhCQ0M1c+ZM/f333woJCdFll12mBx98MN9tREZGavXq1brvvvvUu3dvpaWlqXbt2urTp4/s516KWAhbt27NdbmXj4+PUgt5K3d/f3+tWrVK9913nwYOHKhTp06pRo0a6tmzZ4E9p4KCgvTNN9/otttuU6tWrdS8eXNNmzZNN910U64eRmPGjNFTTz2l0aNHuyw/evRokS57yzZt2jSX4C97DK/ly5c77+b37rvvavLkybr66qtlt9vVrVs3LVq0yHk5XkZGhnbu3KnTp09LkjZu3Kh169ZJUq7LIffu3as6deoUaruS2Tts//79uerL7gUVExOjU6dO6ZVXXtHdd9+tkJAQ9ejRI89LOkuTzbiQ/bDKkM1myzWm1Mcffyx/f3/VrVtXe/bs0YMPPqjAwECtXbvW2Q0tp7S0NKWlpTnnk5KSFBUV5bzl4sVu4UKpXz+pUSPGlQIAAADgvlJTU7V3717VrVs3V1iBiuWDDz7Q6NGjlZiYKD8/P+fyn376ST179tTBgwcVnj1WDYqkoOMoKSlJwcHB581TKnRPqaFDhzqfN2/eXC1atFD9+vW1YsUK9ezZM1f7mTNn6tFHHy3LEt1Kzsv3HI7cY/cBAAAAAGCld999V/Xq1VONGjW0efNm3XfffRo8eLAzkEpLS9OJEyc0Y8YMDRo0iEDKYhdVrFCvXj1Vq1ZNu3fvznP9Aw88oMTEROd08ODBMq6wfMsZSmVkWFsLAAAAAADnOnbsmIYPH65LLrlEkydP1qBBg/TGG28413/00UeqXbu2EhISNGvWLAsrhVTBe0qd69ChQ4qLi8v3VoY+Pj7cna8A2aHU6dNSSorERwUAAAAAKE/uvfde3Xvvvfmuj4mJUUxMTNkVhAK5dSiVnJzs0utp79692rRpk6pUqaIqVaro0Ucf1Q033KCIiAjt2bNH9957rxo0aKDo6GgLq3ZfISFnnx87djakAgAAAAAAKCq3vnxvw4YNat26tXPU+ClTpqh169aaNm2aPDw89Mcff+jaa69Vo0aNNGbMGLVp00Y//fQTvaGKycPjbDAVG2tpKQAAAAAAwM25dU+p7t27q6CbBy5evLgMq7k4VK4sJSRIJ05YXQkAAAAAlIzD4bC6BMBtlcbx49ahFMpe1arS3r2EUgAAAADcl7e3t+x2u44cOaLQ0FB5e3vLZrNZXRbgFgzDUHp6uk6cOCG73S5vb+9ib4tQCkWSPY7UyZPW1gEAAAAAxWW321W3bl0dPXpUR44csbocwC35+/urVq1astuLPzIUoRSKJDuUSky0tg4AAAAAKAlvb2/VqlVLmZmZysrKsrocwK14eHjI09OzxD0MCaVQJNmhVFKStXUAAAAAQEnZbDZ5eXnJy8vL6lKAi5Jb330PZS87lEpIsLQMAAAAAADg5gilUCT0lAIAAAAAAKWBUApFQigFAAAAAABKA6EUioRQCgAAAAAAlAZCKRRJzlDK4bC2FgAAAAAA4L4IpVAkOUMp7poKAAAAAACKi1AKRZIdSp06JZ0+bW0tAAAAAADAfRFKoUgqVzYfDUM6ccLaWgAAAAAAgPsilEKReHtLgYHm8+PHra0FAAAAAAC4L0IpFFl2byl6SgEAAAAAgOIilEKRVa1qPsbGWlsHAAAAAABwX4RSKLLswc7j4qytAwAAAAAAuC9CKRRZdij1zz/W1gEAAAAAANwXoRSKLDuUSkqytg4AAAAAAOC+CKVQZNmhVGKitXUAAAAAAAD3RSiFIiOUAgAAAAAAJUUohSLLvvseoRQAAAAAACguQikUGWNKAQAAAACAkiKUQpHlDKUMw9paAAAAAACAeyKUQpFlh1KnTkmZmdbWAgAAAAAA3BOhFIosZ0+p9HRrawEAAAAAAO6JUApFVrmy+ZiVJZ08aW0tAAAAAADAPRFKocj8/MxJko4ft7YWAAAAAADgngilUCzZvaViY62tAwAAAAAAuCdCKRRL1arm44kT1tYBAAAAAADcE6EUiiV7sHPGlAIAAAAAAMXh1qHUqlWr1L9/f0VGRspms2nBggUu6w3D0LRp01S9enX5+fmpV69e2rVrlzXFVjDZoVR8vLV1AAAAAAAA9+TWoVRKSopatmyp2bNn57l+1qxZ+t///qfXXntN69atU0BAgKKjo5WamlrGlVY82aFUUpK1dQAAAAAAAPfkaXUBJdG3b1/17ds3z3WGYejFF1/Uww8/rOuuu06S9O677yo8PFwLFizQ0KFDy7LUCic7lEpMtLYOAAAAAADgnty6p1RB9u7dq2PHjqlXr17OZcHBwerQoYPWrl2b52vS0tKUlJTkMiFvhFIAAAAAAKAkKmwodezYMUlSeHi4y/Lw8HDnunPNnDlTwcHBzikqKuqC1+muuHwPAAAAAACURIUNpYrjgQceUGJionM6ePCg1SWVW4RSAAAAAACgJCpsKBURESFJOn78uMvy48ePO9edy8fHR0FBQS4T8pYzlDIMa2sBAAAAAADup8KGUnXr1lVERISWLVvmXJaUlKR169apY8eOFlZWMeQMpbKyrK0FAAAAAAC4H7e++15ycrJ2797tnN+7d682bdqkKlWqqFatWpo0aZKeeOIJNWzYUHXr1tUjjzyiyMhIDRgwwLqiK4icoVR6uuTp1j9JAAAAAACgrLl1lLBhwwZdeeWVzvkpU6ZIkkaNGqW5c+fq3nvvVUpKiv7zn/8oISFBXbp00aJFi+Tr62tVyRVGdiiVni4lJEj+/paWAwAAAAAA3IzNMBgRKD9JSUkKDg5WYmIi40udwzAkHx8pI0PauFFq3drqigAAAAAAQHlQ2Dylwo4phQvLZpMqVzafx8ZaWwsAAAAAAHA/hFIotqpVzUdCKQAAAAAAUFSEUii27HGlTp60tg4AAAAAAOB+CKVQbNmhVHy8tXUAAAAAAAD3QyiFYssOpRITra0DAAAAAAC4H0IpFBuhFAAAAAAAKC5CKRQboRQAAAAAACguQikUW3YolZRkbR0AAAAAAMD9EEqh2AilAAAAAABAcRFKodgIpQAAAAAAQHERSqHYcoZSWVnW1gIAAAAAANwLoRSKrVo18/Gff6T0dGtrAQAAAAAA7oVQCsUWEWE+pqdLx49bWwsAAAAAAHAvhFIoNl9fKTjYfL5vn6WlAAAAAAAAN0MohRKpXt18PHjQ2joAAAAAAIB7IZRCiWSHUocPW1sHAAAAAABwL4RSKJHsUIoxpQAAAAAAQFEQSqFEsgc7P3nS2joAAAAAAIB7IZRCiWT3lDpxwto6AAAAAACAeyGUQolkh1L0lAIAAAAAAEVBKIUSyb58Ly5OMgxrawEAAAAAAO6DUAolkt1TKi5OysiwthYAAAAAAOA+CKVQItk9pU6dkpKSrK0FAAAAAAC4D0IplEjlypKPj/l83z5LSwEAAAAAAG6EUAolYrNJ4eHm84MHra0FAAAAAAC4D0IplFj2JXyHDllbBwAAAAAAcB+EUiixyEjz8cgRa+sAAAAAAADug1AKJZbdUyouzto6AAAAAACA+yCUQolVr24+xsZaWwcAAAAAAHAfFTqUmjFjhmw2m8vUpEkTq8uqcLJDqZMnra0DAAAAAAC4D0+rC7jQLr30Ui1dutQ57+lZ4d9ymePyPQAAAAAAUFQVPqHx9PRURHZqggsiu6dUXJyUlSV5eFhbDwAAAAAAKP8q9OV7krRr1y5FRkaqXr16uvnmm3XgwAGrS6pwsjO/+HgpNdXaWgAAAAAAgHuo0KFUhw4dNHfuXC1atEivvvqq9u7dq65du+rUqVN5tk9LS1NSUpLLhPMLD5dsNrOX1KFDVlcDAAAAAADcQYUOpfr27atBgwapRYsWio6O1vfff6+EhAR98sknebafOXOmgoODnVNUVFQZV+yevLykqlXN5/v3W1sLAAAAAABwDxU6lDpXSEiIGjVqpN27d+e5/oEHHlBiYqJzOnjwYBlX6L6yL+HjIwMAAAAAAIVxUYVSycnJ2rNnj6pnj8x9Dh8fHwUFBblMKJzISPPx8GFr6wAAAAAAAO6hQodSU6dO1cqVK7Vv3z6tWbNG119/vTw8PDRs2DCrS6twsntKnThhbR0AAAAAAMA9eFpdwIV06NAhDRs2THFxcQoNDVWXLl30yy+/KDQ01OrSKpzszmcnT1pbBwAAAAAAcA8VOpT6+OOPrS7hopEdStFTCgAAAAAAFEaFvnwPZSf78r24OGvrAAAAAAAA7oFQCqUiu6dUXJxkGNbWAgAAAAAAyr9SCaVSUlJKYzNwYzl7SmVkWFsLAAAAAAAo/0ollAoPD9ctt9yin3/+uTQ2BzeU3VPq9GnGlQIAAAAAAOdXKqHU+++/r/j4ePXo0UONGjXS008/rSNHjpTGpuEmKlWSAgLM5/v3W1sLAAAAAAAo/0ollBowYIAWLFigw4cPa/z48frwww9Vu3ZtXXPNNfriiy+UmZlZGrtBORcebj4eOGBtHQAAAAAAoPwr1YHOQ0NDNWXKFP3xxx964YUXtHTpUt14442KjIzUtGnTdPr06dLcHcqZyEjz8fBha+sAAAAAAADln2dpbuz48eOaN2+e5s6dq/379+vGG2/UmDFjdOjQIT3zzDP65Zdf9MMPP5TmLlGOZA92fuyYtXUAAAAAAIDyr1RCqS+++EJz5szR4sWL1bRpU02YMEHDhw9XSEiIs02nTp10ySWXlMbuUE5lD3YeF2dtHQAAAAAAoPwrlVBq9OjRGjp0qFavXq127drl2SYyMlIPPfRQaewO5VR2KBUba20dAAAAAACg/CuVUOro0aPy9/cvsI2fn5+mT59eGrtDOZV9+d7Jk9bWAQAAAAAAyr9SCaUyMzOVlJSUa7nNZpOPj4+8vb1LYzco57J7SsXHW1sHAAAAAAAo/0ollAoJCZHNZst3fc2aNRUTE6Pp06fLbi/VG/6hHMnZUyozU/Is1WH0AQAAAABARVIqscHcuXP10EMPKSYmRu3bt5ck/frrr5o3b54efvhhnThxQs8995x8fHz04IMPlsYuUQ5l95RKSJBSUqTgYEvLAQAAAAAA5ViphFLz5s3T888/r8GDBzuX9e/fX82bN9frr7+uZcuWqVatWnryyScJpSqwatUkDw8pK0s6cEBq3tzqigAAAAAAQHlVKtfSrVmzRq1bt861vHXr1lq7dq0kqUuXLjpw4EBp7A7llIeHFBpqPt+/39paAAAAAABA+VYqoVRUVJTefvvtXMvffvttRUVFSZLi4uJUuXLl0tgdyrHISPPx4EFr6wAAAAAAAOVbqVy+99xzz2nQoEFauHCh2rVrJ0nasGGDduzYoc8++0yStH79eg0ZMqQ0dodyrFEjaeNG6fffra4EAAAAAACUZzbDMIzS2NC+ffv0+uuva+fOnZKkxo0ba9y4capTp05pbN4SSUlJCg4OVmJiooKCgqwuxy289ZY0dqw5ntQff1hdDQAAAAAAKGuFzVNK3FMqIyNDffr00WuvvaaZM2eWdHNwcz17mo/btknHj0vh4dbWAwAAAAAAyqcSjynl5eWlP+gSg3/VrSvVqWPege/7762uBgAAAAAAlFelMtD58OHD8xzoHBenXr3MxyVLrK0DAAAAAACUX6Uy0HlmZqbeeecdLV26VG3atFFAQIDL+hdeeKE0dgM3cdVV5thS69ZJhiHZbFZXBAAAAAAAyptSCaW2bNmiyy67TJL0119/uayzkUhcdK680nz8+29zql/f2noAAAAAAED5Uyqh1PLly0tjM6ggQkOlFi3Mu+99/bU0ebLVFQEAAAAAgPKmVMaUyrZ7924tXrxYZ86ckSQZhlGam4cbyR5XasUKS8sAAAAAAADlVKmEUnFxcerZs6caNWqkfv366ejRo5KkMWPG6O677y6NXcDNXHWV+bhunXknPgAAAAAAgJxKJZSaPHmyvLy8dODAAfn7+zuXDxkyRIsWLSqNXcDNdO0qeXlJx49LmzdbXQ0AAAAAAChvSiWU+uGHH/TMM8+oZs2aLssbNmyo/fv3l8Yu4GYCAqT27c3nX39tbS0AAAAAAKD8KZVQKiUlxaWHVLb4+Hj5+PiUxi5KZPbs2apTp458fX3VoUMH/frrr1aXdFHo3dt8/Plna+sAAAAAAADlT6mEUl27dtW7777rnLfZbHI4HJo1a5auvPLK0thFsc2fP19TpkzR9OnTtXHjRrVs2VLR0dGKjY21tK6LQc+e5uP69VJ6urW1AAAAAACA8sVmlMIt8rZs2aKePXvqsssu048//qhrr71WW7duVXx8vFavXq369euXRq3F0qFDB7Vr106vvPKKJMnhcCgqKkp33HGH7r///gJfm5SUpODgYCUmJiooKKgsyq1QMjKkypWllBRp2TKpRw+rKwIAAAAAABdaYfOUUukp1axZM/3111/q0qWLrrvuOqWkpGjgwIH6/fffLQ2k0tPT9dtvv6lXr17OZXa7Xb169dLatWstq+ti4eUlXXGF+fybb6ytBQAAAAAAlC+epbWh4OBgPfTQQ6W1uVJx8uRJZWVlKTw83GV5eHi4duzYkat9Wlqa0tLSnPNJSUkXvMaKrndvaeFCadUqqysBAAAAAADlSamFUgkJCfr1118VGxsrh8Phsm7kyJGltZsLaubMmXr00UetLqNCGTBAmjJF2rhR2rxZatnS6ooAAAAAAEB5UCqh1DfffKObb75ZycnJCgoKks1mc66z2WyWhVLVqlWTh4eHjh8/7rL8+PHjioiIyNX+gQce0JQpU5zzSUlJioqKuuB1VmR16pgDni9dKr30kvTOO1ZXBAAAAAAAyoNSGVPq7rvv1i233KLk5GQlJCTon3/+cU7x8fGlsYti8fb2Vps2bbRs2TLnMofDoWXLlqljx4652vv4+CgoKMhlQslNmGA+LlggnT5taSkAAAAAAKCcKJVQ6vDhw7rzzjvl7+9fGpsrVVOmTNGbb76pefPmafv27brtttuUkpKi0aNHW13aReOaa6TwcOmff6T33rO6GgAAAAAAUB6USigVHR2tDRs2lMamSt2QIUP03HPPadq0aWrVqpU2bdqkRYsW5Rr8HBeOl5d0yy3m87fftrYWAAAAAABQPtgMwzBKupG3335bjz32mEaPHq3mzZvLy8vLZf21115b0l1YIikpScHBwUpMTORSvhLat0+qV08yDOn336VWrayuCAAAAAAAXAiFzVNKJZSy2/PvcGWz2ZSVlVXSXViCUKp09e4tLVkijR7NgOcAAAAAAFRUhc1TSuXyPYfDke/kroEUSt9tt5mPDHgOAAAAAABKFEr169dPiYmJzvmnn35aCQkJzvm4uDg1bdq0JLtABcKA5wAAAAAAIFuJQqnFixcrLS3NOf/UU08pPj7eOZ+ZmamdO3eWZBeoQHIOeP7WW9bWAgAAAAAArFWiUOrc4ahKYXgqVHD/+Y9ks0kbNkgrVlhdDQAAAAAAsEqpjCkFFFadOtKgQebzsWOl1FRLywEAAAAAABYpUShls9lks9lyLQMK8sorUkiItHu39NBDVlcDAAAAAACs4FmSFxuGoZiYGPn4+EiSUlNTNX78eAUEBEiSy3hTQLbQUOmll6RRo6T//U8aOlRq187qqgAAAAAAQFmyGSUYCGr06NGFajdnzpzi7sJSSUlJCg4OVmJiooKCgqwup0IxDCk6WlqyRLr0Uun3382B0AEAAAAAgHsrbJ5SolCqoiOUurAOH5YaN5ZSUqRHHpEee8zqigAAAAAAQEkVNk9hoHNYpkYNadYs8/msWdKff1pbDwAAAAAAKDuEUrDUbbdJXbpIaWnm2FJnzlhdEQAAAAAAKAuEUrCUzSa9954UFCRt2yaNG2d1RQAAAAAAoCwQSsFydepIc+eaz997T3rrLSurAQAAAAAAZYFQCuXC9ddLkyebz++8U9q82dp6AAAAAADAhUUohXJj1izp8svNcaUGDpROnbK6IgAAAAAAcKEQSqHc8PSUvvhCqlpV+vtvacQIyTCsrgoAAAAAAFwIhFIoV6pXl+bPlzw8pK++kp580uqKAAAAAADAhUAohXKnZ0/p8cfN59OnSwsWWFoOAAAAAAC4AAilUC7df780eLDkcEgjR0pbt1pdEQAAAAAAKE2EUiiXbDZp3jypZUtzwPNrrpHi462uCgAAAAAAlBZCKZRbvr7Sd99JoaHSvn3SDTdIWVlWVwUAAAAAAEoDoRTKtRo1zDGlvL2lFSukW27hjnwAAAAAAFQEhFIo9zp1kl57zXz+7rvS5MnW1gMAAAAAAEqOUApuYfRo6ZlnzOcvvSQ9+qi19QAAAAAAgJIhlILbuPde6YEHzOczZpjhFAAAAAAAcE+EUnArTz4p3Xab+XzKFGnuXEvLAQAAAAAAxUQoBbdis0mzZ0s33SQ5HNKtt0offWR1VQAAAAAAoKgIpeB2bDZzwPMBA6SsLGnkSOmTT6yuCgAAAAAAFAWhFNySh4f06afSNddImZnS8OHS559bXRUAAAAAACisCh1K1alTRzabzWV6+umnrS4LpcTTU/riC6lPHykjQxo2TFqwwOqqAAAAAABAYVToUEqSHnvsMR09etQ53XHHHVaXhFLk5SV99ZV01VVmMDVkiPTii5JhWF0ZAAAAAAAoSIUPpSpVqqSIiAjnFBAQYHVJKGXe3tI330g9e0rp6dLkyVK/ftLJk1ZXBgAAAAAA8lPhQ6mnn35aVatWVevWrfXss88qMzMz37ZpaWlKSkpymeAefHykxYul++83x5tatEhq1sxcBgAAAAAAyp8KHUrdeeed+vjjj7V8+XKNGzdOTz31lO69995828+cOVPBwcHOKSoqqgyrRUl5eEgzZ5qBVM2a0vHjUt++0oMPcjkfAAAAAADljc0w3OvP9fvvv1/PPPNMgW22b9+uJk2a5Fr+zjvvaNy4cUpOTpaPj0+u9WlpaUpLS3POJyUlKSoqSomJiQoKCip58SgzcXHSLbdIX39tzg8eLL33nnmpHwAAAAAAuHCSkpIUHBx83jzF7UKpEydOKC4ursA29erVk3ce6cPWrVvVrFkz7dixQ40bNz7vvgr7IaJ8MgzpySel6dMlh0Pq1En69lupcmWrKwMAAAAAoOIqbJ7iWYY1lYrQ0FCFhoYW67WbNm2S3W5XWFhYKVeF8shmkx5+WKpXTxozRlqzRmrXzry8r0EDq6sDAAAAAODi5nahVGGtXbtW69at05VXXqlKlSpp7dq1mjx5soYPH67KdJW5qNx0kznG1MCB0p49UseO0urVUqNGVlcGAAAAAMDFq8IOdO7j46OPP/5Y3bp106WXXqonn3xSkydP1htvvGF1abDAFVdIa9dKdetKJ09KffqYjwAAAAAAwBpuN6ZUWWJMqYpn716pQwfpxAnzUr5VqyRfX6urAgAAAACg4ihsnlJhe0oBealb1xzs3M9PWr9eGjrUHBAdAAAAAACULUIpXHTat5c++kjy8JC++kqaPNnqigAAAAAAuPgQSuGidN110n//az5/6SVp1ixr6wEAAAAA4GJDKIWL1h13SFOmmM/vu0964QVr6wEAAAAA4GJCKIWL2nPPSWPHms/vvlv63/+srQcAAAAAgIsFoRQuajab9Prr0pgx5vykSdIrr1haEgAAAAAAFwVCKVz0bDbpzTel0aPNO/Hdeaf02mtWVwUAAAAAQMVGKAXIDKbeflsaNcoMpiZOlL780uqqAAAAAACouAilgH/ZbNKcOdKNN0oOhzR8uLRhg9VVAQAAAABQMRFKATnYbNIHH0gdOkinT0vXXCMdOGB1VQAAAAAAVDyEUsA5vL2l776T6tSRjh+X+vSRkpKsrgoAAAAAgIqFUArIQ9Wq0qJFUkiItH27dP31Umam1VUBAAAAAFBxEEoB+WjcWPriC7Pn1I8/Sv36ScnJVlcFAAAAAEDFQCgFFODKK6W5cyUvL2nJEqlLF+nYMaurAgAAAADA/RFKAecxbJj01VdSUJC0ebPUvr15SR8AAAAAACg+QimgEPr2lVaulKpXlw4elDp1klatsroqAAAAAADcF6EUUEitWkm//CJdcomUkGCOMbV2rdVVAQAAAADgngilgCKoVcsMotq0kVJSzGBq82arqwIAAAAAwP0QSgFFFBwsLVsmXXqp2WOqVy/pr7+srgoAAAAAAPdCKAUUQ3CwtHy5VL++dPKkeZe+AwesrgoAAAAAAPdBKAUUU2ioOfh5zZrSkSNSt27S7t1WVwUAAAAAgHsglAJKoEYNM5gKC5P27TPHmlq40OqqAAAAAAAo/wilgBKqV09at868K19SktS/vzRzpmQYVlcGAAAAAED5RSgFlII6daQNG6Trr5eysqQHH5QGD5bOnLG6MgAAAAAAyidCKaCU+PtLn38uPfaYZLdLn30mtWtnXtYHAAAAAABcEUoBpchmkx55RFqwwLxD39at0mWXSUuXWl0ZAAAAAADlC6EUcAH072+OM9WwofTPP1KfPtJzzzHOFAAAAAAA2QilgAukcWPpt9+kfv3McabuuUcaNkw6fdrqygAAAAAAsB6hFHABVaokffON9NBD5jhT8+dLLVtKmzdbXRkAAAAAANZy21DqySefVKdOneTv76+QkJA82xw4cEBXX321/P39FRYWpnvuuUeZmZllWyguena79MQT5iDoVapIu3dLHTpIs2dzOR8AAAAA4OLltqFUenq6Bg0apNtuuy3P9VlZWbr66quVnp6uNWvWaN68eZo7d66mTZtWxpUCpgEDpI0bpcsvl9LSpNtvlwYOlJKSrK4MAAAAAICyZzMM9+6rMXfuXE2aNEkJCQkuyxcuXKhrrrlGR44cUXh4uCTptdde03333acTJ07I29v7vNtOSkpScHCwEhMTFRQUdCHKx0UoM1N6+GHp2Wclh0OqX9+8xO+SS6yuDAAAAACAkitsnuK2PaXOZ+3atWrevLkzkJKk6OhoJSUlaevWrXm+Ji0tTUlJSS4TUNo8PaWnn5a++06qWlXas0dq3968vA8AAAAAgItFhQ2ljh075hJISXLOHzt2LM/XzJw5U8HBwc4pKirqgteJi1efPtKGDebA58nJ0o03Sg8+aPaeAgAAAACgoitXodT9998vm81W4LRjx44Ltv8HHnhAiYmJzungwYMXbF+AJNWpI/3yizR8uDk/c6bUo4e0f7+lZQEAAAAAcMF5Wl1ATnfffbdiYmIKbFOvXr1CbSsiIkK//vqry7Ljx4871+XFx8dHPj4+hdo+UFp8faX33pPatZOmTpVWrpSaNpWeekq64w7z7n0AAAAAAFQ05SqUCg0NVWhoaKlsq2PHjnryyScVGxursLAwSdKSJUsUFBSkpk2blso+gNJ0551S585STIy0ZYs0aZL00UfSvHlS48ZWVwcAAAAAQOly2z4YBw4c0KZNm3TgwAFlZWVp06ZN2rRpk5KTkyVJvXv3VtOmTTVixAht3rxZixcv1sMPP6yJEyfSGwrlVps20saN0mOPmT2o1q2TmjeXxo2TDh+2ujoAAAAAAEqPzTAMw+oiiiMmJkbz5s3LtXz58uXq3r27JGn//v267bbbtGLFCgUEBGjUqFF6+umn5elZuA5ihb2FIXAh7Nhh9ppat86c9/aWbrlFeuQRKTLS0tIAAAAAAMhXYfMUtw2lygKhFKzmcEhffSU9+qi0ebO5zNtbGjPGDKeqV7e2PgAAAAAAzlXYPMVtL98DLgZ2u3T99dJvv0mffSa1aCGlp0uvvirVrStNnCgdPWp1lQAAAAAAFB2hFOAGPDykG24wx5v6+GNznKm0NOn//s8Mp+64Q4qPt7pKAAAAAAAKj1AKcCMeHtKQIdLvv5vhVLNmZjj1yitmODVrltmTCgAAAACA8o5QCnBD2eHUpk3Shx9K9etLSUnSffdJjRpJn3wiMVocAAAAAKA8I5QC3JiHhzRsmLRtm9lLqkoVaf9+M7Bq2lR65x16TgEAAAAAyidCKaAC8PaW7rlH2rVLmjBB8vWVduww79JXq5b01FNSQoLVVQIAAAAAcBahFFCBVKkizZ5thlNTpkhVq0rHj0sPPSTVqCHdequ0ZYvVVQIAAAAAQCgFVEg1a0rPPy/9/bf07LPmIOinT0tvv23eua9zZ3PcqawsqysFAAAAAFysCKWACiwoSJo6Vdq5U/r8c6l7d8lul9asMcedatxYmjdPysy0ulIAAAAAwMWGUAq4CHh5SQMHSj/+KG3ebF7GV6mStGePFBMjNWzIoOgAAAAAgLJFKAVcRGw2qVkz6c03z447FRQk7dtnDooeGioNHy59+62UlmZ1tQAAAACAioxQCrhIhYefHXfq/vvNQdGTkqQPPpD695eqVZOGDZN++cXqSgEAAAAAFRGhFHCRq1pVmjlTOnjQHHdq0CAzkEpOlj7+WOrYUWrdWnr3XXpPAQAAAABKj80wDMPqIsqrpKQkBQcHKzExUUFBQVaXA5SZlBRp6VLpjTekH344OxB6tWrSqFHmmFRNmlhbIwAAAACgfCpsnkJPKQC5BARI110nffedtGOHdOedZiB18qR5yd8ll0jt20uvv25e8gcAAAAAQFERSgEoUP360ksvmYOhv/661LmzZLdL69dL48dLkZHm444dVlcKAAAAAHAnhFIACiUgQPrPf6SffpK2b5fuvluqVcu81O/1183eUz16SAsWSGfOWF0tAAAAAKC8Y0ypAjCmFFCwzEwzhHr5ZTOsyj6b+PhIXbpIfftKffpITZtKNpulpQIAAAAAykhh8xRCqQIQSgGF9+ef5nhT334rxcW5rqtTRxoyRBo+XGrWzJLyAAAAAABlhFCqFBBKAUWXmiqtWyd9843Ze2rzZikt7ez6Jk2km26Sbr5ZqlfPujoBAAAAABcGoVQpIJQCSsbhkI4dkz77TPr0U+mXX8xL/rK1aWP2nhoyRKpe3bo6AQAAAAClh1CqFBBKAaXH4ZD275c++kj64gvp99/NZZI53lTXrmZAdcMNUpUq1tYKAAAAACg+QqlSQCgFXBhZWdJff0nvvSd9/bW0devZdV5eUq9eZkB17bVSYKB1dQIAAAAAio5QqhQQSgEXXlaWOUj6nDnSd99Je/acXefrK119tRlQ9e1r3tUPAAAAAFC+EUqVAkIpoGxlZZmDpL/zjrRwoXTkyNl1lSpJXbpI3bpJV1xhjkfl7W1drQAAAACAvBFKlQJCKcA6mZnS8uVmD6offpDi4lzX+/pKrVtL7dqZAdVll5l39vP0tKZeAAAAAICJUKoUEEoB5UNqqrRihbRkibR+vbR5s5SUlLtdYKB5J79x46S2bc0B1AEAAAAAZYtQqhQQSgHl0+nT0saN0s8/m3fx27FD2rVLOnPmbJtmzaT//Efq0UOqW1fy97euXgAAAAC4mBBKlQJCKcA9OBxSSorZk+r1183L/jIyXNuEh5vhVNOmZlDVo4dUvbo19QIAAABARVbhQ6knn3xS3333nTZt2iRvb28lJCTkamPL49qdjz76SEOHDi3UPgilAPd0+LD02mvSl19K+/aZgVVeGjeWevY0x6Vq0cIMrHx9y7RUAAAAAKhwKnwoNX36dIWEhOjQoUN6++238w2l5syZoz59+jiXhYSEyLeQf3USSgHuzeEwL/U7fty8xG/nTvOyv/Xrzcv9zj37eXhIDRua41F16SJ17mwGVXa7NfUDAAAAgDsqbJ7itvepevTRRyVJc+fOLbBdSEiIIiIiyqAiAOWN3W4Ofh4YKNWvL119tbnc4ZAOHpS+/15audIMq/bskU6dMsOrHTuk99832wYHSx06nO1Jdckl5hQcbN37AgAAAICKwG17SmWbO3euJk2alG9PqcjISKWlpalevXoaP368Ro8enedlfXmhpxRwcTAM8w5/u3dL69ZJv/xi9qjavt1cnpfq1aUmTaRLLz0bVjVtKoWGctc/AAAAABe3Ct9TqjAee+wx9ejRQ/7+/vrhhx80YcIEJScn684778yzfVpamtLS0pzzSXndcx5AhWOzSX5+UvPm5nTrrWZQlZQkrV0r/fST9Ndf0t9/S/v3S3Fx0tGj5rR8ueu2Klc+G1C1bSu1b29u07NCn20BAAAAoOjKVU+p+++/X88880yBbbZv364mTZo45wvqKXWuadOmac6cOTp48GCe62fMmOG8LDAnekoBkM72qIqNlX7/Xfrjj7Nh1b590rFjucepkszB01u3NkOqZs3MwKppU6lKlTJ/CwAAAABwwbnlQOcnTpxQXFxcgW3q1asnb29v53xRQqnvvvtO11xzjVJTU+Xj45NrfV49paKiogilAJxXerr0zz/S5s3mtG2btGWLeQlgfnf/Cw2VIiLOTmFh5iWBnTqZjwywDgAAAMAdueXle6GhoQoNDb1g29+0aZMqV66cZyAlST4+PvmuA4CCeHtL4eFS797mlO30aWnTJmnFCjOk+vtvae9es7fViRPm9OefubcXHCxdfrk5yHpkpLnt8HAzvKpZU/LyKqt3BgAAAAAXRrkKpYriwIEDio+P14EDB5SVlaVNmzZJkho0aKDAwEB98803On78uC6//HL5+vpqyZIleuqppzR16lRrCwdwUfH3N3s+dep0dll6+tkw6uBBc2yq7IBq506zd1ViorR4sTmdy8NDqlVLatBAatjQDKkqVTp7p8GgIKlePaluXbMtAAAAAJRH5eryvaKIiYnRvHnzci1fvny5unfvrkWLFumBBx7Q7t27ZRiGGjRooNtuu01jx46VvZDXxHD3PQBlzTCkU6fMAdaXL5d27JBOnpTi483LA+PjzVCrMLy9zeDqkkuk+vWlqCgzzIqKMqeqVblTIAAAAIDS55ZjSpU3hFIAyguHQ8rIMAOpAwfMSwF37DAvBTxxQjpzxrxU8MwZM9Q6fPj84ZWvr9nLKjusqlrV7HGVPYWESNWqmVNoqPnIZYMAAAAAzodQqhQQSgFwN4ZhBlipqealgJs2SVu3SocOmZcJxsaa0z//FG/7wcFnQ6rsoCrnY9Wq5uWDgYFnw63AQCkggIHbAQAAgIsFoVQpIJQCUNFkZZk9qBITzUHX//5b2r9fOnZMSkgw7xR4+rSUnGxOiYnm8qQkM+wqiexwyt/fDKhsNnOy283llSuboVdIiOTnZ+4ve5LM1+UMunx9zTGzPDzMbWQ/L+zk6VmyddnvAQAAAIArt7z7XrmVkpL3aMEeHuZfRTnb5cduN//KKk7b06fN7g95sdnMv9SK0/bMmYL/ygwIKF7b1FTzL9/SaOvvf/avvrQ0KTOzdNr6+Z3ttpGebl4XVRpts/9KLmrb7Ouy8uPjY/4lXNS2mZnmZ5Efb++z12MVpW1Wlvnd5cfLy2xf1LYOh/mzVhptPT3Nz0Iyj4nTp0unbVGO+3J4jvBIOy0/w5BfJSmipdSpZf5tdeaMMtMdzh+NY8fM3lbHjv3b4yolQPHx5jhXKSfP6FSiQ0lJ5teSPZ0+LTkM6bQCnEGXj1LlofyP+9M6e444f1t/SeZx7600eSr/474obc/IT4bM495L6fJS3sey3Sale/jJ7mmXh4fk55EuX4+MXEFZdpCV6ekrm6eHPDwkH1u6fOwZeQZgdrvk8DbbOhySLTND9sx0Z0+4c6c0+SjD8DTbZmXIR+n5hmsOLx/ZvMxzhN2RKS9H/sd9pt1bWXavXG3zCuOy29pskt3Ikrcj1dku56PNJmXavOTwNI9lu5ElHyM1z3aSlGX3UpaHt7ldOeSVecZlfc5Hh8fZtjbDIe+sM/nW4LB7KsvTPO5tMuSTdTrfGrLb2mxmW+/Ms+eIc2sx7B7K8vJ1fv5+jhSX7yLnd+KweSjDw1cOh3mqtJ9JkcNhHs5ZWecc1na7Mr38nPvxSk/JVe+5bbOXeaaflk1GrnY2m/mfLJ+zx71XxtnzSa5t52hrs0ke6WdkMxx51mCzSZk+Ac5lHulnZJfDZX3Ox5xtPTNTZXNk5fv+snwDnMvs6amyG67niJyvyfIxfzew2SR7Rprsjsw820lmW5vdnMnZ9tzvzzDM4z7LsCszUzLS0uXhyHCG7DkfbTbJ8PWTzcMuu12yZ55taxi5p0xPXxl2D3M+LV22zAyX9Tlrz/IyzxE2m3mO8MhKd647970Z3md/N7BnmeeTvD4Dm02Sj48MD09zO1mZsqXnf44wvLxleJrniKK0VVaW7On5/25geHrJ8PJ2aXvuZ+v8jL3N3w0MQzKyHLKlnsnzs821XYdD9rTcv0fY7f9u28tTNl8fc95myCPt9Nnzx78/ytnbzj5HGIZkOAzZzpx2HsvZ+3Z+L//+bpBdv0dqist35qzVMM8nDm/fs9s4nZKrjfPRbpfD5+zvBjm3m+u8ZrfL8D3b1nbm7DkiF5tNhp9/8dqmmueIfJrK4RdQqLaSZPjnaJuW/98PNlsR2/qd/fvBlp7/3w9FaSuZx3323w+2jIL/Jshu6+kpeTrS5WlkOP9EyP45cj56+8phM88RjlTzHJFre9k/Fz6uf2vYMtLz/XXR4eX6t4YtI91lWy7b9zbPEZKkzIKPe4en698a9oyCzxHyMn+PUFaW+d0p7985bN65zxG52mQfU56ufz/kddw7eZ2/rbMeT08Z3ubvBjLM4z6vem02mZ+Xz9nfObLbnts++/cI+frKz08KC5Pb/a3h0ragfeRkIF+JiYmGJCMx798bDKNfP9cX+Pvn3U4yjG7dXNtWq5Z/27ZtXdvWrp1/26ZNXds2bZp/29q1Xdu2bZt/22rVXNt265Z/W39/17b9+uXf9twfuRtvLLhtcvLZtqNGFdw2NvZs2wkTCm67d+/ZtlOnFtx2y5azbadPL7jtr7+ebTtrVsFtly8/2/aVVwpu++23Z9vOmVNw208+Odv2k08Kbjtnztm2335bcNtXXjnbdvnygtvOmnW27a+/Ftx2+vSzbbdsKbjt1Kln2+7dW3DbCRPOto2NLbjtqFFn2yYnF9z2xhsNFwW15RxhGJKR5edvbNtmGKtXG8Z33xnG4dYFnyMmTDCMW24xjFtvNYwNdQs+R3Rvl2y0a2cYbdoYxldVRhXYtnXNWCMqyjAiIw3jHf+CzxENPPcadvu/P84q+BzRVFvO/jhreoFt2+rXsz/OKvgc0U3Lz/44q+BzRD99e/bHWXMKbHujPjn746yCzxGjNOfsj7MKPkdM0Ctnf5y1vMC2UzXr7I+zCj5HTNf0sz/OKvgcMUtTnbO1VfA54hVNcM5WU8HniDka5Zz1V8HniE90o8uigtp+q34ui5KV/zliubq5LIpV/ueIX9XWZdFe5X+O2KKmLou2KP9zxF7Vdln0q/I/R8Sqmsui5cr/HJEsf5dF36rgc0TO2U9U8DnCX8nO2Tkq+BxRTbHO2VdU8DmitvY6ZzlHmLOcI8xZzhFnF3GOMGc5R5iznCPM2cKeI5x/RhTQtrz/rZEoGZKMxMREoyD0lAIAXFB2m3kHQKfZkn7Pv/3s2TlmBknam3/b5cslZ8eqGEm5b8rq9OuvkkLN57Y7JL2Wf9vff5cya5r/E9T3kYLbvveulFLH/L+Ytd6R9G7+bac9Ip34t22zhZK+yL/tqJFS11rm/6zqsEHS9/m3HT9O6tfc/J9VjddKej//tjcMlBo2Nts22y7py/zb9r9GqtnSfN5ol6RP8m8b3VsKusx8Xne/pI/yb9vtCkntzd9Yoo5J+iD/tpd3kO663GwbEVdw29atpNs6mm2rJEn6MP+2TZtKt3YynwecLrhtg/rSyM7mc690SR/n3zYqShraWc7eT/o8/7ZVqkg9Wp3t9eHxo5Rfx8CqVaX+ncz3JkneP0jKp9NscLDUt9PZeb/lkvLplBIYIPX+970ZhhSwRlI+/2PT11fq2flsDZXWSzqVd1svL6lbjhqCN0lKzLut3S516XS2hsrbJP2Td1tJuvzys8+r/iUpPv+2bdtIZ/7t7Fztb0lx+bdt3kxK9DZrCD0o6WT+bevXkwL9zP9JHXZU0on829atI2X+u90qJ1Xge6tdSzrlZ/5MhMdLii14uycDzO1Wj5d0LP+2NWtIjf49V4YnSjqef9vwMKnuv23DUgquIbSaVLeS+TzidMHbrVZVqvvv1RvVzxRcb5XKUt0Q83lkqqSj+betFCiFB5ifWWimCvzegipJtSqbz2tkSjqSf9sAfykiyDyWq2SpwJ8dXx8pLNj8WfY3VODn4OcrVf+3Boej4LY+3lJ45Rw9nY5L+XVS8vGWavz7b5xhSPZjkvLpeOTpKYVVOTtvP5l/Ww8PKTRHW4945Xuestul0Ko59vOPlF+nZJvN/JnI5pWQf1vJPAdm806S8unALMk8tzrbniq4beUQyfffc4RPsvI9r0pSSLCUld32tKQCLjAIDpIq/9tJybcQbYP/7YHndUYFfg5+vlKgp/n5+aYXvN3AACn437/2/dKU778DkjksQuV/OzQFpEkq4KKBwECpyr+diQLTJSXn37ZSoFTt3448wemSkvJvGxAgVfn3woWgDOX774sk+ftJlf/dbqWMgmvw9ZGC/t1uYJby/TdOkry9pEr/bjcgSwV+Dl6ekv+/n4O/IamADlgeHpLfv239DBX4Xdjtko9XwRfcVDSMKVUA5zWQR47kfQ1kee8ud762XL5nPufyvaK35fK9s/Pu3KX2fG05R5jPOUcUvS3niLPznCOK3vYiOUcYRh6XpVzAc4Tzcptz2uaswTCU5zni3EvLnM45RxipZ88RBbW1OVwvzcnZ1jDkPJazL+PJPkfkaie5HPc2w7x8L+f7ym5nGGcv9ctum32OOHe7hiEZHualOYZhLjRS8r9sN/syHkkyHOY5IuelczlrMeweMnxyX5KX13az2zovBUxOyXV5qPP7y3HcZ7fNfi+5tn/OOcJIMY/7nN0fnJ9LjkvybDbz8j27zci9/+wG/54jDEMyTpvHvcv2ctRhCww4eynr6TMyshzmZeg5tm23m+sdfgHOsN9x2rzEN6/LZSW5nCOMM2fPEef+TBqGZAs4e4lvzuM+r9NgdltJ5s96Hm2dNfiZl+3abJLSz3OZna+fDJvd/BzS0uVIy3CuO/c9yte8xFcy2yojI+9abZLNzzxHZF/ia8tId/lZzHlp4LnnCCMtPdf36/xefM8OA5DzuM/5PWdPNh9v2by9CnU5cPY5wjDkPO7P/dlx8vIyt20zzyc5zxHn/gxnX7Zrs5097vO7ZDb7HGGzyfl7xLldg5zfh5en7H5nj3tH8ulcx5zzs/PK/XtEdtvs7yH7M5aHh2x+5uV7Hh5y698jkpKSFBwZyUDnJcFA5wAAAAAAAEVT2DyFG3QDAAAAAACgzBFKAQAAAAAAoMwRSgEAAAAAAKDMEUoBAAAAAACgzBFKAQAAAAAAoMwRSgEAAAAAAKDMEUoBAAAAAACgzBFKAQAAAAAAoMwRSgEAAAAAAKDMEUoBAAAAAACgzHlaXUB5ZhiGJCkpKcniSgAAAAAAANxDdo6Snavkh1CqAKdOnZIkRUVFWVwJAAAAAACAezl16pSCg4PzXW8zzhdbXcQcDoeOHDmiSpUqyWazWV1OiSQlJSkqKkoHDx5UUFCQ1eUAFR7HHFB2ON6AssUxB5Qtjjm4I8MwdOrUKUVGRspuz3/kKHpKFcBut6tmzZpWl1GqgoKCOJEBZYhjDig7HG9A2eKYA8oWxxzcTUE9pLIx0DkAAAAAAADKHKEUAAAAAAAAyhyh1EXCx8dH06dPl4+Pj9WlABcFjjmg7HC8AWWLYw4oWxxzqMgY6BwAAAAAAABljp5SAAAAAAAAKHOEUgAAAAAAAChzhFIAAAAAAAAoc4RSAAAAAAAAKHOEUheB2bNnq06dOvL19VWHDh3066+/Wl0SUCHMmDFDNpvNZWrSpIlzfWpqqiZOnKiqVasqMDBQN9xwg44fP25hxYB7WbVqlfr376/IyEjZbDYtWLDAZb1hGJo2bZqqV68uPz8/9erVS7t27XJpEx8fr5tvvllBQUEKCQnRmDFjlJycXIbvAnAf5zvmYmJicv2716dPH5c2HHNA4cycOVPt2rVTpUqVFBYWpgEDBmjnzp0ubQrzu+SBAwd09dVXy9/fX2FhYbrnnnuUmZlZlm8FKBFCqQpu/vz5mjJliqZPn66NGzeqZcuWio6OVmxsrNWlARXCpZdeqqNHjzqnn3/+2blu8uTJ+uabb/Tpp59q5cqVOnLkiAYOHGhhtYB7SUlJUcuWLTV79uw818+aNUv/+9//9Nprr2ndunUKCAhQdHS0UlNTnW1uvvlmbd26VUuWLNG3336rVatW6T//+U9ZvQXArZzvmJOkPn36uPy799FHH7ms55gDCmflypWaOHGifvnlFy1ZskQZGRnq3bu3UlJSnG3O97tkVlaWrr76aqWnp2vNmjWaN2+e5s6dq2nTplnxloDiMVChtW/f3pg4caJzPisry4iMjDRmzpxpYVVAxTB9+nSjZcuWea5LSEgwvLy8jE8//dS5bPv27YYkY+3atWVUIVBxSDK+/PJL57zD4TAiIiKMZ5991rksISHB8PHxMT766CPDMAxj27ZthiRj/fr1zjYLFy40bDabcfjw4TKrHXBH5x5zhmEYo0aNMq677rp8X8MxBxRfbGysIclYuXKlYRiF+13y+++/N+x2u3Hs2DFnm1dffdUICgoy0tLSyvYNAMVET6kKLD09Xb/99pt69erlXGa329WrVy+tXbvWwsqAimPXrl2KjIxUvXr1dPPNN+vAgQOSpN9++00ZGRkux1+TJk1Uq1Ytjj+gFOzdu1fHjh1zOcaCg4PVoUMH5zG2du1ahYSEqG3bts42vXr1kt1u17p168q8ZqAiWLFihcLCwtS4cWPddtttiouLc67jmAOKLzExUZJUpUoVSYX7XXLt2rVq3ry5wsPDnW2io6OVlJSkrVu3lmH1QPERSlVgJ0+eVFZWlstJSpLCw8N17Ngxi6oCKo4OHTpo7ty5WrRokV599VXt3btXXbt21alTp3Ts2DF5e3srJCTE5TUcf0DpyD6OCvo37tixYwoLC3NZ7+npqSpVqnAcAsXQp08fvfvuu1q2bJmeeeYZrVy5Un379lVWVpYkjjmguBwOhyZNmqTOnTurWbNmklSo3yWPHTuW57+D2esAd+BpdQEA4K769u3rfN6iRQt16NBBtWvX1ieffCI/Pz8LKwMAoPQNHTrU+bx58+Zq0aKF6tevrxUrVqhnz54WVga4t4kTJ2rLli0uY5MCFwt6SlVg1apVk4eHR647NBw/flwREREWVQVUXCEhIWrUqJF2796tiIgIpaenKyEhwaUNxx9QOrKPo4L+jYuIiMh1Y4/MzEzFx8dzHAKloF69eqpWrZp2794tiWMOKI7bb79d3377rZYvX66aNWs6lxfmd8mIiIg8/x3MXge4A0KpCszb21tt2rTRsmXLnMscDoeWLVumjh07WlgZUDElJydrz549ql69utq0aSMvLy+X42/nzp06cOAAxx9QCurWrauIiAiXYywpKUnr1q1zHmMdO3ZUQkKCfvvtN2ebH3/8UQ6HQx06dCjzmoGK5tChQ4qLi1P16tUlccwBRWEYhm6//XZ9+eWX+vHHH1W3bl2X9YX5XbJjx476888/XcLgJUuWKCgoSE2bNi2bNwKUEJfvVXBTpkzRqFGj1LZtW7Vv314vvviiUlJSNHr0aKtLA9ze1KlT1b9/f9WuXVtHjhzR9OnT5eHhoWHDhik4OFhjxozRlClTVKVKFQUFBemOO+5Qx44ddfnll1tdOuAWkpOTnT0wJHNw802bNqlKlSqqVauWJk2apCeeeEINGzZU3bp19cgjjygyMlIDBgyQJF1yySXq06ePxo4dq9dee00ZGRm6/fbbNXToUEVGRlr0roDyq6BjrkqVKnr00Ud1ww03KCIiQnv27NG9996rBg0aKDo6WhLHHFAUEydO1IcffqivvvpKlSpVco4BFRwcLD8/v0L9Ltm7d281bdpUI0aM0KxZs3Ts2DE9/PDDmjhxonx8fKx8e0DhWX37P1x4L7/8slGrVi3D29vbaN++vfHLL79YXRJQIQwZMsSoXr264e3tbdSoUcMYMmSIsXv3buf6M2fOGBMmTDAqV65s+Pv7G9dff71x9OhRCysG3Mvy5csNSbmmUaNGGYZhGA6Hw3jkkUeM8PBww8fHx+jZs6exc+dOl23ExcUZw4YNMwIDA42goCBj9OjRxqlTpyx4N0D5V9Axd/r0aaN3795GaGio4eXlZdSuXdsYO3asy63oDYNjDiisvI41ScacOXOcbQrzu+S+ffuMvn37Gn5+fka1atWMu+++28jIyCjjdwMUn80wDKPsozAAAAAAAABczBhTCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAAAAAAGWOUAoAAKCCqVOnjl588UWrywAAACgQoRQAAEAJxMTEaMCAAZKk7t27a9KkSWW277lz5yokJCTX8vXr1+s///lPmdUBAABQHJ5WFwAAAABX6enp8vb2LvbrQ0NDS7EaAACAC4OeUgAAAKUgJiZGK1eu1EsvvSSbzSabzaZ9+/ZJkrZs2aK+ffsqMDBQ4eHhGjFihE6ePOl8bffu3XX77bdr0qRJqlatmqKjoyVJL7zwgpo3b66AgABFRUVpwoQJSk5OliStWLFCo0ePVmJionN/M2bMkJT78r0DBw7ouuuuU2BgoIKCgjR48GAdP37cuX7GjBlq1aqV3nvvPdWpU0fBwcEaOnSoTp065Wzz2WefqXnz5vLz81PVqlXVq1cvpaSkXKBPEwAAXAwIpQAAAErBSy+9pI4dO2rs2LE6evSojh49qqioKCUkJKhHjx5q3bq1NmzYoEWLFun48eMaPHiwy+vnzZsnb29vrV69Wq+99pokyW6363//+5+2bt2qefPm6ccff9S9994rSerUqZNefPFFBQUFOfc3derUXHU5HA5dd911io+P18qVK7VkyRL9/fffGjJkiEu7PXv2aMGCBfr222/17bffauXKlXr66aclSUePHtWwYcN0yy23aPv27VqxYoUGDhwowzAuxEcJAAAuEly+BwAAUAqCg4Pl7e0tf39/RUREOJe/8sorat26tZ566innsnfeeUdRUVH666+/1KhRI0lSw4YNNWvWLJdt5hyfqk6dOnriiSc0fvx4/d///Z+8vb0VHBwsm83msr9zLVu2TH/++af27t2rqKgoSdK7776rSy+9VOvXr1e7du0kmeHV3LlzValSJUnSiBEjtGzZMj355JM6evSoMjMzNXDgQNWuXVuS1Lx58xJ8WgAAAPSUAgAAuKA2b96s5cuXKzAw0Dk1adJEktk7KVubNm1yvXbp0qXq2bOnatSooUqVKmnEiBGKi4vT6dOnC73/7du3KyoqyhlISVLTpk0VEhKi7du3O5fVqVPHGUhJUvXq1RUbGytJatmypXr27KnmzZtr0KBBevPNN/XPP/8U/kMAAADIA6EUAADABZScnKz+/ftr06ZNLtOuXbt0xRVXONsFBAS4vG7fvn265ppr1KJFC33++ef67bffNHv2bEnmQOilzcvLy2XeZrPJ4XBIkjw8PLRkyRItXLhQTZs21csvv6zGjRtr7969pV4HAAC4eBBKAQAAlBJvb29lZWW5LLvsssu0detW1alTRw0aNHCZzg2icvrtt9/kcDj0/PPP6/LLL1ejRo105MiR8+7vXJdccokOHjyogwcPOpdt27ZNCQkJatq0aaHfm81mU+fOnfXoo4/q999/l7e3t7788stCvx4AAOBchFIAAAClpE6dOlq3bp327dunkydPyuFwaOLEiYqPj9ewYcO0fv167dmzR4sXL9bo0aMLDJQaNGigjIwMvfzyy/r777/13nvvOQdAz7m/5ORkLVu2TCdPnszzsr5evXqpefPmuvnmm7Vx40b9+uuvGjlypLp166a2bdsW6n2tW7dOTz31lDZs2KADBw7oiy++0IkTJ3TJJZcU7QMCAADIgVAKAACglEydOlUeHh5q2rSpQkNDdeDAAUVGRmr16tXKyspS79691bx5c02aNEkhISGy2/P/Vaxly5Z64YUX9Mwzz6hZs2b64IMPNHPmTJc2nTp10vjx4zVkyBCFhobmGihdMns4ffXVV6pcubKuuOIK9erVS/Xq1dP8+fML/b6CgoK0atUq9evXT40aNdLDDz+s559/Xn379i38hwMAAHAOm8G9fAEAAAAAAFDG6CkFAAAAAACAMkcoBQAAAAAAgDJHKAUAAAAAAIAyRygFAAAAAACAMkcoBQAAAAAAgDJHKAUAAAAAAIAyRygFAAAAAACAMkcoBQAAAAAAgDJHKAUAAAAAAIAyRygFAAAAAACAMkcoBQAAAAAAgDJHKAUAAAAAAIAyRygFAAAAAACAMkcoBQAAAAAAgDJHKAUAAAAAAIAyRygFAAAAAACAMkcoBQAAAAAAgDJHKAUAACqEFStWyGazacWKFaW6XZvNphkzZpTqNgEAAEAoBQAALDB37lzZbDbn5OnpqRo1aigmJkaHDx8u83q+//77chc85fx8zp3Gjx9vdXkFOn78uKZOnaomTZrI399fAQEBatOmjZ544gklJCRYXR4AACgnPK0uAAAAXLwee+wx1a1bV6mpqfrll180d+5c/fzzz9qyZYt8fX3LrI7vv/9es2fPzjOYOnPmjDw9rfmV6aqrrtLIkSNzLW/UqJEF1RTO+vXr1a9fPyUnJ2v48OFq06aNJGnDhg16+umntWrVKv3www8WVwkAAMoDQikAAGCZvn37qm3btpKkW2+9VdWqVdMzzzyjr7/+WoMHD7a4OlNZhmPnatSokYYPH17k150+fVr+/v65lmdmZsrhcMjb27vYNaWkpCggICDPdQkJCbr++uvl4eGh33//XU2aNHFZ/+STT+rNN98s9r4LWwcAAHAPXL4HAADKja5du0qS9uzZ47J8x44duvHGG1WlShX5+vqqbdu2+vrrr8+7vZ9++kmDBg1SrVq15OPjo6ioKE2ePFlnzpxxtomJidHs2bMluV4yly3nmFKfffaZbDabVq5cmWtfr7/+umw2m7Zs2VLiuouie/fuatasmX777TddccUV8vf314MPPqh9+/bJZrPpueee04svvqj69evLx8dH27ZtkyT9+OOP6tq1qwICAhQSEqLrrrtO27dvd9n2jBkzZLPZtG3bNt10002qXLmyunTpkm8tr7/+ug4fPqwXXnghVyAlSeHh4Xr44Yed8/mN11WnTh3FxMQ457Mv91y5cqUmTJigsLAw1axZs1x+HwAAoPDoKQUAAMqNffv2SZIqV67sXLZ161Z17txZNWrU0P3336+AgAB98sknGjBggD7//HNdf/31+W7v008/1enTp3XbbbepatWq+vXXX/Xyyy/r0KFD+vTTTyVJ48aN05EjR7RkyRK99957BdZ39dVXKzAwUJ988om6devmsm7+/Pm69NJL1axZsxLXnS01NVUnT57MtTwoKMilt1NcXJz69u2roUOHavjw4QoPD3eumzNnjlJTU/Wf//xHPj4+qlKlipYuXaq+ffuqXr16mjFjhs6cOaOXX35ZnTt31saNG1WnTh2X/Q0aNEgNGzbUU089JcMw8q3366+/lp+fn2688cbzvrfimDBhgkJDQzVt2jSlpKSU+fcBAABKmQEAAFDG5syZY0gyli5dapw4ccI4ePCg8dlnnxmhoaGGj4+PcfDgQWfbnj17Gs2bNzdSU1OdyxwOh9GpUyejYcOGzmXLly83JBnLly93Ljt9+nSufc+cOdOw2WzG/v37ncsmTpxo5PdrkSRj+vTpzvlhw4YZYWFhRmZmpnPZ0aNHDbvdbjz22GNFrjs/kvKdPvroI2e7bt26GZKM1157zeX1e/fuNSQZQUFBRmxsrMu6Vq1aGWFhYUZcXJxz2ebNmw273W6MHDnSuWz69OmGJGPYsGHnrdcwDKNy5cpGy5YtC9U2+z3m/Gyz1a5d2xg1apRzPvvnpUuXLi6fu2GU3fcBAABKH5fvAQAAy/Tq1UuhoaGKiorSjTfeqICAAH399deqWbOmJCk+Pl4//vijBg8erFOnTunkyZM6efKk4uLiFB0drV27dhV4tz4/Pz/n85SUFJ08eVKdOnWSYRj6/fffi1XzkCFDFBsbqxUrVjiXffbZZ3I4HBoyZEip1J3tuuuu05IlS3JNV155pUs7Hx8fjR49Os9t3HDDDQoNDXXOHz16VJs2bVJMTIyqVKniXN6iRQtdddVV+v7773Nto7B3+0tKSlKlSpUK1bY4xo4dKw8PD5dlZfl9AACA0sXlewAAwDKzZ89Wo0aNlJiYqHfeeUerVq2Sj4+Pc/3u3btlGIYeeeQRPfLII3luIzY2VjVq1Mhz3YEDBzRt2jR9/fXX+ueff1zWJSYmFqvmPn36KDg4WPPnz1fPnj0lmZeKtWrVynlXvJLWna1mzZrq1avXeWuqUaNGvoOX161b12V+//79kqTGjRvnanvJJZdo8eLFuQYRP3cb+QkKCtKpU6cK1bY48qqjLL8PAABQugilAACAZdq3b++8+96AAQPUpUsX3XTTTdq5c6cCAwPlcDgkSVOnTlV0dHSe22jQoEGey7OysnTVVVcpPj5e9913n5o0aaKAgAAdPnxYMTExzm0XlY+PjwYMGKAvv/xS//d//6fjx49r9erVeuqpp5xtSlJ3ceTsEVaUdaWx/ZyaNGmiTZs2KT09vUR3+MvKyip0HeXx+wAAAIVDKAUAAMoFDw8PzZw5U1deeaVeeeUV3X///apXr54kycvLq1A9hnL6888/9ddff2nevHkaOXKkc/mSJUtytc15t73CGDJkiObNm6dly5Zp+/btMgzDeamYpBLVfaHVrl1bkrRz585c63bs2KFq1aq59JIqiv79+2vt2rX6/PPPNWzYsPO2r1y5shISElyWpaen6+jRo0Xarzt/HwAAXMwYUwoAAJQb3bt3V/v27fXiiy8qNTVVYWFh6t69u15//fU8g4oTJ07ku63ssYeMHHeLMwxDL730Uq622SHMuQFJfnr16qUqVapo/vz5mj9/vtq3b+9yaVlJ6r7QqlevrlatWmnevHku73fLli364Ycf1K9fv2Jve/z48apevbruvvtu/fXXX7nWx8bG6oknnnDO169fX6tWrXJp88Ybb+TbUyo/7vx9AABwMaOnFAAAKFfuueceDRo0SHPnztX48eM1e/ZsdenSRc2bN9fYsWNVr149HT9+XGvXrtWhQ4e0efPmPLfTpEkT1a9fX1OnTtXhw4cVFBSkzz//PNfYUpLUpk0bSdKdd96p6OhoeXh4aOjQofnW6OXlpYEDB+rjjz9WSkqKnnvuuVxtilt3Tn/99Zfef//9XMvDw8N11VVXnff1+Xn22WfVt29fdezYUWPGjNGZM2f08ssvKzg4WDNmzCj2ditXrqwvv/xS/fr1U6tWrTR8+HDnZ7tx40Z99NFH6tixo7P9rbfeqvHjx+uGG27QVVddpc2bN2vx4sWqVq1akfZbVt8HAAAoZdbd+A8AAFys5syZY0gy1q9fn2tdVlaWUb9+faN+/fpGZmamYRiGsWfPHmPkyJFGRESE4eXlZdSoUcO45pprjM8++8z5uuXLlxuSjOXLlzuXbdu2zejVq5cRGBhoVKtWzRg7dqyxefNmQ5IxZ84cZ7vMzEzjjjvuMEJDQw2bzWbk/BVJkjF9+vRcdS5ZssSQZNhsNuPgwYN5vs/C1J0fSflO3bp1c7br1q2bcemll+Z6/d69ew1JxrPPPpvn9pcuXWp07tzZ8PPzM4KCgoz+/fsb27Ztc2kzffp0Q5Jx4sSJ89ab05EjR4zJkycbjRo1Mnx9fQ1/f3+jTZs2xpNPPmkkJiY622VlZRn33XefUa1aNcPf39+Ijo42du/ebdSuXdsYNWqUs11BPy/ZLvT3AQAASp/NMHL0aQcAAAAAAADKAGNKAQAAAAAAoMwRSgEAAAAAAKDMEUoBAAAAAACgzBFKAQAAAAAAoMwRSgEAAAAAAKDMEUoBAAAAAACgzBFKAQAAAAAAoMx5Wl1AeeZwOHTkyBFVqlRJNpvN6nIAAAAAAADKPcMwdOrUKUVGRspuz78/FKFUAY4cOaKoqCirywAAAAAAAHA7Bw8eVM2aNfNdTyhVgEqVKkkyP8SgoCCLqwEAAAAAACj/kpKSFBUV5cxV8kMoVYDsS/aCgoIIpQAAAAAAAIrgfEMhMdA5AAAAAAAAyhyhFAAAAAAAAMocoVQeZs+eraZNm6pdu3ZWlwIAAAAAAFAh2QzDMKwuorxKSkpScHCwEhMTGVMKAAAAAACgEAqbp9BTCgAAAAAAAGWOUAoAAAAAAABljlAKAAAAAAAAZY5QCgAAAAAAAGWOUAoAAAAAAABlztPqAnDhZTmylJaVJk+7pzztnrLbyCIBAAAAAIC1CKUuAmsPrVXXOV2d83ab3RlQ5Zy87F6q6l9VEYER5hQQoVYRrXRj0xvl5eFl4TsAAAAAAAAVDaHURSDTkeky7zAcSs9KV3pWeq62h08d1h/H/3BZ9uCPD+r+zvcrplWMfDx9LmitAAAAAADg4mAzDMOwuojyKikpScHBwUpMTFRQUJDV5RRbliNLZzLPKNORWeCUlpmmk6dP6njKcR1LPqZDSYf0ydZPdDzluCSpRqUaurfzvZrQboI87eSZAAAAAAAgt8LmKYRSeZg9e7Zmz56trKws/fXXX24fSpXEmYwzenPjm5q1epYOnzosSbqn0z2addUsiysDAAAAAADlEaFUKagoPaVKQ1pmml7+9WXds+Qeedm9tGXCFjWq2sjqsgAAAAAAQDlT2DyF27ChUHw8fTS101T1a9hPGY4M3f3D3VaXBAAAAAAA3BihFIrkhd4vyNPuqW//+laLdy+2uhwAAAAAAOCmCKVQJI2rNdYd7e+QJE1ePFkZWRkWVwQAAAAAANwRoRSKbFq3aarmX03bT27XqxtetbocAAAAAADghgilUGQhviF64sonJEnTV0zXydMnLa4IAAAAAAC4G0IpFMutl92qFuEtlJCaoGnLp1ldDgAAAAAAcDOEUigWD7uHXurzkiTp9d9e16Zjm6wtCAAAAAAAuBVCKRRb9zrdNfjSwXIYDk38fqIchsPqkgAAAAAAgJsglEKJPN/7eQV4BWjNwTV6b/N7VpcDAAAAAADcBKEUSqRmUE1N62aOKXXv0nuVkJpgbUEAAAAAAMAtEEqhxCZdPklNqjVRbEqspi+fbnU5AAAAAADADVT4UOrgwYPq3r27mjZtqhYtWujTTz+1uqQKx9vDWy/3fVmS9Mr6V7T52GaLKwIAAAAAAOVdhQ+lPD099eKLL2rbtm364YcfNGnSJKWkpFhdVoXTq14vDWo6SA7DodsX3i7DMKwuCQAAAAAAlGMVPpSqXr26WrVqJUmKiIhQtWrVFB8fb21RFdTzvZ+Xv5e/fj7ws4Z+PlRJaUlWlwQAAAAAAMqpch9KrVq1Sv3791dkZOT/s3ffcVXX/f/HH+ewQYaIgijuiQM3jtyombnNkSZZaUPtm9bVVV1X2dWyaVpR2tSGppYrZ0ruvVNx7wUqypZ5zu8PfuckgooIHITn/XbjdovP53M+n9chT+qz1/v1xmAwsGDBgmzXhIWFUaVKFZydnQkODmbbtm053mvnzp1kZGQQEBBQwFWXTAGeAXz50JfYG+2Zc2AOTaY1YeeFnbYuS0RERERERESKoCIfSiUmJhIUFERYWFiO52fPns348eOZMGECu3btIigoiG7dunHp0qUs1129epXhw4fz9ddfF0bZJVZoo1DWj1hPZc/KHL92nFbfteKzrZ9pOZ+IiIiIiIiIZGEw30dpgcFgYP78+fTp08d6LDg4mObNm/PFF18AYDKZCAgIYOzYsbzyyisApKSk0KVLF0aOHMljjz12y/unpKSQkpJi/T4uLo6AgABiY2Px8PAomDdVTF27fo0nFz3J/EPzAehUtRMfhnxIU/+mNq5MRERERERERApSXFwcnp6ed8xTinyn1O2kpqayc+dOQkJCrMeMRiMhISFs3rwZALPZzOOPP06nTp1uG0gBTJw4EU9PT+uXlvnlXWmX0vw+8Hc+7/45TnZO/HXyL5p904xHf3+UE9dO2Lo8EREREREREbGx+zqUunLlChkZGfj6+mY57uvrS2RkJAAbN25k9uzZLFiwgEaNGtGoUSP27duX4/1effVVYmNjrV9nz54t8PdQnBkMBsa0GMOhMYcY1nAYBgzM2j+LOl/UYfSS0ey6uEvL+kRERERERERKKHtbF1DQHnjgAUwmU66udXJywsnJqYArKnmqeFXhp74/8WKrF3ll1SusOL6CL3d8yZc7vqSOTx2GNRjGow0epWrpqrYuVUREREREREQKyX3dKeXj44OdnR1RUVFZjkdFReHn55fn+4aFhREYGEjz5s3vtUS5QSO/Riwftpzw4eEMrDcQZ3tnDl05xH9X/5dqn1WjxTcteH/D+xyJPmLrUkVERERERESkgBWLQectWrTg888/BzIHnVeqVIkxY8ZYB53nVW4Hc0nexKXEMe/gPH7Z9wvhJ8Ix888vxfrl6tOvTj/61e1HQ9+GGAwGG1YqIiIiIiIiIrmV2zylyIdSCQkJHDt2DIDGjRszadIkOnbsiLe3N5UqVWL27NmEhoYybdo0WrRoweTJk5kzZw6HDh3KNmvqbimUKjxRCVEsPLyQ3w/+zl8n/yLdlG49V610NWtA1aJCC+yMdjasVERERERERERup9iEUmvWrKFjx47ZjoeGhjJ9+nQAvvjiCz766CMiIyNp1KgRn332GcHBwXl+ZlhYGGFhYWRkZHDkyBGFUoXs2vVrLD6ymHmH5rH82HKS05Ot53xcfeheozs9avaga/WulHYpbcNKRURERERERORmxSaUsiV1StleYmoiy48tZ96heSw+spi4lDjrOTuDHa0DWtOjZg8eqvkQ9cvV1zI/ERERERERERtTKJUPFEoVLWkZaWw8u5GlR5ey5OgSIi5HZDlfybMS3ap3o1XFVgRXDKaOTx2Mhvt6lr+IiIiIiIjIfUehVD5QKFW0nbx2kqVHl7L02FL+OvlXlmV+AB5OHrSo0IL2ldvTpVoXmvk30zwqERERERERkQKmUOoeaKbU/ScpLYnVJ1ez5tQatp7fyo4LO7iefj3LNV7OXnSs0pGHaj5E/7r9NY9KREREREREpAAolMoH6pS6f6Wb0tl/aT+bzm5i1YlV/HXyL2JTYq3nHe0c6VmrJ481fIzuNbvjaOdow2pFREREREREig+FUvlAoVTxkW5KZ+eFnfx5/E9mH5jNgcsHrOfKuJRhUL1BPBb0GMEVgjUsXUREREREROQeKJTKBwqliiez2czeqL38tPcnZu6fSWRCpPVcDe8aDGswjNBGoVTxqmK7IkVERERERETuUwql7oFmSpUc6aZ0wk+E8/O+n5l3cB5JaUkAGA1GetXuxfMtnqdDlQ7qnhIRERERERHJJYVS+UCdUiVLQmoC8w/OZ8beGYSfDLcer1+uPmNbjGVgvYF4OXvZrkARERERERGR+4BCqXygUKrkirgcwRfbvmDG3hnW7ilHO0e61+jO4PqD6VmrJ26ObjauUkRERERERKToUSiVDxRKSUxyDN/t+o4f9vyQZTi6q4MrQ+oPYVzLcdQrV8+GFYqIiIiIiIgULQql8oFCKbnR/kv7+XX/r8zaP4sT105Yj3et3pVxLcfRrXo3zZ4SERERERGREk+h1D3QoHO5HbPZzKazm5i8dTLzDs7DZDYBUMenDo8HPc6whsOo4FHBxlWKiIiIiIiI2IZCqXygTim5k5PXTvL5ts/5dte3xKfGA5k794VUCyE0KJQ+dfrg6uBq4ypFRERERERECo9CqXygUEpyKy4ljrkH5jJj7wzWn1lvPe7u6M7AegMJDQrlgUoPaHmfiIiIiIiIFHsKpfKBQinJi+NXj/PT3z8xY+8MTsWcsh6vVroaoUGhDA8aThWvKjarT0RERERERKQgKZTKBwql5F6YzCbWn17PjL0zmBsxl4TUBOu5DlU68HjQ4/QP7E8px1I2rFJEREREREQkfymUygcKpSS/JKYmMv/QfGbsnUH4iXDMZH7s3BzcCA0KZUyLMdQtW9fGVYqIiIiIiIjcO4VS90C770lBOhN7hp/2/sT0vdM5dvWY9XiXal0Y22IsD9V8CDujnQ0rFBEREREREck7hVL5QJ1SUpDMZjNrTq3hs22fsejwIkxmEwABHgGEBoUS2iiUGt41bFyliIiIiIiIyN1RKJUPFEpJYTkVc4ovt3/Jt7u+5VryNevxNgFtGB40nD51+lDOrZwNKxQRERERERHJHYVS+UChlBS25PRkFh1exPQ901lxfIW1e8qAgTaV2tC3Tl/61OlDtdLVbFypiIiIiIiISM4USuUDhVJiSxfiL/Dz3z8z58Acdl7cmeVcg3IN6FOnD33q9KGxX2MMBoONqhQRERERERHJSqFUPlAoJUXF2dizLDy8kAWHFrDm1BoyzBnWc5U8K9GvTj8eb/Q4QX5BNqxSRERERERERKFUvlAoJUXR1etXWXJkCQsOL2D5seUkpSVZzzX2a8yIRiN4tMGjlHEtY8MqRUREREREpKRSKJUPFEpJUXc97TorT6zkp79/YtHhRaRmpALgYHQgpFoI/ev2p1ftXpR1K2vjSkVERERERKSkUCh1D8LCwggLCyMjI4MjR44olJL7QnRSNLP2z+KHPT+w6+Iu63GjwUj7yu3pV7cffev0pYJHBRtWKSIiIiIiIsWdQql8oE4puV8dunKIeQfn8fvB37MEVAAtK7akX51+9Kvbj+re1W1UoYiIiIiIiBRXCqXygUIpKQ5OxZxi3sF5zDs4j01nN2Hmn498u8rtGNlkJP3r9sfFwcWGVYqIiIiIiEhxoVAqHyiUkuLmYvxFFhxawO8Hf2f1qdWYzCYAvJy9GNZgGAPrDaRlxZY42DnYuFIRERERERG5XymUygcKpaQ4Oxd3jul7pvPtrm85HXvaetzDyYNOVTvRtVpXHqr5EJW9KtuwShEREREREbnfKJTKBwqlpCQwmU2Enwhnxt4ZrDi+gitJV7Kcb1upLaFBoQwIHICns6eNqhQREREREZH7hUKpfKBQSkoak9nErou7+PP4nyw/tpwNZzZYZ1A52zvTt05fhgcNp0u1LtgZ7WxcrYiIiIiIiBRFCqXygUIpKenOxZ3jl79/YcbeGRy8ctB6vHyp8gxrOIzhQcOpX66+DSsUERERERGRokahVD5QKCWSyWw2s/PiTmbsmcGs/bOIvh5tPdfcvzljW4xlYL2BONk72bBKERERERERKQoUSuUDhVIi2aVmpLL06FJm7J3BkiNLSDOlAVDOrRzPNH2GZ5o9Q3n38jauUkRERERERGxFoVQ+UCglcnuXEy/zza5v+HL7l5yPPw+AncGONpXa0KNmD3rU7EFg2UAMBoONKxUREREREZHColDqHoSFhREWFkZGRgZHjhxRKCVyB2kZacw/NJ8pW6ew6eymLOcqe1ZmUL1BjGo6iure1W1UoYiIiIiIiBQWhVL5QJ1SInfv5LWTLD26lMVHF7P65GpSMlKs57pV78azzZ6lR60e2BvtbViliIiIiIiIFBSFUvlAoZTIvUlMTWTF8RV8s+sbVhxbgZnM/9xU9KjIyCYjearJU/i7+9u4ShEREREREclPCqXygUIpkfxz4toJpu2Yxvd7vudK0hUgc/5Unzp9eLbZs3Sq2kmzp0RERERERIoBhVL5QKGUSP5LSU/ht4jf+GrHV2w8u9F6PLBsIC8Ev8CwhsNwcXCxYYUiIiIiIiJyLxRK5QOFUiIFa1/UPqbumMqPf/9IQmoCAD6uPjzT9BlGtxiNXyk/G1coIiIiIiIid0uhVD5QKCVSOGKTY/lu93d8tvUzTseeBsDJzoknGj/By21epopXFdsWKCIiIiIiIrmmUCofKJQSKVzppnQWHFrApM2T2HxuMwD2RnuGNhjKqw+8Sm2f2jauUERERERERO5EoVQ+UCglYhtms5l1p9fx7vp3WXliJQAGDHSo0oGhDYbSP7A/Xs5eti1SREREREREcqRQKh8olBKxvW3nt/Hu+ndZdHiR9ZiTnRM9avVgcL3BPFjjQdyd3G1YoYiIiIiIiNxIoVQ+UCglUnScijnFrH2z+GXfLxy4fMB63NHOkU5VO9G7dm961+5NeffyNqxSREREREREFErlA4VSIkWP2Wzm76i/mblvJvMPzefo1aPWc/ZGe15p8wqvt38dRztHG1YpIiIiIiJScimUygcKpUSKNrPZzKErh1h4eCHzD81n2/ltADQo14DpfabTpHwTG1coIiIiIiJS8uQ2TzEWYk0207dvX0qXLs2AAQNsXYqI5CODwUDdsnV55YFX2PrUVuYMmIOPqw/7Lu2jxTcteGP1G1xPu27rMkVERERERCQHJaJTas2aNcTHxzNjxgx+++23XL9OnVIi959LiZcYvXQ0v0VkftYd7RwJrhBMu8rtaF+5Pa0DWuPm6GbjKkVERERERIovdUrdoEOHDri7a3cukZKgnFs55j4ylzkD5lDJsxKpGamsP7Oed9e/S9efu1L+k/K8/tfrXLt+zdalioiIiIiIlGhFPpRat24dPXv2xN/fH4PBwIIFC7JdExYWRpUqVXB2diY4OJht27YVfqEiUqQ8Uu8RTv3fKY6MOcK3Pb/lsYaPEeARQHxqPO+sf4cqU6owYfUEYpJjbF2qiIiIiIhIiWR/ty8wmUysXbuW9evXc/r0aZKSkihbtiyNGzcmJCSEgICAfC0wMTGRoKAgnnjiCfr165ft/OzZsxk/fjxTp04lODiYyZMn061bNw4fPky5cuXytRYRub8YDAZqlqlJzTI1ebLJk5jMJhYcWsCba95k36V9vLXuLaZsncLjjR5nRKMRBPkF2bpkERERERGREiPXM6WuX7/OJ598wldffcXVq1dp1KgR/v7+uLi4cPXqVfbv38+FCxfo2rUrb7zxBi1btsz/Yg0G5s+fT58+fazHgoODad68OV988QWQGZoFBAQwduxYXnnlFet1a9as4YsvvrjtTKmUlBRSUlKs38fFxREQEKCZUiLFjMlsYt7Beby55k0OXD5gPd6kfBNGNBrBow0exdvF24YVioiIiIiI3L/yfaZUrVq1+Pvvv/nmm2+Ii4tj8+bN/P777/z8888sXbqUM2fOcPz4cdq2bcvgwYP55ptv8uWN3E5qaio7d+4kJCTEesxoNBISEsLmzZvv+n4TJ07E09PT+pXfXV8iUjQYDUYGBA7g72f/ZsmjSxgQOAAHowO7Lu5i7LKxVJ5cmTdWv0FscqytSxURERERESm2ct0pdfDgQerWrZurm6alpXHmzBmqV69+T8Xd7OZOqQsXLlChQgU2bdpEq1atrNe9/PLLrF27lq1btwIQEhLC3r17SUxMxNvbm7lz52a53kKdUiIlV3RSNDP3zeSbXd+w79I+AEo7l+blNi8ztsVY7dgnIiIiIiKSS/neKZXbQArAwcEh3wOpe7Fq1SouX75MUlIS586dyzGQAnBycsLDwyPLl4iUDGVcyzA2eCx7n9nLb4/8Rl2fulxLvsar4a9S7bNqvP7X65yJPWPrMkVERERERIqNe9p9Lz09nbCwMB555BH69evHJ598QnJycn7Vdkc+Pj7Y2dkRFRWV5XhUVBR+fn55vm9YWBiBgYE0b978XksUkfuMwWCgf2B/9j27jxl9ZlDVqyqXEi/xzvp3qDqlKg/PfJjFRxaTYcqwdakiIiIiIiL3tXsKpZ5//nnmz59Px44dad++PTNnzmTEiBH5VdsdOTo60rRpU8LDw63HTCYT4eHht+yGyo3Ro0cTERHB9u3b86NMEbkP2RntGB40nENjDjF7wGw6Ve2EyWxiydEl9JzVk6pTqvLW2rc4H3fe1qWKiIiIiIjcl3I9Uwpg/vz59O3b1/p9jRo1OHz4MHZ2dgAcOnSIli1bEhMTk28FJiQkcOzYMQAaN27MpEmT6NixI97e3lSqVInZs2cTGhrKtGnTaNGiBZMnT2bOnDkcOnQIX1/fe3p2btdAikjJcCT6CF/v/Jrpe6YTfT0aADuDHT1r9+SZps/QtXpXDAaDjasUERERERGxrdzmKXcVSvXs2RM7Ozu+/PJL/P39GThwIJ6envTv35+0tDS++eYbrl+/zsqVK/PlTQCsWbOGjh07ZjseGhrK9OnTAfjiiy/46KOPiIyMpFGjRnz22WcEBwfn+ZlhYWGEhYWRkZHBkSNHFEqJSBbJ6cn8HvE703ZOY/2Z9dbj9crWY3yr8QxtMBQneycbVigiIiIiImI7BRJKAcyePZvXX3+dsWPH8thjj/H222+zZs0aMjIyaNOmDW+++SZly5a95zdQFKhTSkTuJOJyBFN3TOWHPT+QkJoAgF8pP8Y0H8OYFmPwdPa0cYUiIiIiIiKFq8BCKYCYmBhefvll9u7dy9SpU2ncuPE9FVtUKZQSkdyKSY7hm53fMGXrFM7HZ86Z8nf356seX9Grdi8bVyciIiIiIlJ4CjSUsli3bh2jR4/mwQcf5O2338bZ2TmvtyqSFEqJyN1Ky0hjzoE5vLn2TY5dzZyHN6jeID7r/hnl3MrZuDoREREREZGCl9s85a523ztz5gwDBw6kQYMGDB06lJo1a7Jz505cXV0JCgpi2bJl91x4URAWFkZgYCDNmze3dSkicp9xsHNgaMOh/P3M3/y7zb+xM9gx+8BsAsMC+XHvj5jMJluXKCIiIiIiUiTcVadUhw4d8PPz4/HHH2fFihUcP36cRYsWAXDw4EGefvpp/Pz8mDNnToEVXJjUKSUi92rnhZ08uehJ9kbtBaBp+aZ83PVjOlTpYNvCRERERERECkiBLN8rVaoUe/fupXr16pjNZqpWrcqpU6eyXPP1118zatSoPBdelCiUEpH8kJaRxiebP+G99e8RnxoPwMO1HuaDkA8ILBto4+pERERERETyV4GEUu3bt6dixYqEhoayatUqDh48yB9//JEvBRdFCqVEJD9dSrzEW2vfYtrOaaSb0jEajDxQ6QH61ulLnzp9qOJVxdYlioiIiIiI3LMCCaVOnz7Niy++yMGDB2nUqBEfffQR/v7++VJwURIWFkZYWBgZGRkcOXJEoZSI5Ksj0Ud4ZdUrzD80P8vxRn6NGNdyHMODhtuoMhERERERkXtXKLvvFXfqlBKRgnQ65jQLDi1gweEFrDu9zjoE/bUHXuOdTu9gMBhsXKGIiIiIiMjdy/dQKjExETc3t1wXcLfXF0UKpUSksFxJusKULVN4Z/07ADze6HG+fvhrHOwcbFyZiIiIiIjI3cltnmLM7Q1r1KjB+++/z8WLF295jdlsZuXKlXTv3p3PPvvs7ioWESnBfFx9eLvT23zX6zvsDHZM3zOd3r/2JiE1wdaliYiIiIiIFIhcd0odPnyY1157jSVLlhAUFESzZs3w9/fH2dmZa9euERERwebNm7G3t+fVV1/l6aefxs7OrqDrL1DqlBIRW1hyZAmPzH2E6+nXaVK+Ce91eo8u1btgNOT6/yOIiIiIiIjYTIHNlDpz5gxz585l/fr1nD59muvXr+Pj40Pjxo3p1q0b3bt3v+/DKA06FxFb23puKz1m9iD6ejQAVbyq8GTjJxnRaAQVPCrYuDoREREREZFb06DzfKBOKRGxpVMxp5i0eRI//f0TMckxABgNRkY2GclHXT7C3cndtgWKiIiIiIjkQKFUPlAoJSJFwfW06/wW8Rvf7PqG9WfWA1DZszI/9P6BjlU72rg6ERERERGRrPJ90LmIiNiGi4MLjwU9xroR61gdupoqXlU4HXuaTj924vllz5OYmmjrEkVERERERO6aOqVuQ51SIlIUxafE86+V/2LazmkA+Lr50rlaZ9pWakvbSm2pW7auhqKLiIiIiIjNaPlePlAoJSJF2Z/H/+TJRU9yLu5cluNlXcvycdePGR403EaViYiIiIhISVZgy/fS09N56623OHfu3J0vvk+FhYURGBhI8+bNbV2KiMgtda3elaNjj7LqsVVMaD+BTlU74WLvwuWky4QuCOXttW+j/+8gIiIiIiJFVZ46pdzd3dm3bx9VqlQpgJKKDnVKicj9Ji0jjQlrJjBxw0QAnmr8FF/2+BIHOwcbVyYiIiIiIiVFgQ4679SpE2vXrs1zcSIiUjAc7Bx4r/N7fPnQlxgNRr7d/S29f+1NQmqCrUsTERERERHJwj4vL+revTuvvPIK+/bto2nTpri5uWU536tXr3wpTkRE8ubZ5s9SwaMCg38bzLJjy2j3QzsWDF5AJc9Kti5NREREREQEyOPyPaPx1g1WBoOBjIyMeyqqqNDyPRG53209t5Wes3pyOekyZV3LMveRubSv0t7WZYmIiIiISDFWoMv3TCbTLb+KSyAlIlIcBFcMZvvI7TT2a8zlpMuE/BRC2LYwDUAXERERERGby1MoJSIi94/KXpXZ8MQGhtQfQropnTHLxvDUoqdITk+2dWkiIiIiIlKC5TmUWrt2LT179qRGjRrUqFGDXr16sX79+vysTURE8omrgyu/9PuFj7t8jNFg5Ps939Pqu1Ycu3rM1qWJiIiIiEgJladQ6ueffyYkJARXV1eef/55nn/+eVxcXOjcuTMzZ87M7xoLXVhYGIGBgTRv3tzWpYiI5BuDwcCLrV9k+dDl+Lj6sCdyD02mNWHOgTm2Lk1EREREREqgPA06r1u3LqNGjWLcuHFZjk+aNIlvvvmGgwcP5luBtqRB5yJSXJ2PO8+Q34ew/kxmh+tzzZ7jk26f4GzvbOPKRERERETkflegg85PnDhBz549sx3v1asXJ0+ezMstRUSkEFXwqMBfoX/x2gOvAfDlji+p+XlN3ln3DlEJUTauTkRERERESoI8hVIBAQGEh4dnO75q1SoCAgLuuSgRESl49kZ73u38LsuGLsOvlB/n4s7x+urXCfg0gEd/f5St57baukQRERERESnG7PPyohdffJHnn3+ePXv20Lp1awA2btzI9OnTmTJlSr4WKCIiBevBGg9y6v9OMTdiLmHbw9hybguz9s9i1v5ZvNLmFd7u9Db2xjz9diEiIiIiInJLeZopBTB//nw++eQT6/younXr8q9//YvevXvna4G2pJlSIlIS7bq4i0+3fMrPf/8MQKeqnZjVfxbl3MrZuDIREREREbkf5DZPuetQKj09nffee48nnniCihUr3nOhRZlCKREpyWbvn82Ti54kMS2Rih4V+e2R3wiuGGzrskREREREpIgrsEHn9vb2fPjhh6Snp99TgSIiUrQNqj+IbSO3UbtMbc7FnaPtD235YfcPti5LRERERESKiTwNOu/cuTNr167N71pERKSICSwbyLaR2+hftz9ppjSeWPQE7294nzyu/BYREREREbHK0+Ta7t2788orr7Bv3z6aNm2Km5tblvO9evXKl+JERMT2PJw8mPvIXF4Lf433N77Pq+GvEpUQxSfdPsFoyNP/2xAREREREcnboHOj8dZ/CTEYDGRkZNxTUUWFZkqJiGT16eZPGf/neACGNhjK972/x9HO0cZViYiIiIhIUVJgM6UATCbTLb+KQyAVFhZGYGAgzZs3t3UpIiJFyrhW4/ip70/YG+35Zd8vPDzzYa4kXbF1WSIiIiIich+6606ptLQ0XFxc2LNnD/Xr1y+ouooEdUqJiORs2dFl9J/Tn+vp16ngXoGZ/WfSrnI7W5clIiIiIiJFQIF1Sjk4OFCpUqVi0RElIiJ5071mdzY/uZnaZWpzPv48HWd05O21b5Nh0u8NIiIiIiKSO3lavvef//yH1157jatXr+Z3PSIicp8I8gtix6gdhAaFYjKbeGPNG3T9uSsnr520dWkiIiIiInIfyNOg88aNG3Ps2DHS0tKoXLlytt33du3alW8F2pKW74mI5M6Pe3/kuSXPkZiWiJOdE+NbjefVB17F3cnd1qWJiIiIiEghy22eYp+Xm/fp0yevdYmISDE0PGg4wRWCGb10NOEnw5m4YSI/7PmB9zq9R2ijUIyGPDXmioiIiIhIMZanTqmSQp1SIiJ3x2w288eRP3jxzxc5dvUYAG0C2jB7wGwqeFSwcXUiIiIiIlIYCmTQ+bZt22474DwlJYU5c+bczS1FRKQYMRgM9Krdi/3P7ufjLh/j7ujOxrMbafp1U9aeWmvr8kREREREpAi5q1CqVatWREdHW7/38PDgxIkT1u9jYmIYMmRI/lUnIiL3JSd7J15s/SK7nt5FQ9+GRCVG0fnHzny6+VPUoCsiIiIiInCXM6Vu/otETn+x0F82RETEooZ3DTY/uZlRf4zil32/MP7P8YSfDKdlxZaUdi6Nt4s3Pq4+tK/SHkc7R1uXKyIiIiIihShPg85vx2Aw5PctRUTkPubq4MpPfX+iZcWWjFsxjiVHl7Dk6JIs1zQp34Tw4eF4OXvZpkgRERERESl0+R5KiYiI3MxgMDCmxRhaVWzF3Ii5RCdFcy35GteSr7Hr4i52XdxFt5+7sfKxlXg4aWMJEREREZGS4K5DqYiICCIjI4HMpXqHDh0iISEBgCtXruRvdflg8eLFvPjii5hMJv7973/z1FNP2bokEZESq6l/U5r6N81ybF/UPjrO6Mi289vo/kt3VgxbQSnHUjaqUERERERECovBfBdDoIxGIwaDIce5UZbjBoPhtjv0Fab09HQCAwNZvXo1np6eNG3alE2bNlGmTJlcvT63WxiKiMi92X1xN51+7ERMcgztKrdj6aNLcXVw5XLSZY5fPU5CagLtKrfDyd7J1qWKiIiIiMgd5DZPuatOqZMnT95zYYVp27Zt1KtXjwoVKgDQvXt3/vzzT+0QKCJSxDQu35g/h/1JyE8hrDu9jpqf1yQhNYH41HjrNQ9UeoAljy7R8j4RERERkWLCeDcXV65cOVdf+WXdunX07NkTf39/DAYDCxYsyHZNWFgYVapUwdnZmeDgYLZt22Y9d+HCBWsgBVChQgXOnz+fb/WJiEj+aV6hOcuGLqOUYykuJlwkPjUeAwYCPAIo5ViKDWc2EPJjCFevX7V1qSIiIiIikg/uKpQqbImJiQQFBREWFpbj+dmzZzN+/HgmTJjArl27CAoKolu3bly6dKmQKxURkfzQOqA1u5/ezeIhi4l4LoKk/yRxZtwZ1j6+ljIuZdh+YTsdZ3TkUqL+Oy8iIiIicr8r0qFU9+7deeedd+jbt2+O5ydNmsTIkSMZMWIEgYGBTJ06FVdXV77//nsA/P39s3RGnT9/Hn9//0KpXURE8qaGdw161OpB3bJ1cbZ3BqBJ+SasfXwtvm6+/B31N+2nt+dC/AUbVyoiIiIiIveiSIdSt5OamsrOnTsJCQmxHjMajYSEhLB582YAWrRowf79+zl//jwJCQksW7aMbt263fKeKSkpxMXFZfkSEZGioV65eqwbsY6KHhU5dOUQ9b6sx9ilY/k76m9blyYiIiIiInlw34ZSV65cISMjA19f3yzHfX19iYyMBMDe3p5PPvmEjh070qhRI1588cXb7rw3ceJEPD09rV8BAQEF+h5EROTu1CpTi/Uj1hNYNpCY5Bi+2P4FQVODCP42mG92fkN8SvydbyIiIiIiIkVCnkOp9PR0Vq1axbRp04iPz/xLwIULF0hISMi34vJDr169OHLkCMeOHWPUqFG3vfbVV18lNjbW+nX27NlCqlJERHKrilcV9j27jxXDVjAgcAAORge2nd/GqMWjKP9JeZ5a9BRbz23FbDbbulQREREREbkN+7y86PTp0zz44IOcOXOGlJQUunTpgru7Ox988AEpKSlMnTo1v+vMxsfHBzs7O6KiorIcj4qKws/PL0/3dHJywsnJKT/KExGRAmQ0GOlavStdq3flUuIlftz7I9/u+pbD0Yf5bvd3fLf7O+qXq0/9cvXJMGVgMpvIMGfg7exN7zq96Va9G072+u+9iIiIiIgt5SmU+r//+z+aNWvG3r17syyH69u3LyNHjsy34m7H0dGRpk2bEh4eTp8+fQAwmUyEh4czZsyYe7p3WFgYYWFhZGRk5EOlIiJSkMq5leOl1i/xYqsX2XBmA9/s+oa5EXPZf2k/+y/tz3b993u+x8PJg961e/NI4CN0qNIBdyd3G1QuIiIiIlKyGcx5WN9QpkwZNm3aRO3atXF3d2fv3r1Uq1aNU6dOERgYSFJSUr4Ul5CQwLFjxwBo3LgxkyZNomPHjnh7e1OpUiVmz55NaGgo06ZNo0WLFkyePJk5c+Zw6NChbLOm8iIuLg5PT09iY2Px8PC45/uJiEjhiEmOYeGhhcSmxGJnsMNoMGJntCPicgS/RfzG+fh/dmY1GozUK1uP4ArBtKzYkq7VuxLgqZmCIiIiIiJ5lds8JU+dUiaTKccuonPnzuHunn//t3nHjh107NjR+v348eMBCA0NZfr06QwaNIjLly/zxhtvEBkZSaNGjVi+fHm+BFIiInL/8nL2IrRRaI7nJnWbxOazm5kbMZcFhxZwOvY0+y7tY9+lfXy7+1tc7F34uufXDGs4rJCrFhEREREpWfLUKTVo0CA8PT35+uuvcXd35++//6Zs2bL07t2bSpUq8cMPPxRErYXmxuV7R44cUaeUiEgxdjH+IlvPb2XLuS2sOrGKnRd3AjC6+WgmdZuEo52jjSsUEREREbm/5LZTKk+h1Llz5+jWrRtms5mjR4/SrFkzjh49io+PD+vWraNcuXL3VHxRoeV7IiIlS4Ypg7fWvsVb694CoHVAa+Y+Mhd/d38bVyYiIiIicv8o0FAKID09nV9//ZW///6bhIQEmjRpwtChQ3Fxcclz0UWNQikRkZJp8ZHFDJs3jNiUWHzdfFk4eCHBFYNtXZaIiIiIyH2hQEOp5ORknJ2d76nAokzL90RE5NjVY/Sb3Y99l/bh6uDKvIHz6Fajm63LEhEREREp8go0lPLw8KBv374MGzaMzp07YzQa76nYokqdUiIiJVtCagL95/Tnz+N/4mB04Me+PzK4/uBbXh+bHMu60+vYcWEH/er2I8gvqBCrFREREREpGgo0lJo/fz4zZ85kyZIleHp6MmjQIIYNG0azZs3uqeiiRqGUiIikZqQyfP5wZh+YjQEDn3f/nNEtRpOSnsKR6CMcvHKQHRd2sPrUanZd3IXJbALAw8mDlY+tpEWFFjZ+ByIiIiIihavAZ0oBxMfH89tvvzFr1iz++usvqlWrxrBhw3jjjTfyessiRaGUiIhA5gD055c9z5c7vgSgilcVzsSesQZQN6rhXQMnOycOXD6Ap5Mnq4avopl/8fqfNiIiIiIit1MoodSNIiIiGDp0KH///TcZGRn5cUubUyglIiIWZrOZt9a+xZtr37Qe83DyoK5PXRqUa0C7yu3oWLUjFT0qkpCaQPdfurPhzAa8nL0IHx5Ok/JN8r2mdFM6yenJlHIsle/3FhERERHJq0IJpZKTk1m0aBEzZ85k+fLl+Pr6MmTIEN5///283rJI0KBzERG5lb2Re7mSdIW6ZetSvlR5DAZDjtfFp8Tz4C8PsunsJko7l2ZGnxlcS77G31F/szdqLxfiLzAwcCAvtX4JN0e3u64jOimarj93ZW/kXrpU78Kj9R+lT50+uDu53+tbFBERERG5JwUaSq1YsYKZM2eyYMEC7O3tGTBgAEOHDqVdu3b3VHRRo04pERG5F3EpcXT7uRtbzm255TV+pfx4q8NbjGg8Anujfa7ue+36NTr/2JndkbuzHHexd6FX7V78r8P/qO1T+55qFxERERHJqwINpVxdXXn44YcZOnQoDz30EA4ODvdUbFGlUEpERO5VbHIsfWf35e+ov6lfrj5BvkE09G2IndGOt9e9zYlrJwCoV7Yen3b7lC7Vu9z2fjHJMXT5qQs7LuygrGtZfuz7I1vPbeWXfb9w9OpRAMq4lGHFsBU09W9a4O9PRERERORmBRpKxcfH4+5e/JcHKJQSEZGClJKewlc7vuKttW9xLfkaAC+1eon3Or+Hg132/+ETmxxL15+7su38NnxcfVgdupr65eoDmTOvdl3cxbNLnmX7he14OHmw5NElPFDpgTvWcSnxEu6O7rg4uOTvGxQRERGREim3eYrxbm5oYTabiYuLu+WXiIiI3JmTvRMvtHyB488f59lmzwLw8eaPaftDW07FnLJel5qRyvJjy62BlLeLN6seW2UNpAAMBgNN/ZsSPjyc9pXbE5cSR9efurLy+MpbPv9S4iWeXfws/p/4E/BpAO+ue5fY5NgCe78iIiIiIjfKdaeUnZ0dFy9epFy5chiNxhwHu5rNZgwGw32/+54GnYuIiC3MOziPJxc9SUxyDF7OXkxoP4G9UXtZcGgBMckxAJR2Lk348HAal298y/skpSUxYM4Alh1bhqOdI1MenELbSm2p7l0dZ3tnktKSmLxlMu9veJ/41Pgsr/Vy9uL5Fs/zaINHOXD5AFvPbWXL+S0ciT5Cj5o9+KTrJ3g6exbkj0FERERE7nP5vnxv7dq1tGnTBnt7e9auXXvba9u3b3931RZRWr4nIiKF7VTMKQb/Npit57dmOe7r5kv/uv15Pvj5XA0xT81I5dHfH+X3g79bjxkwUNmrMsnpyUQmRALQzL8ZH3X5iAvxF3h3/btEXI647X0DPAL4ofcPdK7W2XrMbDaz5dwWNp3dROPyjXmg0gM42jnezdsWERERkWKkQGdKnTlzhoCAgGzdUmazmbNnz1KpUqW7r7gIUiglIiK2kJaRxhur32D+ofl0qdaFR+o9QpuANtgZ7e7qPummdN5a+xbLjy3ncPRh4lL+WWJfybMSEztPZHD9wRgNmav5TWYT8w7O493171oHs7es0JLgisGUcSnDi3++yPFrxwEY03wMIxqP4PeI35m5f2aW5Ybuju50qd6FHjV70L5ye6qWrmp9hoiIiIgUfwUaSt24lO9G0dHRlCtX7r5fvmehUEpERIoLs9nMpcRLHIk+QmxKLCHVQnC2d77l9RmmjGwhWEJqAi+vfJmvdnyV7fpSjqVoW6ktOy/u5FLipSznXOxdqONTh8CygTT2a8zwoOGUdSubP29MRERERIqcAg2ljEYjUVFRlC2b9Q+Up0+fJjAwkMTExLuvuAhSKCUiIpLdimMreHLRk1xKvET3mt15tP6j9KzdE1cHV0xmEzsv7GTp0aUsPbaUvZF7SclIyfJ6F3sXRjUdxYutXiTAM8B63Gw2cy7uHCdjThKXEkdcShzxKfHEp8aTkp5CmimNtIw00k3p2BntcHVwtX7ZG+2JSojiYsJFLiZc5FLiJYJ8g3ix1YtULV21sH9EIiIiIiVagYRS48ePB2DKlCmMHDkSV1dX67mMjAy2bt2KnZ0dGzduvIfSiw6FUiIiIjmzhESlHEvd9roMUwYnY05y4NIBIi5HMO/QPHZc2AGAg9GBYQ2H4e3izZ7IPeyJ3EP09eh8rdPOYMejDR7llQdeIbBsIOfizrHs6DKWHlvK2lNrcbRzxLeUL75uvpRzK0cdnzoMqjeImmVq5msdIiIiIiVJgYRSHTt2BDKHnrdq1QpHx3+GmDo6OlKlShVeeuklata8v/8gp933RERECobZbGbViVW8t+E91pxak+28vdGeKl5V8HL2wsPJAw8nD0o5lsLJzgkHowMOdg44GB3IMGeQlJZk/UozpVHOtRzl3ctTvlR5PJ09+fnvn1lxfIX13jW8a3Ds6rFc1dnMvxmP1n+UQfUH4e/un19vX0RERKREKNDleyNGjGDKlCnFPqhRp5SIiEjB2XR2E9/u+hZXB1ca+TWisV9j6pWrd9tZV3dr54WdvLfhPeYdnAdk7kAYXDGYh2o8RLca3XC0cyQqIYpLiZeITIjkr1N/sfL4SjLM/8zHrF66urW+Rn6NqOxVGTcHN9wc3XBzcMPVwTXb5i8iIiIiJVmBhlIlhUIpERGR4uHwlcMcunKINpXa4OPqc9trLyVeYu6BuczcP5NNZzfd8d6lHEvxaP1HGdNiDA18G+RXySIiIiL3rQIPpXbs2MGcOXM4c+YMqampWc7NmzcvL7cschRKiYiIlGxXkq6wN3Jv5syrqMy5V1EJUSSmJZKUlpTt+naV2zGm+Rj61u2LvdHeBhWLiIiI2F6BhlK//vorw4cPp1u3bvz555907dqVI0eOEBUVRd++ffnhhx/uqfiiQqGUiIiI3IrJbCIpLYkdF3YQtj2M+QfnW5f9tarYioWDF1LWrewd7iIiIiJS/OQ2TzHm5ebvvfcen376KX/88QeOjo5MmTKFQ4cOMXDgQCpVqpTnokVERETuF0aDkVKOpehQpQNzH5nLqRdO8d+2/8XTyZPN5zbT6rtWHIk+YusyRURERIqsPIVSx48fp0ePHkDmrnuJiYkYDAbGjRvH119/na8FioiIiNwPKnpU5O1Ob7PlqS1U9arK8WvHafVdKzae2Vgoz99/aT8vrniRvZF7C+V5IiIiIvcqT8MOSpcuTXx8PAAVKlRg//79NGjQgJiYGJKSss9XEBERESkp6vjUYfOTm+k5qyfbL2yn84+d+bHvjwysN/CWr4lOimbT2U2cuHYi8yvmBJEJkbg5uOHp7ImHkweeTp40Kd+EPnX64O3ibX1tTHIME1ZPIGx7GBnmDFYcX8G+Z/dpR0AREREp8vIUSrVr146VK1fSoEEDHnnkEf7v//6Pv/76i5UrV9K5c+f8rrHQhYWFERYWRkZGxp0vFhEREbmJbylfVoeuZui8oSw8vJDBvw3G3dGd7jW7Z7v2SPQRWn3XiqvXr+bq3s8sfoYu1bswMHAgqRmpvPbXa1xJugJkLik8cPkAf538i87V7v7PZGazmW93fcuyY8sY1nAYvWv3xs5od9f3EREREcmNPA06v3r1KsnJyfj7+2Mymfjwww/ZtGkTNWvW5L///S+lS5cuiFoLnQadi4iIyL3IMGXw1B9PMX3PdLxdvNk1aheVvSpbzyekJtDy25YcuHyAAI8AWlRoQbXS1ahWuhr+7v5cT7tObEosscmxXEm6wrJjy9h3aV+259T1qctn3T9jwaEFhG0Po2etniwasuiuar16/SpPLXqK+YfmW49V9arKCy1fYESjEbg7uef9ByEiIiIlSoHuvldSKJQSERGRe5WSnsIDPzzAjgs7aObfjA0jNuBk74TZbGbI70OYfWA2fqX82DVqF+Xdy9/xfgcvH2TOgTnMiZhDTHIML7V6iTEtxuBg58DhK4epE1YHAwaOjj1Kde/quapx3el1DJ03lHNx53AwOjCkwRAWH1ls7d7ydPKkZpmamMwm65ezvTNVvKpQ2bMyVbyqUMmzEh5OHrjYu+Di4IKLvQsVPSriZO90Tz8/ERERuf/keygVFxeX64cXlwBHoZSIiIjkh1Mxp2j6dVOuXr/Ks82e5cseXzJlyxReWPEC9kZ7Voeu5oFKD+TLs7r/0p3lx5bzQvALfPrgp7e91mQ28fbat3lr3VuYzCZqetfk1wG/0qR8E5LSkvhx7498uuXTPO8iWL10dXY9vQsPJ/05SkREpCTJ91DKaDTecWCm2WzGYDAUm1lMCqVEREQkvyw/tpyHfnkIM2bGtRzH59s+J92UzpQHp/B88PP5+pzuv3THw8mDc+PO3XLZXVJaEsPnD+f3g78DEBoUyhcPfUEpx1JZrjOZTWw6u4nY5FiMBqP1Kz41ntMxpzkde5pTMac4G3eWxNRErqdfJyktidjkWNJMaTzX7DnCeoTlWMOBSweo4FEBL2evW76f+JR4LR0UERG5z+R7KLV27dpcP7x9+/a5vrYoUyglIiIi+WnC6gm8te4t6/dD6g/hl36/5OtOeSazibphdTkSfYTPu3/OmBZjsl0TmRBJ7197s+38NhztHPn64a8JbRSabzUAmcPWf8wctr5hxAbaVGqT5fz3u7/nyUVP4u/uz+YnN1PJs1K2e3yx7QvGrRjHU42f4quHv8rX+kRERKTgaKZUPlAoJSIiIvkpw5TBQzMf4s/jf1K/XH22PLkFN0e3fH9O2LYwxiwbQ03vmhwacwijwWg9t//SfnrM7MGZ2DN4u3izYNAC2lZum+81ADy16Cm+2/0ddXzqsPvp3TjbOwPw5/E/eeiXh8gwZ3bX1/Gpw4YRGyjjWsb62h/3/kjogn+CsrmPzGVA4IACqVNERETyV27zFOMtz9zB+vXrGTZsGK1bt+b8+fMA/PTTT2zYsCGvtxQREREp1uyMdvz2yG980/MbVj22qkACKYDQRqF4OHlw9OpRlh9bjslsYsu5Lfwn/D+0+b4NZ2LPUNO7Jlue3FJggRTAR10+wq+UH4euHOLdde8CsDdyLwPmDCDDnEG/uv2o6FGRQ1cO0XNWT5LSkgBYeGghTyx8AsjcWRDgmcXPcDH+YoHVKiIiIoUvT6HU77//Trdu3XBxcWHXrl2kpKQAEBsby3vvvZevBYqIiIgUJ+5O7jzV5Cl8S/kW2DNKOZbiycZPAvDckuco/0l5Wn3Xivc2vEdcShztKrdj85ObqVmmZoHVAFDapTRfdP8CgPc3vs+yo8voMbMH8anxdKjSgVn9Z7F86HK8nL3YfG4zg38bzMrjKxn02yAyzBk83uhxdj+9m8Z+jYm+Hs1TfzyFmvxFRESKjzwt32vcuDHjxo1j+PDhuLu7s3fvXqpVq8bu3bvp3r07kZGRBVFrodPyPREREblfnbh2ghqf1cBM5h/1PJw8eLDGg/Sq1YtH6j2Co51jodXSb3Y/5h+ab/2+rk9dNj6xkdIupQHYeGYjIT+FkJyebL2mT50+zH1kLvZGew5cOkDTr5uSkpHCtIenMarpqEKrXURERO5egS7fO3z4MO3atct23NPTk5iYmLzcUkRERETyUbXS1fix74/8q/W/WPXYKi7/6zKzB8xmaMOhhRpIAXzx0Bd4OnkC4Ovmy9KhS62BFECbSm34tf+v1tlXnap2Ylb/Wdgb7QGoV64e73XO7MYfv2I8x68ev+Wztp3fRoOvGtBxRkd2XdxVUG9JRERE8kGeQik/Pz+OHTuW7fiGDRuoVq3aPRdla2FhYQQGBtK8eXNblyIiIiKSZ8MaDuPDLh/SuVrnQg+ibuTv7s+s/rPoUbMHy4Yuo4pXlWzX9K7Tm4WDF/JKm1dYMGiBdSi6xQstX6B95fYkpiUy6LdB7I3cm+W82Wxm8pbJPPD9A+y/tJ81p9bQ7OtmPP3H01xOvJzluhPXTvB7xO8cuHSgQN6viIiI5E6elu9NnDiRn3/+me+//54uXbqwdOlSTp8+zbhx43j99dcZO3ZsQdRa6LR8T0RERKToOB1zmoZTGxKXEgdAj5o9eK3ta9T1qcuIhSNYeHghAAMCB+Bo58jMfTMB8HL2YmSTkRy/dpxNZzcRmfDPqIlGfo0Y1mAYQxoMwd/dn9SMVE5cO8HR6KNEJkQyIHBAlq6uG0UnRbPqxCr6B/a3dnWJiIhI7vOUPIVSZrOZ9957j4kTJ5KUlLlLipOTEy+99BJvv/123qsuYhRKiYiIiBQtBy8f5K11bzHnwBxMZhMA7o7uxKfG42jnyKSuk3iu+XMYDAbWn17P2GVj2RuVtavKwehAYNlAIi5HkGZKA8CAgUqelTgbd9Z6X4CHaz3MH0P+yFaH2Wymw4wOrDu9jlcfeNW6vFBEREQKOJSySE1N5dixYyQkJBAYGEipUqW4fv06Li4ueb1lkaJQSkRERKRoOhp9lA83fsiMvTNIM6VR1asqcx+ZS1P/plmuyzBl8N3u71h3eh0NyjWgTaU2NC3fFBcHF6KTopkbMZef//6ZjWc3Wl9TyrEUNb1rsu/SPtJN6Sx9dCnda3bPct9f9//KkN+HAJkh14HnDhTYboaxybE8segJfN18+SDkA9yd3AvkOSIiIvmlUEKpG6WkpBAWFsaHH36o3fdEREREpFCcizvHXyf/onft3ng6e+b5PqdiTnE29iw1vGvgV8oPg8HAv/78Fx9v/phaZWqx79l91rlciamJ1P6iNufjz+Pp5ElsSizda3RnyaNLMBgMd/3s3Rd3k5CaQNvKbbOdM5lN9JrViyVHlwBQq0wt5gyYQ5BfUJ7fq4iISEErkN33UlJSePXVV2nWrBmtW7dmwYIFAPzwww9UrVqVTz/9lHHjxt1T4SIiIiIiuVXRoyLDg4bfUyAFUMWrCm0rt6W8e3lrsPR6+9fxdfPlSPQRPtv6mfXaiRsmcj7+PFW9qrJuxDocjA4sO7aMxUcWZ7vvj3t/5MUVL5KYmpjjc49EH6Hldy1pN70d76x7h5v/f/GE1RNYcnQJzvbOVHCvwJHoIwR/G8y0HdOyXSsiInK/uatQ6o033uCrr76iSpUqnDp1ikceeYRRo0bx6aefMmnSJE6dOsW///3vgqpVRERERKTQeDh58H7I+wD8b+3/uBh/keNXj/PRpo8AmNRtEg19GzK+1XgAXljxAsnpyQCkm9IZs3QMoQtCmbRlEi+vfDnb/c1mM6OXjiY1IxWA11e/zvgV460zrX6P+J131r8DwDc9v2HvM3vpUbMHKRkpPLPkGR6Z+wiLDi/iStKVgv1BiIiIFJC7Wr5XrVo1Jk+eTK9evdi/fz8NGzbk8ccf57vvvstTq3JRp+V7IiIiIiWbyWyi1Xet2HZ+G6FBoVxLvsaiw4voUq0LK4atwGAwkJCaQO0vanMh/gLvdHyHMS3GMPj3wSw/thwDBsxk/nH7z2F/0qV6F+u9Z++fzeDfB+Ns78y4luOYuGEiAMODhjOu5Tge+P4BEtMSGd9yPJ90+8Raz6TNk3g1/FXSTenWe9UqU4vWAa15svGTPFDpgUL8CYmIiGRXIDOlHB0dOXnyJBUqVADAxcWFbdu20aBBg3uvuAhSKCUiIiIiW89tpeV3La3f2xvt+fuZv6lbtq712Kx9s3h03qO42LtQtXRVIi5H4Orgyi/9fiH8RDhfbP+Cih4V2ffsPrycvYhLiaPOF3W4mHCRtzq8xevtX+fnv3/m8QWPk2HOwN5oT7opnc5VO7N82HLsjfZZatp+fjvTdk5j09lNHLxyMMu5p5s+zQchH9zzkkYREZG8KpCZUhkZGTg6Olq/t7e3p1SpUnmvUkRERESkiAuuGMzjjR63fj+2xdgsgRTA4PqDaVe5HdfTrxNxOQJ/d3/Wj1hPnzp9eD/kfWp41+Bc3DleWP4CAG+sfoOLCRep6V2Tf7X5FwDDGg5j/qD5ONs7k25Kp4pXFWYPmJ0tkAJoXqE53/b6lojREUS/HM2SR5dYa5y2cxqBXway4NCCgvhxFHlms9m6BFJERIq2u+qUMhqNdO/eHScnJwD++OMPOnXqhJubW5br5s2bl79V2og6pUREREQEIDIhkoZfNcTFwYW/n/k7xy6kfVH76DCjA9VLV2feoHlU9KhoPbfp7Cba/tAWk9nE/zr8j/+t/R8msynbkj7Ltd/v/p6XWr9EHZ86d1XnmlNrGPXHKI5ePQpAnzp9eKPdGzQu3zjLdWazmXWn1zFl6xRSM1LpXbs3fer0oaxbWev5iMsRzI2Yy5/H/6RmmZq80uaVbGFcXiWkJjBgzgBqlanFlAen5NsokKS0JJpMa4KzvTMrhq3At5RvjtddSbqCl7NXjoGfiIjcuwJZvjdixIhcXffDDz/k9pZFmkIpEREREbGISY7BaDDi4XTrPxcmpyfjZOeUY8jyyqpX+GDjB9bvB9UbxK8Dfs33Oq+nXeftdW/z4cYPyTBnANC5amdeav0SXat35c/jf/LOunfYeHZjltcZDUbaVW5HE78mLD22lENXDmU5b8DAwHoD+W+7/1K/XP17qnHylsmMW5G5a/fPfX9maMOh93Q/i1/+/oVh84cBUL9cfVaHrsbH1cd63mw288W2Lxi3Yhy1fWqzeMhiqpaumi/PFhGRfxRIKHW/6tu3L2vWrKFz58789ttvuX6dQikRERERyS8p6Sk0+6YZ+y/tx93RnUNjDuHv7l9gz9t/aT8TN0xk9v7Z1nCqjEsZoq9HA+Bo58iTjZ+kgnsF5h2ax66Lu7K83tHOka7Vu/JwzYdZfnx5luWAD9d6mC7VutAmoA1BfkF31XGUlpFGjc9rcCb2DAClnUsTMToCv1J+Wa47cOkA41aMY3D9wTzR+Ilc3bvbz9348/if1u+blG9C+PBwvJy9SMtI4/llzzN151Tr+XJu5Vg0eBHBFYNzXb+IiNyZQqkbrFmzhvj4eGbMmKFQSkRERERsZv+l/Ty9+GnGthjL4PqDC+WZp2NOM2XrFL7Z9Q0JqQm4OrjyTNNneLH1i1lCsVMxp5h3cB4HLh2gY9WO9KzVM8syxT2Re3hn3Tv8fvD3LPd3dXClVcVWfNjlQ5qUb3LHeixD4cu6lqWiR0V2R+6md+3ezB8039phdiT6CO1+aEdUYhR2BjvWj1hPq4BWt73v+bjzVJpcCZPZxB9D/uCJhU9wOekyLSu2ZPaA2Tyx8AnCT4ZjwMAb7d9g0eFF7I7cjbO9Mz/3/Zn+gf3v5scqIiK3oVDqJmvWrOGLL75QKCUiIiIiJdK169fYcm4LzfybWWdH5cWBSwdYcGgBG89uZPO5zcQkxwBQ1asqEaMjcLZ3vuVrzWYzTb9uyu7I3bzV4S161+lNs6+bkWZKY2a/mQxpMIRTMado+0NbzsWdw9HOkdSMVKqVrsaep/fg7uR+y3t/uPFD/r3q37QJaMOGJzawN3IvHWd05FryNetuhm4ObszsP5NetXuRkJrA4N8Gs+TokszXh3xoHTovIiL3pkB23ysI69ato2fPnvj7+2MwGFiwYEG2a8LCwqhSpQrOzs4EBwezbdu2wi9UREREROQ+VtqlNN1rdr+nQAqgXrl6/Kfdf1g6dCnRL0ez79l9+Lv7czLmJJ9v/fy2r119ajW7I3fjYu/Cs82fpaFvQ15v9zoAY5aNYdfFXXT+sTPn4s5Rx6cO+5/dT2XPypy4doLnlz9/y/uazWZm7J0BQGhQKABBfkH8+difeDh5kG5Kp5JnJTY9uYletXsBUMqxFAsHL2Rsi7EAvLzqZbac23JPP5ubRVyO4MvtX2o3QBGRW7B5KJWYmEhQUBBhYWE5np89ezbjx49nwoQJ7Nq1i6CgILp168alS5es1zRq1Ij69etn+7pw4UJhvQ0RERERkRLHaDBSv1x93uv0HgDvrH+Hy4mXb3n9R5s+AuCJxk9YB5C/8sArNPZrzNXrV2n+TXNOXDtBtdLVWPXYKmqWqclPfX/CaDAyfc90fovIedXD7sjdRFyOwMnOiUfqPWI93sy/GeseX8eE9hPY9tQ2Gvo2zPI6O6Mdn3X/jIH1BgLwe0TWpYn36rH5jzF66Wh+2vtTvt63KElJT+HDjR+y/9J+W5ciIvchm4dS3bt355133qFv3745np80aRIjR45kxIgRBAYGMnXqVFxdXfn++++t1+zZs4f9+/dn+/L3L7jBkSIiIiIikumxoMdo7NeYuJQ4/rf2fzles//SfpYfW47RYGRcy3HW4w52DkzvMx17oz0ms4kAjwDCh4dTwaMCAG0rt+WVNq8AMOqPUZyLO5ft3jP2ZHZJ9anTBy9nryzngvyCeLPDm/iW8r1l/Y8EZgZZi44syv2bvoNzceesw+NvnsOVX0xmE9vOb+PNNW/S+cfOfL3z6wJ5zu18vOlj/r3q3/T5tQ9pGWmF/nwRub/ZPJS6ndTUVHbu3ElISIj1mNFoJCQkhM2bN+f781JSUoiLi8vyJSIiIiIit2c0GPmk6ycATN0xlYOXD2a75uNNHwPQr24/qntXz3KuoW9Dfuj9Az1r9WTV8FVU8aqS5fybHd6kmX8zriVf47H5j5Gcnmw9l5aRxsz9MwEYHjQ8T/V3q94NRztHjkQf4fCVw3m6x82WHl1q/ec/j/9JfEp8rl63+MhiKk+uzB+H/7jlNUeijxC6IBS/j/0I/jaY/639H3+d/It/rfwXKekp91x7biWlJTFl6xQAjl87zo97fyy0Z8vdSctIY8qWKRyJPmLrUkSyKNKh1JUrV8jIyMDXN+v/1fD19SUyMjLX9wkJCeGRRx5h6dKlVKxY8ZaB1sSJE/H09LR+BQQE3FP9IiIiIiIlRceqHelVuxcZ5gxeXvVylnPn484zc19mcPSv1jkPEx/WcBiLhiyiVpla2c452DnwS79fcHVwZc2pNbSf3p7zcecBWH5sOVeSruDr5kvX6l3zVLu7kzsdq3QE4I8jtw6D7oZlgDpASkYKy48tv+NrLideZsTCEZyJPcO0ndNued24FeP4ce+PXE66jIeTBwMCB1DOrRxxKXGsOrEqX+rPje93f8/lpMsYDZl/rXxr3Vu5DsV+3f8rK46tKMjy5AbTdk7jhRUvMHbZWFuXIpJFkQ6l8suqVau4fPkySUlJnDt3jlatct5O9tVXXyU2Ntb6dfbs2UKuVERERETk/vVhyIfYG+1ZfGQxiw4vYt7BeQyfP5wGXzUgzZRGu8rtaFGhRZ7uXatMLRYOXkhp59JsO7+Npl83ZeOZjdYB50MbDMXeaJ/n2nvW6gnAosP3voQvOT3ZGg5ZgrJ5h+bd8XUvrHiBK0lXANhwZgMZpoxs16Sb0ll3eh0AM/vN5Mq/rjD3kbkMqjcIgLkRc++5/txIy0izdr993OVjKrhX4EzsGb7d9e0dX7s3ci9Dfh9Cr197EZeS8+qUC/EXqBtWlzfXvJmfZZdYliWkm89u1uB9KVKKdCjl4+ODnZ0dUVFRWY5HRUXh5+eX789zcnLCw8ODn376iZYtW9K5c+d8f4aIiIiISHFV26c2zzZ7FoDev/am/5z+/PT3T1xLvkb5UuWtS/zyKqRaCNtHbqdBuQZEJUbRcUZHFh5eCOR96Z5Fz9qZodTGsxutwdDtpGaksv70+hz/gr/21FqS0pKo4F6BN9u/CcCSI0tu20W0+MhiZu6bidFgxMnOidiU2ByHh+++uJuE1AQ8nTwZWG8gDnYOAAwIHADAwsMLSc1IvWP99+rX/b9yOvY05dzK8UyzZ/hvu/8CmcPuk9KSbvtaS9dcakYqK4+vzPGan/b+xKErh/h1/6/5W7iNmc1mzGZzoT4zOinaGmTGp8Zz6MqhQn2+yO0U6VDK0dGRpk2bEh4ebj1mMpkIDw+/ZbdTfhg9ejQRERFs3769wJ4hIiIiIlIcTWg/gbKuZQGo4lWFcS3Hse7xdZwdd5Zm/s3u+f7Vvauz+cnNDKw3kDRTGummdIJ8gwjyC7qn+1byrEQjv0aYzKYs86Bu5aU/X6Ld9Ha8t/69bOcsS/ceqvkQwRWD8Xf3Jz41nvCT4dmuBYhNjuWZxc8A8GKrF2lXuR0A68+sz3atJVxoW7ktdkY76/E2AW3wK+VHTHIMf53864713wuT2cQHGz8A4IXgF3BxcOGJxk9QxasKkQmRfLX9q9u+dtb+Wdbvb/Wztgydj74enY+V20aGKYNlR5cxYM4AnN5x4qlFTxXq8/848keW8HTb+W2F+nyR27F5KJWQkMCePXvYs2cPACdPnmTPnj2cOXMGgPHjx/PNN98wY8YMDh48yLPPPktiYiIjRoywYdUiIiIiIpKTMq5l2PvMXg48d4ATz59gUrdJ2QKUe+Xm6Mav/X/lg5AP8HH14ZUHXsmX+/aq1Qu48xK+a9evWZepfbTpI65dv2Y9ZzabWXxkMQA9avbAaDDSt07mTuPzDua8hO/llS9zPv48Nbxr8GaHN2lbqS2Qcyi19vRaANpXbp/luJ3Rjn51+gEw90D2JXxbz22lxmc16DGzB9P3TM9S891acmQJBy4fwMPJg+eaPweAo50jE9pPAOD9je/fcrD7hjMbOBv3z5iUpceWZus2u5R4ic1nM+cAX71+9b5dbhaTHMN/wv9D5cmVeWjmQ/x+8HfSTGl8v+d7Ii5HFFod8w/NB8DNwQ0oGqFUXErcLZduSsli81Bqx44dNG7cmMaNGwOZIVTjxo154403ABg0aBAff/wxb7zxBo0aNWLPnj0sX7482/BzEREREREpGsq7lyewbCAGg6HAnmEwGHi5zctc/tdlBtcfnC/37FU7M5RacXzFbZfafbf7O66nXwcy/3I9ectk67lDVw5xMuYkjnaOdK6WOQ6kX93MsGjh4YWkm9Kz3Gv1ydV8vetrAL7t+S2uDq60rfz/Q6nT67Ms9TKZTdagytJNdSPLEr4FhxeQlpFmPZ5uSuepP57i+LXjLD26lBELR+D7sS8P/fLQLZfP3YrZbGbihokAPNvsWTydPa3nhjUcRq0ytbiSdIXPtn6W4+stS/cebfAobg5uRCZEsvvi7izXLD26FDNm63vO7c6FRc3AuQN5b8N7nI8/TxmXMrwQ/AKdq2b+mrDM4ypoiamJ/Hn8TwDGtRwH2DaUup52nffWv0eFSRWoMKkC761/j+tp1/N0r8iESHr/2tsaAhem62nX+WzrZ0QlRN35Yrktm4dSHTp0sK6rvfFr+vTp1mvGjBnD6dOnSUlJYevWrQQHBxdoTWFhYQQGBtK8efMCfY6IiIiIiBQdTco3wd/dn4TUBNacWpPjNRmmDMK2hwHwcK2HAZi8dbK188iydK9DlQ6UciwFZAZI3i7eXEm6woYzG6z3OhVzitAFoQA80/QZ2lfJ7H4KrhCMg9GBiwkXOXHthPX6fVH7iEmOwc3BjSblm2SrrV3ldpR1LcvV61dZfWq19fhX279i/6X9lHEpw4T2E6hfrj5ppjSWHVtG91+6c/LayVz/jNafWc/mc5txsnPihZYvZDlnb7S3ztD6aNNH2WZzpWakWgexj2g0gi7VuwDZl/Dd3Kl29frVXNVmNps5Gn2Uc3Hncvt2CszWc1tZeWIl9kZ7fu3/K+fHn+fTBz/lnU7vAPDz3z9bd5AsSCuOryA5PZlqpavxZJMnAdgbtZfk9OQ83e9i/EVm759913PLzGYzs/bNok5YHf7z139ISE0gITWB//z1H+qE1eHX/b/e9aytz7Z+xqLDi3hq0VN5fj959eX2L/m/5f/Hq+GvFupziyObh1JFkWZKiYiIiIiUPAaD4Y5L+JYcXcKpmFN4u3jza/9faVCuQZZuKUso1aNmD+tr7I321i6s+Qczl1KdjjlNxxkdORt3llplavFBlw+s17s4uFjnb924hM8yT6pNpTY57jRoZ7SzdmX9FvEbAJcTL/PGmsxVKO92epc3O7zJvmf3cXD0QVpWbEmGOSNXO+ZZfLrlUyAzVPIrlX3zqUH1BxHkG0RsSiwv/vlilnN/Hv+Tq9ev4uvmS8cqHa0/I8vPDDJ3LrR09hgNmX9dvV0olZiayB+H/+C5Jc9R/bPq1PqiFjU/r8kPu3/I9XsqCJZusmENhzGo/iCc7J0AaFmxJe0qtyPNlJalw+52pmyZwgcbPsjTgHTL0r0+tftQ2bMy5dzKkW5KZ0/knru+F8ATi55g8O+D6TWrF4mpibl6TXRSNK2/b82j8x7lTOwZKnpU5Oe+P/Nz35+p6FGRM7FnGPL7EFp/35qzsWfvfEMyQ645B+YAEJUYxY97f8zT+8mr3ZGZ3X2bz20u1OcWRwqlRERERERE/j9LeLToyKIcQwDLsrSnGj+Fm6ObdY7S5K2TORVzytoJdWMoBVjnPc07NM8aSJ2KOUUN7xr8NfwvPJw8slxvnSt1+p9Q6lbzpG5kWcI3/9B80k3p/Pev/xKTHEMjv0Y81eSfAdt1fOrwUquXAPh+z/dZlvvdislsYvXJzA6sG+91I6PByLSHp2HAwI97f8yyPPCXfb8AMLj+YOyMdjxU8yEgcznZ5cTLQOZyxsS0RPzd/QksGwjcetj5lnNb8P3Yl16/9uKrHV9xMuYkBgwkpyfzxKInePqPp3PVQbPp7CYmrJ6QbzOO9l/az8LDCzFg4N9t/p3t/MutXwZg2s5pxCTH3PZecSlxjFsxjlfCX2Hj2Y13VUdaRpp1aVvfun0xGAy0qNACyNsSvqiEKGtguOL4CkJ+CslVF9unWz5ly7ktuDm48XbHtzk85jBDGw5laMOhHB5zmLc6vIWrgytbzm1h6LyhuZohtjtyN8evHbd+/9Gmj8gwZdz1e8ory0yww1cOazbWPVIolQMt3xMRERERKZk6Vu2Im4Mb5+LOZesmibgcQfjJcIwGo3XAd9+6fa3dUv1m9yPdlE4dnzpU966e5bVdqnex3rf5N805GXOS6qWrszp0NRU8KmSr4+Yd+Mxms7VT6nahVIcqHSjjUoYrSVf4dPOnfLPrGwA+7/55tmHzvWr3wtfNl8iESP448scdfzYHLx8kNiUWVwfX2+52GFwxmDEtxgDwzJJnSEpLIiE1gYWHFgIwtMFQAPzd/Wnk1wgzZpYdWwZgraNXrV74uPoAt+6UWnViFYlpiZRzK8ezzZ5l0eBFxL4Sy9sd38aAga93fU3bH9pyOub0LWu9EH+B7r905611b/HwzIdJSkvKdk1cShxDfh9CyI8hxCbH3unHxPsb3gegf2B/6vjUyXa+e83u1Ctbj/jUeKbtmHbbe12Iv2Cdr/XJ5k/u+OwbrTm1hpjkGMq5laNVxczd61v45z2UmhsxF5PZRPXS1fF28WbLuS20+6HdHZchWn4Nf9rtU/7b7r+4Orhaz7k6uPJ6+9fZ8/Qe3BzcWH9mPVO2TLlzLf9/mP+DNR7E28WbY1eP3XIjgfxmMps4dOUQAGbM2Waiyd1RKJUDLd8TERERESmZnO2d6Vq9K5DZ/XTj7JzPt34OQO/avansVRnI7AyydEtZlvTc3CVluW+PWpnHLyddplrpaqwOXU1Fj4o51tGmUhsMGDh69SiRCZEcunKIy0mXcbZ3ti7ty4m90d6629/Lq17GjJmhDYbyQKUHsl3rYOfAE42fADK7du7EslSpRYUWOS4fvNG7nd6lokdFTlw7wf/W/I+FhxZyPf06NbxrZKnf8rNaenQpZrPZumyyZ+2eeLt4A7cOpSwzq55o9ARf9viSnrV74u7kzn/b/ZdlQ5fh7eLNjgs7aPJ1E7ae25rjPcYsHWPtdFl/Zj19Z/fNMuT+YvxF2k9vz6/7fyX8ZDj/Wvmv277vE9dO8Ov+XwF49YGc5w0ZDUZebpPZLTV56+TbdnNdjL9o/eeFhxZyNProbZ9/owWHFgCZv14tgeS9dEpZ3tfo5qNZ9/g6/N39OXD5AG2+b3PLulLSU6w/+5yG81vULFOTSd0mAfBq+KscvHzwlteazWbmRGQu3RvRaARjmmcGoB9szNsSx5zuv/DQQk7FnMrx/JnYM9aNDgB2XNhxz88syRRKiYiIiIiI3GB40HAAftz7I02/bsq289uISY7hx78z59Y8H/x8lust3VIWOYVSAEPqDwGgqldVVoeuJsAz4JY1eDl70cA3857rT6+3Lt1rVbGVdT7RrViW8AG4ObjxQcgHt7zWsgzvz+N/3nHg+aazmwBoXbH1ba8DcHdy58uHvgQyO3wsM5Yerf9oll0ZLT+rFcdXsP3Cds7Hn8fVwZVOVTvh7Xz7UOpyUuaSP0tH1Y261ejGrlG7aObfjKvXr/LwrIc5fvV4lmvmHZzH/EPzsTfaM+3habg6uPLn8T8Z8vsQ0k3pHL5ymNbft2ZP5B7KuJQB4Jtd37DqxKpbvu+PNn5EhjmDbtW75TiM3mJw/cFU9KhIZEIkP//98y2vu5jwTyhlxsyUrXfuIoLMbp4FhxcAWENKgOYVMlcDHb16NNcD5CEziNl4diMGDAysN5B65eqx8YmN1PSuyenY04xYOCLH1+28uJOUjBR8XH2oVabWbZ8xsslIulXvRkpGCqELQrPtVGmx6+IuTlw7gYu9Cz1q9mBs8Fhc7F3YeXEnf538K9fv6Va+3/09fWb3sW5CcDPL0j2LHRcVSt0LhVIiIiIiIiI36FOnD7/2/5WyrmXZf2k/rb5rRfdfupOUlkSDcg2yLZ+7sVvK08kzx64kyOxYCR8ezo5RO6jkWemOdVjnSp1Zb126d7tuE4tOVTtZu4z+2+6/OS4PtKhWupq1M+xOA88tnVKtAlrdsQbI7HZ6JPARMswZHLh8AIBHGzya5ZoWFVpQxqUMMckxvBb+GgDdqnfD2d45151SZd3K5ni+sldlVoeupkn5JlxJusJDMx8iOilzPlVMcgxjlmZ22Lzc+mVGNR3FwsELcbRzZP6h+fSd3Zc237fhVMwpanrXZNvIbdaOnKcWPUV8Sny2512Mv8j3e74H4LW2r932Z+No58j4luOBzHlIt5qjZOmUsvx6+WHPD7kKk7af386F+Au4O7rTqWon63FvF29qeNcA7q7DxzJUvG3lttZfT1W8qrDyscyZYZvObrLOBbuRZcbaA5UeyBJG5sRgMPBdr+/wcvZi+4XtfLAh5zDVUkuPWj1wc3TDx9WHJxtn7iz4wcZbB7AWaRlpt5yhlpyezJtr3wQyd1DM6TpLF1dZ18xfd+qUujcKpXKgmVIiIiIiIiXboPqDiBgdwdAGmYOXt5zbAsDYFmNz/Mt1v7r9mPbwNOYNmoeDnUOO9zQYDFkCozu5MZTKzZBzCwc7B37q+xNvtHuD8a3G3/H6UU1GAbcfeH71+lXrHJ2WFVvmqn6AKQ9OwdPJE4Cm5ZtS26d2lvN2RjserPEgAOEnwwHoWasnwB1DKUsIklOnlEUpx1IsHrKYSp6VOBJ9hD6z+5Ccnswrq17hYsJFapWpxevtXwcgpFoIcx+Zi53BjsVHFhN9PZrm/s3Z+MRGqpWuxsSQiVT1qsrp2NP8e1X2AeafbvmU1IxUWge0tv67u52nmjyFh5MHR6KPsPPCzhyvsXRKDag7gCDfIJLSkpi6Y+od723Zde+hmg9l66zLyxI+y9I9S7efRWWvyta5YCuOr8j2Oss8qdz8PAAqeFTg8+6Zy2T/t/Z/2ea6mc1m5kZkzpMaGDjQevzF1i9iZ7Bj5YmVt/xZQuZ8sMqTK9P8m+Y5Botfbv+Sc3HnAEjJSMnWFQVw8EpmKDWo3iAAjl09dseB9XJrCqVyoJlSIiIiIiLi4+rDz/1+ZsmjS6hWuhr1y9VnaMOhOV5rMBgY1XRUlq6Ue9W2cuZf5PdE7uFC/AUcjA65DoQeqvkQ/+v4PxztHO94bW4GnltCuVplat02BLpZeffyTH14Ku6O7vyrdc7zmG5c7mjAYJ29VcY1c8ncrXbfs3ZKuebcKXVjDUsfXYqHkwcbzmygy09drDO0vn74a5ztna3X9qrdi5/6/oSLvQu9avdidehqaydWKcdSfNsrs5vsqx1fWXci3H9pP88ufta6M+NrD7x2x64gyFziaFn2eav5RZZQqrx7eV5s9SIAn2/7PMvcq5tFJ0Xz3e7vgMyw9GZ3O+z8aPRRdl7ciZ3Bjv51+2c7371GdwDrsHoLk9nExjOZOwbeqnswJ0MbDKVvnb6kmdIY9NsgohKirOd2XtzJyZiTuDq4WndvhMyurUH1M0OiDzd9eMt7rz21losJF9kbtZcxy8ZkOReXEsd7698DwMXexfq8m1mCqgcqPUBVr6pA5pJCyRuFUiIiIiIiIrfxUM2HOP78cfY+szfLzmEFzd/dn2qlq1m/b1GhBS4OLvn+nNwMPN989v8v3auYu6V7NxpcfzBxr8ZZQ4ObdavRDaMh86+mrQJaUc6tHHD7Timz2XzbmVI3q1euHvMGzsPeaG9dUjaqySjaV8neeTakwRCu/vsqCwcvxM3RLcu5TlU78XTTpwEYsXAE7ae3p8FXDZi6cyopGSn0qNkjS1hyJ5ZB95bunJtZlu+VL1WeQfUH4e/uT2RCpLVzKScvr3yZK0lXqF+ufpZ5UhY3dkrlZjC45Vkh1UJyXCppCaWWH1tOhinDejzicgTXkq/h6uBKY7/Gd3yOhcFgYOrDUwnwCOBI9BFCfgqxBpCWpXsP13o427+bl1tnDo//LeK3LAPib2RZBguZM+N+3Puj9ftPNn1C9PVo6vjUsf47vrnrymw2WzulAssGWof2awlf3imUEhERERERyQVLcFKYbpwhlZule3k1sslI4NYDzy3zpFoH3HnI+d3ydvG23rdXrV5ZjkPOoVRSWpJ117pbzZS6Wedqnfm2Z2anUwX3CnzQ5dbzh27snrrZh10+JMAjgNOxp1l3eh12BjsGBA5gdehq/hjyR666pCwCPDKH3d8ylLqhU8rRzpGxLcYCmcPjcwqU1p1eZ51rNe3haTkuJW3k1wh7oz1RiVGcjTt72/rMZjOz9s8CMsPFnLQKaIWnkydXr19l+4V/VhtZwr+WFVvecknrrZRzK0f48HDKlyrP/kv76fpTV65dv2YNpR4JfCTba4L8gmhavikms+mWw+gtywktYdJzS57jSPQRLiVeYtKWzN3/3un4jjW4u7lTKjIhkpjkGIwGI7XK1FIolQ8USomIiIiIiBRRN87iyamrJ79ULV3VOvD85plFGaYMtp7fCuStUyo3vnzoS1574DXGBo+1HrtdKGXpknKyc8LNwS3b+VsJbRTKvmf3seeZPXg5e+WpVg8nD2YPmE3bSm15vd3rnHrhFHMfmUuHKh3uKpCCGzql4nMOpSITIoHMTimAp5s+jauDK/su7WPG3hlZrk1JT+HpxZkdPqOajLplgOji4EJD34ZA5jDv29l/aT8HrxzE0c6RPnX65HiNvdHe+mtn2dF/lvBZh5wH5H7p3o1qlqlJ+PBwyrqWZXfkbpp/05zTsaezLd27UZdqXQBYdTJ7KJWYmmgNmX7t/ysdqnQgMS2RQb8NYsLqCSSkJtDMvxn96vajqX9TAPZG7c2yC6ClS6pa6Wo42TsplMoHCqVyoEHnIiIiIiJSFFi6oxztHAssELKw7C739a6vSUhNsB7ff2k/CakJuDu6E1g2sECe3cC3Ae92fjfL8sgbQ6mbu4Ju3HnvboOg+uXq39VcrJy0CmjFuhHreKvjW9ZgKS9ut3zvetp16wDt8u6ZoVRpl9I81+w5IHP54PgV463D6T/a9BGHrhyinFs53g95/7bPze1cKcvSvYdqPnTbEC+nuVLWIeeVczfkPCd1y9Zl1fBVeLt4c/zacSBzEP6tltGGVAsBYNWJVdl+zWw5t4V0UzoBHgFUK12NX/r9go+rD3si9zB1Z2YQO7HzRAwGAzW8a+Du6E5yerJ1tz34Z+c9y+egSfkmAJyMOWnd2VHujkKpHGjQuYiIiIiIFAXVvavzc9+f+X3g77g7uRfos3rU6kFN75rEJMcwfc906/FNZzcBmcuw7Ix2BVrDjcq4ZA46TzelE5+adae03Oy8dz+4XShl6ZJytne27mAIMDFkonVo/KdbPqXDjA6sObWGd9a9A8DkbpMp7VL6ts+1zpW6cOtQymw28+uBzFBqcL2cl+5ZWHZQ3H5hO5cSL3Em9gxnYs9gZ7C7q90ac9LQtyErH1tp/RncvAPgjdpUaoOzvTMX4i9Yd4u0sMyTalu5LQaDAX93f6b3nm4936lqJ2uoZTQYrYHTjUv4LEPO6/rUBcDL2Ysa3jWyXSe5p1BKRERERESkCBvacCgP13q4wJ9jNBh5oeULAEzeMtk6tNoyT6qgO7Vu5uLgYp3tdPMSPsvyvTvtvFfUWUKp83HnswwJhxvmSZUqn6UbzN5oz4ddPmT+oPl4Onmy6ewmOs7oSEpGCl2qdbnl7KcbWUKpHRd2ZFmedqONZzdy4toJXB1c7/jrr7x7eesw8xXHVliX7jUu35hSjqXuWM+dNCnfhG0jtzGz30x61e51y+uc7Z2tS15XnliZ5Zylc6tdpX/mtPWo1YO3OmR2u03qOinL9U3LZy7hu3HYuWX5niWUAnK9hG/jmY20+6GddYc/yaRQSkRERERERAAIDQqltHNpjl87zuIji4GCHXJ+J7eaK2VZvne/d0r5lfLDzmBHhjmDqMSoLOesO+/9/6V7N+tTpw87R+2kkV8jIDOQ+arHV7lazljHpw6eTp4kpSURfiI8x2sss8WG1B+Sbae7nNy4hO9e50nlpFaZWgxpMOSO7+/GJXwWqRmp1l/HNy8nfL3965wdd5Ygv6Asx2/XKXXjMtZm5Ztlu+5GCakJPL/sedr+0Jb1Z9bzxbYvblt/SaNQSkRERERERABwc3Tj6aaZw7InbZnEpcRLHLt6DIDgisGFXs+tQinL8r37vVPKzmiHv7s/kH0J342dUrdS3bs6m57YxCddP2HxkMVU966e6+c+3uhxIHMJ4M2uJF1hbsRcAJ5p9kyu7tm9ZmYoteL4CtaeXgvAA5XyL5TKLUsotebUGuu8rZ0XdpKcnoyPq0+WLqfbsQw73xO5h3RTOteuX7MGh3V86livu12n1MrjK6n/ZX0+3/Y5ZjJnXF1OupzjzokllUIpERERERERsRrTYgz2RnvWnV5H2LYwILMzJK+71d2L4t4pBbeeK2XtlLpNKAWZyxzHtxpP52qd7+q5zwc/j9FgZMXxFRy4dCDLuRl7ZpCakUqT8k2socudtKzYEi9nL65ev2rtKLJFKNXIrxFlXMoQnxrP9guZc6ItS/ceqPRArgfj1ypTi1KOpbiefp3DVw5bl+5V9KiYZb5b4/KNMWDgTOwZLiVeAjLncb0W/hpdf+7K6djTVPaszB9D/gAyZ6RZBtiLQikRERERERG5QQWPCgyqNwiA9zZkzr9pXbHwl+7BP8POb97ZzDpTyu3+7pSC24RSCbdfvnevqpWuRp86fYDMGWIWZrOZaTunAfBM09x1SUHmrKuu1btav6/pXRPfUr75UuvdMBqM1oDOsoTPOuS8Uu53AjQajNY5WTsv7sw25NzCw8mD2j61M6+7sBOT2cT/Lf8/Jm6YCGTuarn/uf08XOthPJw8AKzhlSiUylFYWBiBgYE0b97c1qWIiIiIiIgUunEtxwFYh2C3CijcIecWJalT6mzs2SzHc7N8716NbzkegJ/+/skalKw+tZqjV4/i7ujOkAa33ukuJ5a5UnB3AVB+C6mauYRv5YmVZJgyrDOu2lVud7uXZXPjsPODlzM7pW6cJ2Vh6Sbben4rT//xNJ9v+xyAqT2m8vlDn1uHvZdzKwf8E6qKQqkcjR49moiICLZv327rUkRERERERApdU/+mWf4Cb4sh53CbmVLFZPc9uKFTKj7n5Xt+pfwK7NmtA1rT3L85KRkpfLX9K+CfAefDGg67653zHqzxoPWfbbF0z8IyV2rLuS1sObeF2JRYSjmWsg6Fzy3LXKmdF3cScSXnTin4Z9j5+xve59vd32I0GJneezpPN3s6y3WWX6/qlPqHQikRERERERHJxtJF4+PqQ60ytWxSgzWUSi7+nVKFvXwPwGAwML5V5r/nL3d8yemY08w/NB/AOvD+bviV8qN37d54u3hnCagKW9XSValWuhrppnTeWf8OkBnA2Rvt7+o+lh34dkfuts7dqls2h1Dq/3dKpWSkYG+0Z1b/WYQ2Cs12nbVTKlGdUhZ3929ERERERERESoRetXsxtcdUapWphdFgm36GnDql0k3p1u+Lw0ypAI8AIGsolW5KtwYXBbl8D6B/3f5U9KjIubhz9Jndh3RTOq0qtiLILyhP9/tt4G+YzCYc7RzzudK706VaF6btnMbyY8uBvC0nrF2mNm4ObiSmJZKUlgTkvHyvkV8jPJ08uZ5+nTkD5tC7Tu8c76dOqezUKSUiIiIiIiLZGAwGnm72NB2rdrRZDZZQ6sZB5zcGVJbz9zNLp9T5uPOYzCYAohKiMGPGzmBX4MGbg50Dz7d4HoA9kXuAvHVJWdgb7W0eSME/S/gs7naeFICd0S7Lkj8fV58cu/PcHN3YPnI7+5/df8tACv7plFIo9Q+FUiIiIiIiIlIkWXbfuzGIsnQQebt43/VyrKLIr5QfRoORNFOaNaywLN3zLeVbKF1qI5uOxM3BDQAvZy8G1htY4M8saB2rdMSAAQBHO0daVGiRp/tYhp1DzvOkLGqWqUnNMjVvey9LwKhB5/9QKCUiIiIiIiJFUk7L9yzzpIrDkHPI7FSyDDO3LOGLTIgECn7pnoWXs5e1O+qpxk/h4uBSKM8tSGVcy1hnQrWo0AJne+c83ccy7BxyXrp3N9QplZ1CqRyEhYURGBhI8+bNbV2KiIiIiIhIiXVjKGU2m4F/ukyKw5Bzi5uHnVt23ivIIec3ez/kff4Y8gfvdn630J5Z0PrV7QfAwzUfzvM9ctsplRvWQefqlLJSKJWD0aNHExERwfbt221dioiIiIiISIllCaXSTGkkpiUCN3RKFYMh5xbZQinLznuF1CkFmR1bD9d6uEjMg8ovL7d5mbWPr+XF1i/m+R61fWrjYp/ZOZbTznt3Q4POs7v/F+CKiIiIiIhIseTq4IqjnSOpGalcvX6VUo6lrDOlfFyKT6fUzTvwWTulCjGUKo7sjfZ5GnB+8z3e7PAmW85toX3l9vd0L0un1JWkK5jMJpvtalmUKJQSERERERGRIslgMFDGpQwXEy4SnRRNJc9KxbpT6mzcWeCGTqlCXL4nt/Zym5fz5T6WJacms4mr168WqyWoeaVYTkRERERERIqsm4edl4iZUjZYvicFz8HOgdLOpQEt4bNQKCUiIiIiIiJF1s2hVHHbfQ+KxqBzKRzWYeeJGnYOCqVERERERESkCCtpnVIms4nIhEhAnVLFkWXZqTqlMimUEhERERERkSLrlp1SxWimlL+7PwYMpGakcvjKYdJMaQD4lvK1cWWS3yydUjmFUqdjTvPz3z+z6+Kuwi7LZhRKiYiIiIiISJFVxqUMANHXozGbzf/svleMOqUc7RytAdT2C9uBzPfnaOdoy7KkAFiWnVo6/m609vRaHpv/GC+vzJ/B6vcDhVIiIiIiIiJSZN3YKZWYlkhKRgpQvGZKwT9L+Lafzwyl/Er52bIcKSC365Q6ce0EANVKVyvUmmxJoZSIiIiIiIgUWTeGUpYuKWd7Z1wdXG1ZVr6zhFLbLmwDNE+quLpdp9Txa8cBqF66eqHWZEsKpXIQFhZGYGAgzZs3t3UpIiIiIiIiJdqNodSNO+8ZDAZblpXvKrpnhlJ7I/cC2nmvuFKnVFYKpXIwevRoIiIi2L59u61LERERERERKdGydEoVw533LCydUpblieqUKp4soZSl6+9GCqVEREREREREipAyrv8MOi+OO+9ZWEIpC4VSxZPl1+7NnVKJqYlEJkQCUN1by/dEREREREREbC6nmVLFsVMqwDMgy/davlc8WTqloq9Hk25Ktx4/GXMSgNLOpfFy9rJFaTahUEpERERERESKLEsolZqRyunY00Dx23kP1ClVUpRxKYOBzHlo0UnR1uPHr/7/IeclqEsKFEqJiIiIiIhIEebm4IaD0QGAw9GHgeLZKeXv7p/le3VKFU92RjvrktQbl/CVxHlSoFBKREREREREijCDwWDtljp8JTOUKo6dUs72zlnelzqlii/rsPOkf4adH7/2/zulSqtTSkRERERERKTIsHSWnIk9AxTPTin4Zwmfu6M7bo5uNq5GCoolfFSnlEIpERERERERKeIsnVJmzEDx3H0P/gmltHSveLN0SimUUiglIiIiIiIiRZwllLIorp1SAR6ZO/Bp6V7xZumUsuwmmWHKsO6+p+V7IiIiIiIiIkXIzaFUcZwpBVDJsxKQfSc+KV5u7pS6EH+B1IxUHIwOJe7fvb2tCxARERERERG5HW/nf0IpA4ZsIVVxMTxoOCeuneDZ5s/auhQpQJblp5ZB55Yh51W8qmBntLNZXbagUEpERERERESKNMugc8jsmiquf3Ev716eaT2n2boMKWA3d0qV1HlSUAKW7509e5YOHToQGBhIw4YNmTt3rq1LEhERERERkbtwY2dUcZ0nJSXHzaHU8auZnVIlbZ4UlIBOKXt7eyZPnkyjRo2IjIykadOmPPTQQ7i5aXtNERERERGR+8GNoVRx3XlPSg7roPP/v3zvREzJ7ZQq9qFU+fLlKV8+c+cCPz8/fHx8uHr1qkIpERERERGR+0SWUKqYDjmXksPSKRWTHENqRqq1U6okhlI2X763bt06evbsib+/PwaDgQULFmS7JiwsjCpVquDs7ExwcDDbtm3L07N27txJRkYGAQEB91i1iIiIiIiIFBYt35PipLRLaewMmXPRriRdsc6Uqu5d8pbv2TyUSkxMJCgoiLCwsBzPz549m/HjxzNhwgR27dpFUFAQ3bp149KlS9ZrGjVqRP369bN9XbhwwXrN1atXGT58OF9//XWBvycRERERERHJP2Vc/hl0rk4pud8ZDUZruHrs6jGir0cDUNWrqi3LsgmbL9/r3r073bt3v+X5SZMmMXLkSEaMGAHA1KlTWbJkCd9//z2vvPIKAHv27LntM1JSUujTpw+vvPIKrVu3vu11KSkp1u/j4uLu4p2IiIiIiIhIQVCnlBQ3Zd3KEpUYxZZzW4DMJX3uTu42rqrw2bxT6nZSU1PZuXMnISEh1mNGo5GQkBA2b96cq3uYzWYef/xxOnXqxGOPPXbbaydOnIinp6f1S8v8REREREREbK+UYynsjZk9FRp0LsWBZa6UJZQqifOkoIiHUleuXCEjIwNfX98sx319fYmMjMzVPTZu3Mjs2bNZsGABjRo1olGjRuzbty/Ha1999VViY2OtX2fPnr3n9yAiIiIiIiL3xmAwWLul1CklxcHNoVT10iVvnhQUgeV7Be2BBx7AZDLl6lonJyecnJwKuCIRERERERG5W03LNyX8ZDj1y9W3dSki98wyG+1iwkWg5HZKFelQysfHBzs7O6KiorIcj4qKws/Pr8CeGxYWRlhYGBkZGQX2DBEREREREcm9BYMXEJscq+V7UixYOqUsSmqnVJFevufo6EjTpk0JDw+3HjOZTISHh9OqVasCe+7o0aOJiIhg+/btBfYMERERERERyT1HO0cFUlJs3LyLpDqlbCQhIYFjx45Zvz958iR79uzB29ubSpUqMX78eEJDQ2nWrBktWrRg8uTJJCYmWnfjExERERERERG5n9zcKaVQykZ27NhBx44drd+PHz8egNDQUKZPn86gQYO4fPkyb7zxBpGRkTRq1Ijly5dnG36en7R8T0REREREREQKyo2hlLO9M+Xdy9uwGtsxmM1ms62LKKri4uLw9PQkNjYWDw8PW5cjIiIiIiIiIsXAkegj1P6iNgB1feoSMTrCxhXlr9zmKUV6ppSIiIiIiIiISHFzY6dUde+SOeQcFEqJiIiIiIiIiBQqTydPHIwOAFTzKpnzpEChVI7CwsIIDAykefPmti5FRERERERERIoZg8Fg3U1SnVKSxejRo4mIiGD79u22LkVEREREREREiqGKHhUBqF2mto0rsR2b774nIiIiIiIiIlLSfPbgZ6w5tYaQaiG2LsVmFEqJiIiIiIiIiBSy4IrBBFcMtnUZNqXleznQTCkRERERERERkYJlMJvNZlsXUVTFxcXh6elJbGwsHh4eti5HRERERERERKTIy22eok4pEREREREREREpdAqlRERERERERESk0CmUEhERERERERGRQqdQKgcadC4iIiIiIiIiUrA06Pw2NOhcREREREREROTuaNC5iIiIiIiIiIgUWQqlRERERERERESk0CmUEhERERERERGRQqdQSkRERERERERECp29rQsoisLCwggLCyM9PR3IHNAlIiIiIiIiIiJ3ZslR7rS3nnbfu41z584REBBg6zJERERERERERO47Z8+epWLFirc8r1DqNkwmExcuXMDd3R2DwWDrcu5JXFwcAQEBnD179rbbMYpI/tBnTqTw6PMmUrj0mRMpXPrMyf3IbDYTHx+Pv78/RuOtJ0dp+d5tGI3G2yZ69yMPDw/9h0ykEOkzJ1J49HkTKVz6zIkULn3m5H7j6el5x2s06FxERERERERERAqdQikRERERERERESl0CqVKCCcnJyZMmICTk5OtSxEpEfSZEyk8+ryJFC595kQKlz5zUpxp0LmIiIiIiIiIiBQ6dUqJiIiIiIiIiEihUyglIiIiIiIiIiKFTqGUiIiIiIiIiIgUOoVSJUBYWBhVqlTB2dmZ4OBgtm3bZuuSRIqFN998E4PBkOWrTp061vPJycmMHj2aMmXKUKpUKfr3709UVJQNKxa5v6xbt46ePXvi7++PwWBgwYIFWc6bzWbeeOMNypcvj4uLCyEhIRw9ejTLNVevXmXo0KF4eHjg5eXFk08+SUJCQiG+C5H7x50+c48//ni23/cefPDBLNfoMyeSOxMnTqR58+a4u7tTrlw5+vTpw+HDh7Nck5s/S545c4YePXrg6upKuXLl+Ne//kV6enphvhWRe6JQqpibPXs248ePZ8KECezatYugoCC6devGpUuXbF2aSLFQr149Ll68aP3asGGD9dy4ceP4448/mDt3LmvXruXChQv069fPhtWK3F8SExMJCgoiLCwsx/Mffvghn332GVOnTmXr1q24ubnRrVs3kpOTrdcMHTqUAwcOsHLlShYvXsy6desYNWpUYb0FkfvKnT5zAA8++GCW3/dmzZqV5bw+cyK5s3btWkaPHs2WLVtYuXIlaWlpdO3alcTEROs1d/qzZEZGBj169CA1NZVNmzYxY8YMpk+fzhtvvGGLtySSN2Yp1lq0aGEePXq09fuMjAyzv7+/eeLEiTasSqR4mDBhgjkoKCjHczExMWYHBwfz3LlzrccOHjxoBsybN28upApFig/APH/+fOv3JpPJ7OfnZ/7oo4+sx2JiYsxOTk7mWbNmmc1mszkiIsIMmLdv3269ZtmyZWaDwWA+f/58odUucj+6+TNnNpvNoaGh5t69e9/yNfrMieTdpUuXzIB57dq1ZrM5d3+WXLp0qdloNJojIyOt13z11VdmDw8Pc0pKSuG+AZE8UqdUMZaamsrOnTsJCQmxHjMajYSEhLB582YbViZSfBw9ehR/f3+qVavG0KFDOXPmDAA7d+4kLS0ty+evTp06VKpUSZ8/kXxw8uRJIiMjs3zGPD09CQ4Otn7GNm/ejJeXF82aNbNeExISgtFoZOvWrYVes0hxsGbNGsqVK0ft2rV59tlniY6Otp7TZ04k72JjYwHw9vYGcvdnyc2bN9OgQQN8fX2t13Tr1o24uDgOHDhQiNWL5J1CqWLsypUrZGRkZPmPFICvry+RkZE2qkqk+AgODmb69OksX76cr776ipMnT9K2bVvi4+OJjIzE0dERLy+vLK/R508kf1g+R7f7PS4yMpJy5cplOW9vb4+3t7c+hyJ58OCDD/Ljjz8SHh7OBx98wNq1a+nevTsZGRmAPnMieWUymXjhhRdo06YN9evXB8jVnyUjIyNz/H3Qck7kfmBv6wJERO5X3bt3t/5zw4YNCQ4OpnLlysyZMwcXFxcbViYiIpL/Bg8ebP3nBg0a0LBhQ6pXr86aNWvo3LmzDSsTub+NHj2a/fv3Z5lNKlJSqFOqGPPx8cHOzi7bDg1RUVH4+fnZqCqR4svLy4tatWpx7Ngx/Pz8SE1NJSYmJss1+vyJ5A/L5+h2v8f5+fll29gjPT2dq1ev6nMokg+qVauGj48Px44dA/SZE8mLMWPGsHjxYlavXk3FihWtx3PzZ0k/P78cfx+0nBO5HyiUKsYcHR1p2rQp4eHh1mMmk4nw8HBatWplw8pEiqeEhASOHz9O+fLladq0KQ4ODlk+f4cPH+bMmTP6/Inkg6pVq+Ln55flMxYXF8fWrVutn7FWrVoRExPDzp07rdf89ddfmEwmgoODC71mkeLm3LlzREdHU758eUCfOZG7YTabGTNmDPPnz+evv/6iatWqWc7n5s+SrVq1Yt++fVnC4JUrV+Lh4UFgYGDhvBGRe6Tle8Xc+PHjCQ0NpVmzZrRo0YLJkyeTmJjIiBEjbF2ayH3vpZdeomfPnlSuXJkLFy4wYcIE7OzsGDJkCJ6enjz55JOMHz8eb29vPDw8GDt2LK1ataJly5a2Ll3kvpCQkGDtwIDM4eZ79uzB29ubSpUq8cILL/DOO+9Qs2ZNqlatyuuvv46/vz99+vQBoG7dujz44IOMHDmSqVOnkpaWxpgxYxg8eDD+/v42elciRdftPnPe3t7873//o3///vj5+XH8+HFefvllatSoQbdu3QB95kTuxujRo5k5cyYLFy7E3d3dOgPK09MTFxeXXP1ZsmvXrgQGBvLYY4/x4YcfEhkZyX//+19Gjx6Nk5OTLd+eSO7Zevs/KXiff/65uVKlSmZHR0dzixYtzFu2bLF1SSLFwqBBg8zly5c3Ozo6mitUqGAeNGiQ+dixY9bz169fNz/33HPm0qVLm11dXc19+/Y1X7x40YYVi9xfVq9ebQayfYWGhprNZrPZZDKZX3/9dbOvr6/ZycnJ3LlzZ/Phw4ez3CM6Oto8ZMgQc6lSpcweHh7mESNGmOPj423wbkSKvtt95pKSksxdu3Y1ly1b1uzg4GCuXLmyeeTIkVm2ojeb9ZkTya2cPmuA+YcffrBek5s/S546dcrcvXt3s4uLi9nHx8f84osvmtPS0gr53YjkncFsNpsLPwoTEREREREREZGSTDOlRERERERERESk0CmUEhERERERERGRQqdQSkRERERERERECp1CKRERERERERERKXQKpUREREREREREpNAplBIRERERERERkUKnUEpERERERERERAqdQikRERERERERESl0CqVEREREipkqVaowefJkW5chIiIiclsKpURERETuweOPP06fPn0A6NChAy+88EKhPXv69Ol4eXllO759+3ZGjRpVaHWIiIiI5IW9rQsQERERkaxSU1NxdHTM8+vLli2bj9WIiIiIFAx1SomIiIjkg8cff5y1a9cyZcoUDAYDBoOBU6dOAbB//366d+9OqVKl8PX15bHHHuPKlSvW13bo0IExY8bwwgsv4OPjQ7du3QCYNGkSDRo0wM3NjYCAAJ577jkSEhIAWLNmDSNGjCA2Ntb6vDfffBPIvnzvzJkz9O7dm1KlSuHh4cHAgQOJioqynn/zzTdp1KgRP/30E1WqVMHT05PBgwcTHx9vvea3336jQYMGuLi4UKZMGUJCQkhMTCygn6aIiIiUBAqlRERERPLBlClTaNWqFSNHjuTixYtcvHiRgIAAYmJi6NSpE40bN2bHjh0sX76cqKgoBg4cmOX1M2bMwNHRkY0bNzJ16lQAjEYjn332GQcOHGDGjBn89ddfvPzyywC0bt2ayZMn4+HhYX3eSy+9lK0uk8lE7969uXr1KmvXrmXlypWcOHGCQYMGZbnu+PHjLFiwgMWLF7N48WLWrl3L+++/D8DFixcZMmQITzzxBAcPHmTNmjX069cPs9lcED9KERERKSG0fE9EREQkH3h6euLo6Iirqyt+fn7W41988QWNGzf+f+3dTyi0WwDH8d+8MpvX+LOYBZpMGoNhKH9KFAuTIjZKkii7KRsLSxvlTxQbsVESWVggK4lJKYvxX0KRP1GU/wvZYN670J2M9733zu16n839fuosntM5z3nOWT39Os951NXVFawbGRmRzWbT4eGhnE6nJCklJUW9vb0h9/x4PpXdbldHR4e8Xq+GhoZkNpsVExMjk8kUMt5nPp9Pu7u7Oj09lc1mkySNjY0pIyNDa2trys/Pl/QeXo2OjspisUiSGhoa5PP51NnZqaurK72+vqq6ulpJSUmSJLfb/R9WCwAAgJ1SAAAAv9XOzo6WlpYUFRUVLGlpaZLedyf9KTc396e+i4uLKi0tVWJioiwWixoaGnR3d6fn5+ewxz84OJDNZgsGUpLkcrkUGxurg4ODYJ3dbg8GUpIUHx+v6+trSVJ2drZKS0vldrtVU1Oj4eFhPTw8hL8IAAAAv0AoBQAA8Bs9PT2pqqpK29vbIeXo6EjFxcXBdt+/fw/pd3Z2psrKSmVlZWlqakobGxsaHByU9H4Q+leLjIwMuTaZTAoEApKkiIgILSwsaG5uTi6XSwMDA0pNTdXp6emXPwcAAPj/IJQCAAD4ImazWW9vbyF1OTk52tvbk91ul8PhCCmfg6iPNjY2FAgE1NfXp4KCAjmdTl1eXv7jeJ+lp6fr4uJCFxcXwbr9/X09Pj7K5XKFPTeTyaSioiK1t7dra2tLZrNZMzMzYfcHAAD4jFAKAADgi9jtdvn9fp2dnen29laBQEDNzc26v79XXV2d1tbWdHx8rPn5eTU1Nf1toORwOPTy8qKBgQGdnJxofHw8eAD6x/Genp7k8/l0e3v7ip0R/gAAAVdJREFUy8/6PB6P3G636uvrtbm5qdXVVTU2NqqkpER5eXlhzcvv96urq0vr6+s6Pz/X9PS0bm5ulJ6e/u8WCAAA4ANCKQAAgC/S2tqqiIgIuVwuWa1WnZ+fKyEhQSsrK3p7e1NZWZncbrdaWloUGxurb9/++lUsOztb/f396unpUWZmpiYmJtTd3R3SprCwUF6vV7W1tbJarT8dlC6973CanZ1VXFyciouL5fF4lJycrMnJybDnFR0dreXlZVVUVMjpdKqtrU19fX0qLy8Pf3EAAAA+Mf3gX74AAAAAAAAwGDulAAAAAAAAYDhCKQAAAAAAABiOUAoAAAAAAACGI5QCAAAAAACA4QilAAAAAAAAYDhCKQAAAAAAABiOUAoAAAAAAACGI5QCAAAAAACA4QilAAAAAAAAYDhCKQAAAAAAABiOUAoAAAAAAACGI5QCAAAAAACA4f4AZiV263kAZD4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["最终能量: -16.258207 ± 0.003038\n", "与参考值相对误差: 0.022092%\n"]}], "source": ["import matplotlib.pyplot as plt\n", "# 可视化训练结果\n", "plt.figure(figsize=(12, 8))\n", "\n", "# 绘制能量收敛曲线\n", "plt.subplot(2, 1, 1)\n", "plt.plot(history[\"iterations\"], history[\"energy_mean\"], '-b', label='Trained Energy')\n", "plt.fill_between(\n", "    history[\"iterations\"],\n", "    [m - e for m, e in zip(history[\"energy_mean\"], history[\"energy_error\"])],\n", "    [m + e for m, e in zip(history[\"energy_mean\"], history[\"energy_error\"])],\n", "    color='blue', alpha=0.2\n", ")\n", "plt.axhline(y=reference_energy, color='r', linestyle='--', label=f'Refer Energy: {reference_energy}')\n", "plt.xlabel('Iterations')\n", "plt.ylabel('Energy')\n", "plt.legend()\n", "plt.title('Energy Convergence')\n", "\n", "# 绘制相对误差曲线（如果有参考值）\n", "if history[\"relative_error\"] is not None:\n", "    plt.subplot(2, 1, 2)\n", "    plt.plot(history[\"iterations\"], history[\"relative_error\"], '-g')\n", "    plt.xlabel('Iterations')\n", "    plt.ylabel('Relative Error (%)')\n", "    plt.title('Relative Error <PERSON>urve')\n", "    plt.yscale('log')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 打印最终结果\n", "final_energy = history[\"energy_mean\"][-1]\n", "final_error = history[\"energy_error\"][-1]\n", "final_rel_error = history[\"relative_error\"][-1] if history[\"relative_error\"] is not None else None\n", "\n", "print(f\"最终能量: {final_energy:.6f} ± {final_error:.6f}\")\n", "if final_rel_error is not None:\n", "    print(f\"与参考值相对误差: {final_rel_error:.6f}%\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}