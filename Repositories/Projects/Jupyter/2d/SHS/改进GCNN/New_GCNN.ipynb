{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： False\n", "可用设备： [CudaDevice(id=0)]\n"]}], "source": ["# %%\n", "import os\n", "import jax\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '0'\n", "\n", "import netket as nk\n", "import jax\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# %%\n", "import os\n", "import logging\n", "import sys\n", "import jax\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from functools import partial\n", "from netket.nn import log_cosh\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator\n", "from netket.vqs import VariationalState\n", "from scipy import sparse as _sparse\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.operator import LocalOperator as _LocalOperator\n", "from tqdm.notebook import tqdm\n", "from jax import tree"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import jax.numpy as jnp\n", "from flax import linen as nn\n", "from jax.nn.initializers import zeros, lecun_normal\n", "from netket.utils import HashableArray\n", "from netket.jax import logsumexp_cplx\n", "from netket.nn.activation import reim_selu\n", "from netket.nn.symmetric_linear import DenseSymmMatrix, DenseEquivariantIrrep\n", "\n", "# 默认GCNN初始化器\n", "default_gcnn_initializer = lecun_normal(in_axis=1, out_axis=0)\n", "\n", "def identity(x):\n", "    return x\n", "\n", "class GCNN_Parity_Irrep(nn.Module):\n", "    \"\"\"基于不可约表示实现的带宇称对称性的GCNN\"\"\"\n", "    \n", "    symmetries: HashableArray  # 对称操作数组\n", "    irreps: tuple  # 不可约表示矩阵列表\n", "    layers: int  # 层数 \n", "    features: tuple  # 每层特征数\n", "    characters: Has<PERSON>leArray  # 指定所需对称表示的字符\n", "    parity: int = 1  # 宇称值\n", "    param_dtype: any = np.complex128  # 参数数据类型\n", "    input_mask: any = None  # 输入掩码\n", "    equal_amplitudes: bool = False  # 是否强制等幅\n", "    use_bias: bool = True  # 是否使用偏置\n", "\n", "    def setup(self):\n", "        # 第一层：对称化线性变换\n", "        self.dense_symm = DenseSymmMatrix(\n", "            symmetries=self.symmetries,\n", "            features=self.features[0],\n", "            param_dtype=self.param_dtype,\n", "            use_bias=self.use_bias,\n", "            kernel_init=default_gcnn_initializer,\n", "            bias_init=zeros,\n", "            mask=self.input_mask,\n", "        )\n", "\n", "        # 使用元组推导式创建固定数量的层\n", "        # Flax要求层必须是模块属性，而不是列表项\n", "        self.equivariant_layers = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.layers - 1)\n", "        )\n", "        \n", "        self.equivariant_layers_flip = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.layers - 1)\n", "        )\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        if x.ndim < 3:\n", "            x = jnp.expand_dims(x, -2)  # 添加特征维度\n", "\n", "        # 处理原始输入和翻转输入\n", "        x_flip = self.dense_symm(-1 * x)\n", "        x = self.dense_symm(x)\n", "\n", "        for layer in range(self.layers - 1):\n", "            x = reim_selu(x)\n", "            x_flip = reim_selu(x_flip)\n", "\n", "            # 混合原始和翻转路径\n", "            x_new = (\n", "                self.equivariant_layers[layer](x)\n", "                + self.equivariant_layers_flip[layer](x_flip)\n", "            ) / np.sqrt(2)\n", "            x_flip = (\n", "                self.equivariant_layers[layer](x_flip)\n", "                + self.equivariant_layers_flip[layer](x)\n", "            ) / np.sqrt(2)\n", "            x = jnp.array(x_new, copy=True)\n", "\n", "        # 连接原始和翻转路径的输出\n", "        x = jnp.concatenate((x, x_flip), -1)\n", "\n", "        # 构建宇称为1的字符\n", "        par_chars = jnp.expand_dims(\n", "            jnp.concatenate(\n", "                (jnp.array(self.characters), jnp.array(self.characters)), 0\n", "            ),\n", "            (0, 1),\n", "        )\n", "\n", "        # 应用复值logsumexp\n", "        x = logsumexp_cplx(x, axis=(-2, -1), b=par_chars)\n", "\n", "        return x\n", "\n", "\n", "def create_gcnn(symmetries, layers, features, mask=None, characters=None):\n", "    \"\"\"创建GCNN模型的简化函数\"\"\"\n", "    \n", "    # 处理特征参数\n", "    if isinstance(features, int):\n", "        features = (features,) * layers\n", "    \n", "    # 处理字符参数\n", "    if characters is None:\n", "        characters = HashableArray(np.ones(len(np.asarray(symmetries))))\n", "    else:\n", "        characters = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(characters)\n", "    \n", "    # 获取不可约表示\n", "    sym = HashableArray(np.asarray(symmetries))\n", "    irreps = tuple(HashableArray(irrep) for irrep in symmetries.irrep_matrices())\n", "    \n", "    # 处理掩码\n", "    input_mask = HashableArray(mask) if mask is not None else None\n", "    \n", "    # 创建和返回模型\n", "    return GCNN_Parity_Irrep(\n", "        symmetries=sym,\n", "        irreps=irreps,\n", "        layers=layers,\n", "        features=features,\n", "        characters=characters,\n", "        parity=1,\n", "        param_dtype=np.complex128,\n", "        input_mask=input_mask,\n", "        equal_amplitudes=False,\n", "    )\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-15 11:20:13.442778: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-04-15 11:20:13.442819: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n"]}], "source": ["# 设置模型和哈密顿参数\n", "N_features = 4\n", "N_layers = 4\n", "J1 = 0.8\n", "J2 = 1.0\n", "Q = 1.0 - J2\n", "Lx = 3\n", "Ly = 3\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "lattice.draw()\n", "\n", "# %%\n", "# Hilbert空间定义\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# 定义(Si·Sj - 1/4)算符\n", "ProjOp = jnp.array(SiSj) - 0.25 * jnp.array(umum)\n", "ProjOp2 = jnp.kron(ProjOp, ProjOp)\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hilbert, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hilbert, dtype=jnp.complex128)\n", "\n", "# 获取晶格尺寸\n", "Lx, Ly = lattice.extent[0], lattice.extent[1]\n", "\n", "# 遍历所有单元格\n", "for x in range(Lx):\n", "    for y in range(Ly):\n", "        # 计算当前单元格的基本索引\n", "        base = 4 * (y + x * Ly)\n", "        \n", "        # 当前单元格内的四个格点\n", "        site0 = base      # 左下角 (0.5, 0.5)\n", "        site1 = base + 1  # 右下角 (1.5, 0.5)\n", "        site2 = base + 2  # 右上角 (1.5, 1.5)\n", "        site3 = base + 3  # 左上角 (0.5, 1.5)\n", "        \n", "        # 找到相邻单元格（考虑周期性边界条件）\n", "        right_x = (x + 1) % Lx\n", "        right_base = 4 * (y + right_x * Ly)\n", "        \n", "        left_x = (x - 1 + Lx) % Lx\n", "        left_base = 4 * (y + left_x * Ly)\n", "        \n", "        up_y = (y + 1) % Ly\n", "        up_base = 4 * (up_y + x * Ly)\n", "        \n", "        down_y = (y - 1 + Ly) % Ly\n", "        down_base = 4 * (down_y + x * Ly)\n", "        \n", "        # 1. 单元格内部的水平方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site1, site3, site2]])\n", "        \n", "        # 2. 单元格内部的垂直方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site3, site1, site2]])\n", "        \n", "        # 3. 与右侧单元格形成的水平plaquette（处理x方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site1, right_base, site2, right_base + 3]])\n", "        \n", "        # 4. 与上方单元格形成的垂直plaquette（处理y方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site3, up_base, site2, up_base + 1]])\n", "        \n", "        # # 5. 与左侧单元格形成的水平plaquette（处理x方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, left_base + 1, site3, left_base + 2]])\n", "        \n", "        # # 6. 与下方单元格形成的垂直plaquette（处理y方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, down_base + 3, site1, down_base + 2]])\n", "\n", "# 合并两部分哈密顿量\n", "hamiltonian = H_J + 2*H_Q\n", "hamiltonian = hamiltonian.to_jax_operator()\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# ----------------- 配置采样器 -----------------\n", "sampler = nk.sampler.MetropolisExchange(\n", "    hilbert=hilbert, \n", "    graph=lattice, \n", "    n_chains=2**12, \n", "    d_max=2)\n", "\n", "# ----------------- 定义局部集群和掩码 -----------------\n", "local_cluster = jnp.arange(Lx * Ly * 4).tolist()\n", "mask = jnp.zeros(lattice.n_nodes, dtype=bool)\n", "for i in local_cluster:\n", "    mask = mask.at[i].set(True)\n", "\n", "# ----------------- 定义晶格对称性 -----------------\n", "nc = 4\n", "cyclic_4 = PointGroup(\n", "    [Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],\n", "    ndim=2,\n", ")\n", "C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4\n", "symmetries = lattice.space_group(C4v)\n", "sgb = lattice.space_group_builder(point_group=C4v)\n", "\n", "momentum = [0.0, 0.0]\n", "chi = sgb.space_group_irreps(momentum)[0]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["参数数量: 7084\n"]}], "source": ["# 配置群等变卷积神经网络(GCNN)模型\n", "model = create_gcnn(\n", "    symmetries=symmetries,\n", "    layers=N_layers,\n", "    features=N_features,\n", "    mask=mask,\n", "    characters=chi\n", ")\n", "\n", "# 初始化变分量子态\n", "vqs = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model,\n", "    n_samples=2**12,\n", "    n_discard_per_chain=0,\n", "    chunk_size=2**10,\n", ")\n", "\n", "# 设置优化参数\n", "n_ann = 2000  # 温度退火步数\n", "n_train = 1   # 每个温度的训练步数\n", "lr = 0.05     # 学习率\n", "temperature = 1.0  # 初始温度\n", "n_params = nk.jax.tree_size(vqs.parameters)\n", "\n", "print(f\"参数数量: {n_params}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import netket.optimizer as nk_opt\n", "\n", "optimizer = nk_opt.Sgd(learning_rate=0.05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 80\u001b[0m\n\u001b[1;32m     77\u001b[0m temperature_original \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1.0\u001b[39m  \u001b[38;5;66;03m# 初始温度\u001b[39;00m\n\u001b[1;32m     78\u001b[0m reference_energy \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m16.2631\u001b[39m\n\u001b[0;32m---> 80\u001b[0m vmc \u001b[38;5;241m=\u001b[39m \u001b[43mCustomFreeEnergyVMC_SRt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     81\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreference_energy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mreference_energy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     82\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtemperature_original\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     83\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhamiltonian\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhamiltonian\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     84\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptimizer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptimizer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     85\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdiag_shift\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.01\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     86\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvariational_state\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvqs\u001b[49m\n\u001b[1;32m     87\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     89\u001b[0m start \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     90\u001b[0m vmc\u001b[38;5;241m.\u001b[39mrun(n_iter\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1000\u001b[39m)\n", "Cell \u001b[0;32mIn[8], line 49\u001b[0m, in \u001b[0;36mCustomFreeEnergyVMC_SRt.__init__\u001b[0;34m(self, reference_energy, *args, **kwargs)\u001b[0m\n\u001b[1;32m     48\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, reference_energy, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m---> 49\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     50\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreference_energy \u001b[38;5;241m=\u001b[39m reference_energy\n", "Cell \u001b[0;32mIn[8], line 22\u001b[0m, in \u001b[0;36mFreeEnergyVMC_SRt.__init__\u001b[0;34m(self, temperature, *args, **kwargs)\u001b[0m\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, temperature, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m---> 22\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     23\u001b[0m     \u001b[38;5;66;03m# 记录初始温度，用于后续温度递减计算\u001b[39;00m\n\u001b[1;32m     24\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minit_temperature \u001b[38;5;241m=\u001b[39m temperature\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/experimental/driver/vmc_srt.py:216\u001b[0m, in \u001b[0;36mVMC_SRt.__init__\u001b[0;34m(self, hamiltonian, optimizer, diag_shift, linear_solver_fn, jacobian_mode, variational_state)\u001b[0m\n\u001b[1;32m    213\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dp: PyTree \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    215\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdiag_shift \u001b[38;5;241m=\u001b[39m diag_shift\n\u001b[0;32m--> 216\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjacobian_mode\u001b[49m \u001b[38;5;241m=\u001b[39m jacobian_mode\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_linear_solver_fn \u001b[38;5;241m=\u001b[39m linear_solver_fn\n\u001b[1;32m    219\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_params_structure \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mtree_util\u001b[38;5;241m.\u001b[39mtree_map(\n\u001b[1;32m    220\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: jax\u001b[38;5;241m.\u001b[39mShapeDtypeStruct(x\u001b[38;5;241m.\u001b[39mshape, x\u001b[38;5;241m.\u001b[39mdtype), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mparameters\n\u001b[1;32m    221\u001b[0m )\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/experimental/driver/vmc_srt.py:251\u001b[0m, in \u001b[0;36mVMC_SRt.jacobian_mode\u001b[0;34m(self, mode)\u001b[0m\n\u001b[1;32m    244\u001b[0m \u001b[38;5;129m@jacobian_mode\u001b[39m\u001b[38;5;241m.\u001b[39msetter\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mjacobian_mode\u001b[39m(\u001b[38;5;28mself\u001b[39m, mode: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    246\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m mode \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    247\u001b[0m         mode \u001b[38;5;241m=\u001b[39m nkjax\u001b[38;5;241m.\u001b[39mjacobian_default_mode(\n\u001b[1;32m    248\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39m_apply_fun,\n\u001b[1;32m    249\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mparameters,\n\u001b[1;32m    250\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mmodel_state,\n\u001b[0;32m--> 251\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msamples\u001b[49m,\n\u001b[1;32m    252\u001b[0m             warn\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    253\u001b[0m         )\n\u001b[1;32m    255\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m mode \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcomplex\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreal\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m    256\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    257\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`jacobian_mode` only supports \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mreal\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m for real-valued wavefunctions and\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    258\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcomplex\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    259\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`holomorphic` is not yet supported, but could be contributed in the future.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    260\u001b[0m         )\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:570\u001b[0m, in \u001b[0;36mMCState.samples\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    559\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    560\u001b[0m \u001b[38;5;124;03mR<PERSON>urns the set of cached samples.\u001b[39;00m\n\u001b[1;32m    561\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    567\u001b[0m \u001b[38;5;124;03m:meth:`~MCState.reset` or :meth:`~MCState.sample`.\u001b[39;00m\n\u001b[1;32m    568\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    569\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m:\n\u001b[0;32m--> 570\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    571\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:549\u001b[0m, in \u001b[0;36mMCState.sample\u001b[0;34m(self, chain_length, n_samples, n_discard_per_chain)\u001b[0m\n\u001b[1;32m    546\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m timer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    547\u001b[0m             _\u001b[38;5;241m.\u001b[39mblock_until_ready()\n\u001b[0;32m--> 549\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msampler_state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    550\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sampler_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    551\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sampler_variables\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    552\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msampler_state\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    553\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchain_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchain_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    554\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    555\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_samples\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/base.py:292\u001b[0m, in \u001b[0;36mSampler.sample\u001b[0;34m(sampler, machine, parameters, state, chain_length)\u001b[0m\n\u001b[1;32m    289\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m state \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    290\u001b[0m     state \u001b[38;5;241m=\u001b[39m sampler\u001b[38;5;241m.\u001b[39mreset(machine, parameters)\n\u001b[0;32m--> 292\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sample_chain\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    293\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwrap_afun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmachine\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchain_length\u001b[49m\n\u001b[1;32m    294\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:340\u001b[0m, in \u001b[0;36m_cpp_pjit.<locals>.cache_miss\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    335\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mno_tracing\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    336\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mre-tracing function \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mjit_info\u001b[38;5;241m.\u001b[39mfun_sourceinfo\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m for \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    337\u001b[0m                      \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`jit`, but \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mno_tracing\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is set\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    339\u001b[0m (outs, out_flat, out_tree, args_flat, jaxpr, attrs_tracked, executable,\n\u001b[0;32m--> 340\u001b[0m  pgle_profiler) \u001b[38;5;241m=\u001b[39m \u001b[43m_python_pjit_helper\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjit_info\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    342\u001b[0m maybe_fastpath_data \u001b[38;5;241m=\u001b[39m _get_fastpath_data(\n\u001b[1;32m    343\u001b[0m     executable, out_tree, args_flat, out_flat, attrs_tracked, jaxpr\u001b[38;5;241m.\u001b[39meffects,\n\u001b[1;32m    344\u001b[0m     jaxpr\u001b[38;5;241m.\u001b[39mconsts, jit_info\u001b[38;5;241m.\u001b[39mabstracted_axes,\n\u001b[1;32m    345\u001b[0m     pgle_profiler)\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m outs, maybe_fastpath_data, _need_to_rebuild_with_fdo(pgle_profiler)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:180\u001b[0m, in \u001b[0;36m_python_pjit_helper\u001b[0;34m(fun, jit_info, *args, **kwargs)\u001b[0m\n\u001b[1;32m    179\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_python_pjit_helper\u001b[39m(fun, jit_info, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m--> 180\u001b[0m   p, args_flat \u001b[38;5;241m=\u001b[39m \u001b[43m_infer_params\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjit_info\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    182\u001b[0m   \u001b[38;5;28;01mfor\u001b[39;00m arg \u001b[38;5;129;01min\u001b[39;00m args_flat:\n\u001b[1;32m    183\u001b[0m     dispatch\u001b[38;5;241m.\u001b[39mcheck_arg(arg)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:740\u001b[0m, in \u001b[0;36m_infer_params\u001b[0;34m(fun, ji, args, kwargs)\u001b[0m\n\u001b[1;32m    738\u001b[0m entry \u001b[38;5;241m=\u001b[39m _infer_params_cached(fun, ji, signature, avals, pjit_mesh, resource_env)\n\u001b[1;32m    739\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m entry\u001b[38;5;241m.\u001b[39mpjit_params \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 740\u001b[0m   p, args_flat \u001b[38;5;241m=\u001b[39m \u001b[43m_infer_params_impl\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    741\u001b[0m \u001b[43m      \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mji\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpjit_mesh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresource_env\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mavals\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    742\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m p\u001b[38;5;241m.\u001b[39mattrs_tracked:  \u001b[38;5;66;03m# if attrs, don't popoulate the cache\u001b[39;00m\n\u001b[1;32m    743\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m p, p\u001b[38;5;241m.\u001b[39mconsts \u001b[38;5;241m+\u001b[39m args_flat\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:629\u001b[0m, in \u001b[0;36m_infer_params_impl\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    625\u001b[0m abstract_mesh \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    626\u001b[0m     get_abstract_mesh_from_avals(in_type)\n\u001b[1;32m    627\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mget_abstract_mesh() \u001b[38;5;28;01melse\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mget_abstract_mesh())\n\u001b[1;32m    628\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mset_abstract_mesh(abstract_mesh):\n\u001b[0;32m--> 629\u001b[0m   jaxpr, consts, out_avals, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_create_pjit_jaxpr\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    630\u001b[0m \u001b[43m      \u001b[49m\u001b[43mflat_fun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mattr_token\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdbg\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    631\u001b[0m \u001b[43m      \u001b[49m\u001b[43mHashableFunction\u001b[49m\u001b[43m(\u001b[49m\u001b[43mres_paths\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclosure\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    632\u001b[0m \u001b[43m      \u001b[49m\u001b[43mIgnoreKey\u001b[49m\u001b[43m(\u001b[49m\u001b[43mji\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minline\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    633\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mmutable_array_checks\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    634\u001b[0m     _check_no_aliased_closed_over_refs(dbg, (\u001b[38;5;241m*\u001b[39mjaxpr\u001b[38;5;241m.\u001b[39mconsts, \u001b[38;5;241m*\u001b[39mconsts), explicit_args)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:349\u001b[0m, in \u001b[0;36mcache.<locals>.memoized_fun\u001b[0;34m(fun, *args)\u001b[0m\n\u001b[1;32m    347\u001b[0m   fun\u001b[38;5;241m.\u001b[39mpopulate_stores(stores)\n\u001b[1;32m    348\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 349\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    350\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m explain \u001b[38;5;129;01mand\u001b[39;00m config\u001b[38;5;241m.\u001b[39mexplain_cache_misses\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    351\u001b[0m     explain(fun\u001b[38;5;241m.\u001b[39mf, cache \u001b[38;5;129;01mis\u001b[39;00m new_cache, cache, key)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:1310\u001b[0m, in \u001b[0;36m_create_pjit_jaxpr\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m   1308\u001b[0m     attrs_tracked \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m   1309\u001b[0m   \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1310\u001b[0m     jaxpr, global_out_avals, consts, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43mpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrace_to_jaxpr_dynamic\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1311\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdebug_info\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpe_debug\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1312\u001b[0m     \u001b[38;5;66;03m# assert attr_data is sentinel or attr_data matches attrs_tracked\u001b[39;00m\n\u001b[1;32m   1313\u001b[0m \n\u001b[1;32m   1314\u001b[0m \u001b[38;5;66;03m# TODO(dougalm,mattjj): enable debug info with attrs_tracked\u001b[39;00m\n\u001b[1;32m   1315\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m config\u001b[38;5;241m.\u001b[39mdynamic_shapes\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m attrs_tracked:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/profiler.py:333\u001b[0m, in \u001b[0;36mannotate_function.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(func)\n\u001b[1;32m    331\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    332\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m TraceAnnotation(name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdecorator_kwargs):\n\u001b[0;32m--> 333\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    334\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m wrapper\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:2159\u001b[0m, in \u001b[0;36mtrace_to_jaxpr_dynamic\u001b[0;34m(fun, in_avals, debug_info, keep_inputs)\u001b[0m\n\u001b[1;32m   2157\u001b[0m in_tracers \u001b[38;5;241m=\u001b[39m [t \u001b[38;5;28;01mfor\u001b[39;00m t, keep \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in_tracers, keep_inputs) \u001b[38;5;28;01mif\u001b[39;00m keep]\n\u001b[1;32m   2158\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m core\u001b[38;5;241m.\u001b[39mset_current_trace(trace):\n\u001b[0;32m-> 2159\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mfun\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall_wrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43min_tracers\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2161\u001b[0m out_tracers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmap\u001b[39m(trace\u001b[38;5;241m.\u001b[39mto_jaxpr_tracer, ans)\n\u001b[1;32m   2162\u001b[0m _check_no_returned_refs(debug_info, out_tracers)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:192\u001b[0m, in \u001b[0;36mWrappedFun.call_wrapped\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcall_wrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    191\u001b[0m \u001b[38;5;250m  \u001b[39m\u001b[38;5;124;03m\"\"\"Calls the transformed function\"\"\"\u001b[39;00m\n\u001b[0;32m--> 192\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mf_transformed\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:292\u001b[0m, in \u001b[0;36m_argnums_partial\u001b[0;34m(_fun, _dyn_argnums, _fixed_args, *dyn_args, **kwargs)\u001b[0m\n\u001b[1;32m    290\u001b[0m args \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mnext\u001b[39m(fixed_args_)\u001b[38;5;241m.\u001b[39mval \u001b[38;5;28;01mif\u001b[39;00m x \u001b[38;5;129;01mis\u001b[39;00m sentinel \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m args]\n\u001b[1;32m    291\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28mnext\u001b[39m(fixed_args_, sentinel) \u001b[38;5;129;01mis\u001b[39;00m sentinel\n\u001b[0;32m--> 292\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:72\u001b[0m, in \u001b[0;36mflatten_fun\u001b[0;34m(f, store, in_tree, *args_flat)\u001b[0m\n\u001b[1;32m     69\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m     70\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mflatten_fun\u001b[39m(f, store, in_tree, \u001b[38;5;241m*\u001b[39margs_flat):\n\u001b[1;32m     71\u001b[0m   py_args, py_kwargs \u001b[38;5;241m=\u001b[39m tree_unflatten(in_tree, args_flat)\n\u001b[0;32m---> 72\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     73\u001b[0m   ans, out_tree \u001b[38;5;241m=\u001b[39m tree_flatten(ans)\n\u001b[1;32m     74\u001b[0m   store\u001b[38;5;241m.\u001b[39mstore(out_tree)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:318\u001b[0m, in \u001b[0;36m_argnames_partial\u001b[0;34m(_fun, _fixed_kwargs, *args, **dyn_kwargs)\u001b[0m\n\u001b[1;32m    315\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation2\n\u001b[1;32m    316\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_argnames_partial\u001b[39m(_fun, _fixed_kwargs: WrapKwArgs, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdyn_kwargs):\n\u001b[1;32m    317\u001b[0m   kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m({k: v\u001b[38;5;241m.\u001b[39mval \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m _fixed_kwargs\u001b[38;5;241m.\u001b[39mval\u001b[38;5;241m.\u001b[39mitems()}, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdyn_kwargs)\n\u001b[0;32m--> 318\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:292\u001b[0m, in \u001b[0;36m_argnums_partial\u001b[0;34m(_fun, _dyn_argnums, _fixed_args, *dyn_args, **kwargs)\u001b[0m\n\u001b[1;32m    290\u001b[0m args \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mnext\u001b[39m(fixed_args_)\u001b[38;5;241m.\u001b[39mval \u001b[38;5;28;01mif\u001b[39;00m x \u001b[38;5;129;01mis\u001b[39;00m sentinel \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m args]\n\u001b[1;32m    291\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28mnext\u001b[39m(fixed_args_, sentinel) \u001b[38;5;129;01mis\u001b[39;00m sentinel\n\u001b[0;32m--> 292\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:652\u001b[0m, in \u001b[0;36mresult_paths\u001b[0;34m(_fun, _store, *args, **kwargs)\u001b[0m\n\u001b[1;32m    649\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m    650\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mresult_paths\u001b[39m(_fun, _store, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    651\u001b[0m   \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlinear_util transform to get output pytree paths of pre-flattened function.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 652\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    653\u001b[0m   _store\u001b[38;5;241m.\u001b[39mstore([keystr(path) \u001b[38;5;28;01mfor\u001b[39;00m path, _ \u001b[38;5;129;01min\u001b[39;00m generate_key_paths(ans)])\n\u001b[1;32m    654\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m ans\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/metropolis.py:508\u001b[0m, in \u001b[0;36mMetropolisSampler._sample_chain\u001b[0;34m(sampler, machine, parameters, state, chain_length)\u001b[0m\n\u001b[1;32m    490\u001b[0m \u001b[38;5;129m@partial\u001b[39m(jax\u001b[38;5;241m.\u001b[39mjit, static_argnums\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m4\u001b[39m))\n\u001b[1;32m    491\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_sample_chain\u001b[39m(sampler, machine, parameters, state, chain_length):\n\u001b[1;32m    492\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    493\u001b[0m \u001b[38;5;124;03m    Samples `chain_length` batches of samples along the chains.\u001b[39;00m\n\u001b[1;32m    494\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    506\u001b[0m \u001b[38;5;124;03m        state: The new state of the sampler\u001b[39;00m\n\u001b[1;32m    507\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 508\u001b[0m     state, samples \u001b[38;5;241m=\u001b[39m \u001b[43mjax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscan\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    509\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample_next\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmachine\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    510\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    511\u001b[0m \u001b[43m        \u001b[49m\u001b[43mxs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    512\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlength\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchain_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    513\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    514\u001b[0m     \u001b[38;5;66;03m# make it (n_chains, n_samples_per_chain) as expected by netket.stats.statistics\u001b[39;00m\n\u001b[1;32m    515\u001b[0m     samples \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mswapaxes(samples, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m)\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:304\u001b[0m, in \u001b[0;36mscan\u001b[0;34m(f, init, xs, length, reverse, unroll, _split_transpose)\u001b[0m\n\u001b[1;32m    296\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m (init_flat, carry_avals, carry_avals_out, init_tree, in_flat, jaxpr,\n\u001b[1;32m    297\u001b[0m           consts, out_tree, out_tree_children, attrs_tracked)\n\u001b[1;32m    299\u001b[0m \u001b[38;5;66;03m# The carry input and output avals must match exactly. However, we want to account for\u001b[39;00m\n\u001b[1;32m    300\u001b[0m \u001b[38;5;66;03m# the case when init contains weakly-typed values (e.g. Python scalars), with avals that\u001b[39;00m\n\u001b[1;32m    301\u001b[0m \u001b[38;5;66;03m# may not match the output despite being compatible by virtue of their weak type.\u001b[39;00m\n\u001b[1;32m    302\u001b[0m \u001b[38;5;66;03m# To do this, we compute the jaxpr in two passes: first with the raw inputs, and if\u001b[39;00m\n\u001b[1;32m    303\u001b[0m \u001b[38;5;66;03m# necessary, a second time with modified init values.\u001b[39;00m\n\u001b[0;32m--> 304\u001b[0m init_flat, carry_avals, carry_avals_out, init_tree, \u001b[38;5;241m*\u001b[39mrest \u001b[38;5;241m=\u001b[39m \u001b[43m_create_jaxpr\u001b[49m\u001b[43m(\u001b[49m\u001b[43minit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    305\u001b[0m new_init_flat, changed \u001b[38;5;241m=\u001b[39m _promote_weak_typed_inputs(init_flat, carry_avals, carry_avals_out)\n\u001b[1;32m    306\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m changed:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:286\u001b[0m, in \u001b[0;36mscan.<locals>._create_jaxpr\u001b[0;34m(init)\u001b[0m\n\u001b[1;32m    284\u001b[0m in_flat, in_tree \u001b[38;5;241m=\u001b[39m tree_flatten((init, xs))\n\u001b[1;32m    285\u001b[0m carry_avals \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m(_map(core\u001b[38;5;241m.\u001b[39mget_aval, init_flat))\n\u001b[0;32m--> 286\u001b[0m jaxpr, consts, out_tree, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_initial_style_jaxpr_attrs\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    287\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_tree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mcarry_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mx_avals\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mscan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    288\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mmutable_array_checks\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    289\u001b[0m   _check_no_aliased_closed_over_refs(dbg, (\u001b[38;5;241m*\u001b[39mjaxpr\u001b[38;5;241m.\u001b[39mconsts, \u001b[38;5;241m*\u001b[39mconsts), in_flat)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/common.py:72\u001b[0m, in \u001b[0;36m_initial_style_jaxpr_attrs\u001b[0;34m(fun, in_tree, in_avals, primitive_name)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_initial_style_jaxpr_attrs\u001b[39m(fun: Callable, in_tree, in_avals,\n\u001b[1;32m     71\u001b[0m                                primitive_name: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m---> 72\u001b[0m   jaxpr, consts, out_tree, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_initial_style_open_jaxpr\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     73\u001b[0m \u001b[43m      \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_tree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprimitive_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     74\u001b[0m   closed_jaxpr \u001b[38;5;241m=\u001b[39m pe\u001b[38;5;241m.\u001b[39mclose_jaxpr(pe\u001b[38;5;241m.\u001b[39mconvert_constvars_jaxpr(jaxpr))\n\u001b[1;32m     75\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m closed_jaxpr, consts, out_tree, attrs_tracked\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/common.py:58\u001b[0m, in \u001b[0;36m_initial_style_open_jaxpr\u001b[0;34m(fun, in_tree, in_avals, primitive_name)\u001b[0m\n\u001b[1;32m     55\u001b[0m wrapped_fun, out_tree \u001b[38;5;241m=\u001b[39m flatten_fun_nokwargs(lu\u001b[38;5;241m.\u001b[39mwrap_init(fun), in_tree)\n\u001b[1;32m     56\u001b[0m debug \u001b[38;5;241m=\u001b[39m pe\u001b[38;5;241m.\u001b[39mtracing_debug_info(fun, in_tree, out_tree, \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m     57\u001b[0m                               primitive_name \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<unknown>\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 58\u001b[0m jaxpr, _, consts, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43mpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrace_to_jaxpr_dynamic\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     59\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwrapped_fun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     60\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m jaxpr, consts, out_tree(), attrs_tracked\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/profiler.py:333\u001b[0m, in \u001b[0;36mannotate_function.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(func)\n\u001b[1;32m    331\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    332\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m TraceAnnotation(name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdecorator_kwargs):\n\u001b[0;32m--> 333\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    334\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m wrapper\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:2159\u001b[0m, in \u001b[0;36mtrace_to_jaxpr_dynamic\u001b[0;34m(fun, in_avals, debug_info, keep_inputs)\u001b[0m\n\u001b[1;32m   2157\u001b[0m in_tracers \u001b[38;5;241m=\u001b[39m [t \u001b[38;5;28;01mfor\u001b[39;00m t, keep \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in_tracers, keep_inputs) \u001b[38;5;28;01mif\u001b[39;00m keep]\n\u001b[1;32m   2158\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m core\u001b[38;5;241m.\u001b[39mset_current_trace(trace):\n\u001b[0;32m-> 2159\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mfun\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall_wrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43min_tracers\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2161\u001b[0m out_tracers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmap\u001b[39m(trace\u001b[38;5;241m.\u001b[39mto_jaxpr_tracer, ans)\n\u001b[1;32m   2162\u001b[0m _check_no_returned_refs(debug_info, out_tracers)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:192\u001b[0m, in \u001b[0;36mWrappedFun.call_wrapped\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcall_wrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    191\u001b[0m \u001b[38;5;250m  \u001b[39m\u001b[38;5;124;03m\"\"\"Calls the transformed function\"\"\"\u001b[39;00m\n\u001b[0;32m--> 192\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mf_transformed\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:88\u001b[0m, in \u001b[0;36mflatten_fun_nokwargs\u001b[0;34m(f, store, in_tree, *args_flat)\u001b[0m\n\u001b[1;32m     85\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mflatten_fun_nokwargs\u001b[39m(f, store, in_tree, \u001b[38;5;241m*\u001b[39margs_flat):\n\u001b[1;32m     87\u001b[0m   py_args \u001b[38;5;241m=\u001b[39m tree_unflatten(in_tree, args_flat)\n\u001b[0;32m---> 88\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_args\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     89\u001b[0m   ans, out_tree \u001b[38;5;241m=\u001b[39m tree_flatten(ans)\n\u001b[1;32m     90\u001b[0m   store\u001b[38;5;241m.\u001b[39mstore(out_tree)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/metropolis.py:509\u001b[0m, in \u001b[0;36mMetropolisSampler._sample_chain.<locals>.<lambda>\u001b[0;34m(state, _)\u001b[0m\n\u001b[1;32m    490\u001b[0m \u001b[38;5;129m@partial\u001b[39m(jax\u001b[38;5;241m.\u001b[39mjit, static_argnums\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m4\u001b[39m))\n\u001b[1;32m    491\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_sample_chain\u001b[39m(sampler, machine, parameters, state, chain_length):\n\u001b[1;32m    492\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    493\u001b[0m \u001b[38;5;124;03m    Samples `chain_length` batches of samples along the chains.\u001b[39;00m\n\u001b[1;32m    494\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    506\u001b[0m \u001b[38;5;124;03m        state: The new state of the sampler\u001b[39;00m\n\u001b[1;32m    507\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m    508\u001b[0m     state, samples \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mlax\u001b[38;5;241m.\u001b[39mscan(\n\u001b[0;32m--> 509\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m state, _: \u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msample_next\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmachine\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[1;32m    510\u001b[0m         state,\n\u001b[1;32m    511\u001b[0m         xs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    512\u001b[0m         length\u001b[38;5;241m=\u001b[39mchain_length,\n\u001b[1;32m    513\u001b[0m     )\n\u001b[1;32m    514\u001b[0m     \u001b[38;5;66;03m# make it (n_chains, n_samples_per_chain) as expected by netket.stats.statistics\u001b[39;00m\n\u001b[1;32m    515\u001b[0m     samples \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mswapaxes(samples, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/metropolis.py:357\u001b[0m, in \u001b[0;36mMetropolisSampler.sample_next\u001b[0;34m(sampler, machine, parameters, state)\u001b[0m\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m state \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    355\u001b[0m     state \u001b[38;5;241m=\u001b[39m sampler\u001b[38;5;241m.\u001b[39mreset(machine, parameters)\n\u001b[0;32m--> 357\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sample_next\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwrap_afun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmachine\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/metropolis.py:478\u001b[0m, in \u001b[0;36mMetropolisSampler._sample_next\u001b[0;34m(sampler, machine, parameters, state)\u001b[0m\n\u001b[1;32m    467\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m s\n\u001b[1;32m    469\u001b[0m s \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    470\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mkey\u001b[39m\u001b[38;5;124m\"\u001b[39m: state\u001b[38;5;241m.\u001b[39mrng,\n\u001b[1;32m    471\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mσ\u001b[39m\u001b[38;5;124m\"\u001b[39m: state\u001b[38;5;241m.\u001b[39mσ,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    476\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maccepted\u001b[39m\u001b[38;5;124m\"\u001b[39m: state\u001b[38;5;241m.\u001b[39mn_accepted_proc,\n\u001b[1;32m    477\u001b[0m }\n\u001b[0;32m--> 478\u001b[0m s \u001b[38;5;241m=\u001b[39m \u001b[43mjax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfori_loop\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msampler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msweep_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mloop_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    480\u001b[0m new_state \u001b[38;5;241m=\u001b[39m state\u001b[38;5;241m.\u001b[39mreplace(\n\u001b[1;32m    481\u001b[0m     rng\u001b[38;5;241m=\u001b[39ms[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mkey\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m    482\u001b[0m     σ\u001b[38;5;241m=\u001b[39ms[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mσ\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    485\u001b[0m     n_steps_proc\u001b[38;5;241m=\u001b[39mstate\u001b[38;5;241m.\u001b[39mn_steps_proc \u001b[38;5;241m+\u001b[39m sampler\u001b[38;5;241m.\u001b[39msweep_size \u001b[38;5;241m*\u001b[39m sampler\u001b[38;5;241m.\u001b[39mn_batches,\n\u001b[1;32m    486\u001b[0m )\n\u001b[1;32m    488\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m new_state, new_state\u001b[38;5;241m.\u001b[39mσ\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:2092\u001b[0m, in \u001b[0;36mfori_loop\u001b[0;34m(lower, upper, body_fun, init_val, unroll)\u001b[0m\n\u001b[1;32m   2090\u001b[0m   scan_body \u001b[38;5;241m=\u001b[39m _fori_scan_body_fun(body_fun)\n\u001b[1;32m   2091\u001b[0m   api_util\u001b[38;5;241m.\u001b[39msave_wrapped_fun_sourceinfo(scan_body, body_fun)\n\u001b[0;32m-> 2092\u001b[0m   (_, result), _ \u001b[38;5;241m=\u001b[39m \u001b[43mscan\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2093\u001b[0m \u001b[43m      \u001b[49m\u001b[43mscan_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2094\u001b[0m \u001b[43m      \u001b[49m\u001b[43m(\u001b[49m\u001b[43mlower_\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minit_val\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2095\u001b[0m \u001b[43m      \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   2096\u001b[0m \u001b[43m      \u001b[49m\u001b[43mlength\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlength\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2097\u001b[0m \u001b[43m      \u001b[49m\u001b[43munroll\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43munroll\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2098\u001b[0m \u001b[43m  \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2099\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m result\n\u001b[1;32m   2100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m unroll \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:304\u001b[0m, in \u001b[0;36mscan\u001b[0;34m(f, init, xs, length, reverse, unroll, _split_transpose)\u001b[0m\n\u001b[1;32m    296\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m (init_flat, carry_avals, carry_avals_out, init_tree, in_flat, jaxpr,\n\u001b[1;32m    297\u001b[0m           consts, out_tree, out_tree_children, attrs_tracked)\n\u001b[1;32m    299\u001b[0m \u001b[38;5;66;03m# The carry input and output avals must match exactly. However, we want to account for\u001b[39;00m\n\u001b[1;32m    300\u001b[0m \u001b[38;5;66;03m# the case when init contains weakly-typed values (e.g. Python scalars), with avals that\u001b[39;00m\n\u001b[1;32m    301\u001b[0m \u001b[38;5;66;03m# may not match the output despite being compatible by virtue of their weak type.\u001b[39;00m\n\u001b[1;32m    302\u001b[0m \u001b[38;5;66;03m# To do this, we compute the jaxpr in two passes: first with the raw inputs, and if\u001b[39;00m\n\u001b[1;32m    303\u001b[0m \u001b[38;5;66;03m# necessary, a second time with modified init values.\u001b[39;00m\n\u001b[0;32m--> 304\u001b[0m init_flat, carry_avals, carry_avals_out, init_tree, \u001b[38;5;241m*\u001b[39mrest \u001b[38;5;241m=\u001b[39m \u001b[43m_create_jaxpr\u001b[49m\u001b[43m(\u001b[49m\u001b[43minit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    305\u001b[0m new_init_flat, changed \u001b[38;5;241m=\u001b[39m _promote_weak_typed_inputs(init_flat, carry_avals, carry_avals_out)\n\u001b[1;32m    306\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m changed:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:286\u001b[0m, in \u001b[0;36mscan.<locals>._create_jaxpr\u001b[0;34m(init)\u001b[0m\n\u001b[1;32m    284\u001b[0m in_flat, in_tree \u001b[38;5;241m=\u001b[39m tree_flatten((init, xs))\n\u001b[1;32m    285\u001b[0m carry_avals \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m(_map(core\u001b[38;5;241m.\u001b[39mget_aval, init_flat))\n\u001b[0;32m--> 286\u001b[0m jaxpr, consts, out_tree, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_initial_style_jaxpr_attrs\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    287\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_tree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mcarry_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mx_avals\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mscan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    288\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mmutable_array_checks\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    289\u001b[0m   _check_no_aliased_closed_over_refs(dbg, (\u001b[38;5;241m*\u001b[39mjaxpr\u001b[38;5;241m.\u001b[39mconsts, \u001b[38;5;241m*\u001b[39mconsts), in_flat)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/common.py:72\u001b[0m, in \u001b[0;36m_initial_style_jaxpr_attrs\u001b[0;34m(fun, in_tree, in_avals, primitive_name)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_initial_style_jaxpr_attrs\u001b[39m(fun: Callable, in_tree, in_avals,\n\u001b[1;32m     71\u001b[0m                                primitive_name: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m---> 72\u001b[0m   jaxpr, consts, out_tree, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_initial_style_open_jaxpr\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     73\u001b[0m \u001b[43m      \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_tree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprimitive_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     74\u001b[0m   closed_jaxpr \u001b[38;5;241m=\u001b[39m pe\u001b[38;5;241m.\u001b[39mclose_jaxpr(pe\u001b[38;5;241m.\u001b[39mconvert_constvars_jaxpr(jaxpr))\n\u001b[1;32m     75\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m closed_jaxpr, consts, out_tree, attrs_tracked\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/common.py:58\u001b[0m, in \u001b[0;36m_initial_style_open_jaxpr\u001b[0;34m(fun, in_tree, in_avals, primitive_name)\u001b[0m\n\u001b[1;32m     55\u001b[0m wrapped_fun, out_tree \u001b[38;5;241m=\u001b[39m flatten_fun_nokwargs(lu\u001b[38;5;241m.\u001b[39mwrap_init(fun), in_tree)\n\u001b[1;32m     56\u001b[0m debug \u001b[38;5;241m=\u001b[39m pe\u001b[38;5;241m.\u001b[39mtracing_debug_info(fun, in_tree, out_tree, \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m     57\u001b[0m                               primitive_name \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<unknown>\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 58\u001b[0m jaxpr, _, consts, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43mpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrace_to_jaxpr_dynamic\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     59\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwrapped_fun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     60\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m jaxpr, consts, out_tree(), attrs_tracked\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/profiler.py:333\u001b[0m, in \u001b[0;36mannotate_function.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(func)\n\u001b[1;32m    331\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    332\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m TraceAnnotation(name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdecorator_kwargs):\n\u001b[0;32m--> 333\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    334\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m wrapper\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:2159\u001b[0m, in \u001b[0;36mtrace_to_jaxpr_dynamic\u001b[0;34m(fun, in_avals, debug_info, keep_inputs)\u001b[0m\n\u001b[1;32m   2157\u001b[0m in_tracers \u001b[38;5;241m=\u001b[39m [t \u001b[38;5;28;01mfor\u001b[39;00m t, keep \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in_tracers, keep_inputs) \u001b[38;5;28;01mif\u001b[39;00m keep]\n\u001b[1;32m   2158\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m core\u001b[38;5;241m.\u001b[39mset_current_trace(trace):\n\u001b[0;32m-> 2159\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mfun\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall_wrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43min_tracers\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2161\u001b[0m out_tracers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmap\u001b[39m(trace\u001b[38;5;241m.\u001b[39mto_jaxpr_tracer, ans)\n\u001b[1;32m   2162\u001b[0m _check_no_returned_refs(debug_info, out_tracers)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:192\u001b[0m, in \u001b[0;36mWrappedFun.call_wrapped\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcall_wrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    191\u001b[0m \u001b[38;5;250m  \u001b[39m\u001b[38;5;124;03m\"\"\"Calls the transformed function\"\"\"\u001b[39;00m\n\u001b[0;32m--> 192\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mf_transformed\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:88\u001b[0m, in \u001b[0;36mflatten_fun_nokwargs\u001b[0;34m(f, store, in_tree, *args_flat)\u001b[0m\n\u001b[1;32m     85\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mflatten_fun_nokwargs\u001b[39m(f, store, in_tree, \u001b[38;5;241m*\u001b[39margs_flat):\n\u001b[1;32m     87\u001b[0m   py_args \u001b[38;5;241m=\u001b[39m tree_unflatten(in_tree, args_flat)\n\u001b[0;32m---> 88\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_args\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     89\u001b[0m   ans, out_tree \u001b[38;5;241m=\u001b[39m tree_flatten(ans)\n\u001b[1;32m     90\u001b[0m   store\u001b[38;5;241m.\u001b[39mstore(out_tree)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/lax/control_flow/loops.py:1981\u001b[0m, in \u001b[0;36m_fori_scan_body_fun.<locals>.scanned_fun\u001b[0;34m(loop_carry, _)\u001b[0m\n\u001b[1;32m   1979\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mscanned_fun\u001b[39m(loop_carry, _):\n\u001b[1;32m   1980\u001b[0m   i, x \u001b[38;5;241m=\u001b[39m loop_carry\n\u001b[0;32m-> 1981\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m (i \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m, \u001b[43mbody_fun_ref\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m), \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/sampler/metropolis.py:448\u001b[0m, in \u001b[0;36mMetropolisSampler._sample_next.<locals>.loop_body\u001b[0;34m(i, s)\u001b[0m\n\u001b[1;32m    439\u001b[0m σp, log_prob_correction \u001b[38;5;241m=\u001b[39m sampler\u001b[38;5;241m.\u001b[39mrule\u001b[38;5;241m.\u001b[39mtransition(\n\u001b[1;32m    440\u001b[0m     sampler, machine, parameters, state, key1, s[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mσ\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    441\u001b[0m )\n\u001b[1;32m    442\u001b[0m _assert_good_sample_shape(\n\u001b[1;32m    443\u001b[0m     σp,\n\u001b[1;32m    444\u001b[0m     (sampler\u001b[38;5;241m.\u001b[39mn_batches, sampler\u001b[38;5;241m.\u001b[39mhilbert\u001b[38;5;241m.\u001b[39msize),\n\u001b[1;32m    445\u001b[0m     sampler\u001b[38;5;241m.\u001b[39mdtype,\n\u001b[1;32m    446\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msampler\u001b[38;5;241m.\u001b[39mrule\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.transition\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    447\u001b[0m )\n\u001b[0;32m--> 448\u001b[0m proposal_log_prob \u001b[38;5;241m=\u001b[39m sampler\u001b[38;5;241m.\u001b[39mmachine_pow \u001b[38;5;241m*\u001b[39m \u001b[43mapply_machine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mσp\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mreal\n\u001b[1;32m    449\u001b[0m _assert_good_log_prob_shape(proposal_log_prob, sampler\u001b[38;5;241m.\u001b[39mn_batches, machine)\n\u001b[1;32m    451\u001b[0m uniform \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39muniform(key2, shape\u001b[38;5;241m=\u001b[39m(sampler\u001b[38;5;241m.\u001b[39mn_batches,))\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:2235\u001b[0m, in \u001b[0;36mModule.apply\u001b[0;34m(self, variables, rngs, method, mutable, capture_intermediates, *args, **kwargs)\u001b[0m\n\u001b[1;32m   2233\u001b[0m   method \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__call__\u001b[39m\n\u001b[1;32m   2234\u001b[0m method \u001b[38;5;241m=\u001b[39m _get_unbound_fn(method)\n\u001b[0;32m-> 2235\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2236\u001b[0m \u001b[43m  \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2237\u001b[0m \u001b[43m  \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2238\u001b[0m \u001b[43m  \u001b[49m\u001b[43mmutable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmutable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2239\u001b[0m \u001b[43m  \u001b[49m\u001b[43mcapture_intermediates\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcapture_intermediates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2240\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrngs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrngs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/core/scope.py:1079\u001b[0m, in \u001b[0;36mapply.<locals>.wrapper\u001b[0;34m(variables, rngs, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1074\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m errors\u001b[38;5;241m.\u001b[39mApplyScopeInvalidVariablesStructureError(variables)\n\u001b[1;32m   1076\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m bind(\n\u001b[1;32m   1077\u001b[0m   variables, rngs\u001b[38;5;241m=\u001b[39mrngs, mutable\u001b[38;5;241m=\u001b[39mmutable, flags\u001b[38;5;241m=\u001b[39mflags\n\u001b[1;32m   1078\u001b[0m )\u001b[38;5;241m.\u001b[39mtemporary() \u001b[38;5;28;01mas\u001b[39;00m root:\n\u001b[0;32m-> 1079\u001b[0m   y \u001b[38;5;241m=\u001b[39m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mroot\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1080\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mutable \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m:\n\u001b[1;32m   1081\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m y, root\u001b[38;5;241m.\u001b[39mmutable_variables()\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:3017\u001b[0m, in \u001b[0;36mapply.<locals>.scope_fn\u001b[0;34m(scope, *args, **kwargs)\u001b[0m\n\u001b[1;32m   3015\u001b[0m _context\u001b[38;5;241m.\u001b[39mcapture_stack\u001b[38;5;241m.\u001b[39mappend(capture_intermediates)\n\u001b[1;32m   3016\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3017\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodule\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclone\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscope\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_deep_clone\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3018\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m   3019\u001b[0m   _context\u001b[38;5;241m.\u001b[39mcapture_stack\u001b[38;5;241m.\u001b[39mpop()\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:699\u001b[0m, in \u001b[0;36mwrap_method_once.<locals>.wrapped_module_method\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    697\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(args[\u001b[38;5;241m0\u001b[39m], <PERSON><PERSON><PERSON>):\n\u001b[1;32m    698\u001b[0m   \u001b[38;5;28mself\u001b[39m, args \u001b[38;5;241m=\u001b[39m args[\u001b[38;5;241m0\u001b[39m], args[\u001b[38;5;241m1\u001b[39m:]\n\u001b[0;32m--> 699\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_wrapped_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    700\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    701\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m fun(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:1216\u001b[0m, in \u001b[0;36mModule._call_wrapped_method\u001b[0;34m(self, fun, args, kwargs)\u001b[0m\n\u001b[1;32m   1214\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _use_named_call:\n\u001b[1;32m   1215\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m jax\u001b[38;5;241m.\u001b[39mnamed_scope(_derive_profiling_name(\u001b[38;5;28mself\u001b[39m, fun)):\n\u001b[0;32m-> 1216\u001b[0m     y \u001b[38;5;241m=\u001b[39m \u001b[43mrun_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1217\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1218\u001b[0m   y \u001b[38;5;241m=\u001b[39m run_fun(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "Cell \u001b[0;32mIn[3], line 84\u001b[0m, in \u001b[0;36mGCNN_Parity_Irrep.__call__\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     79\u001b[0m x_flip \u001b[38;5;241m=\u001b[39m reim_selu(x_flip)\n\u001b[1;32m     81\u001b[0m \u001b[38;5;66;03m# 混合原始和翻转路径\u001b[39;00m\n\u001b[1;32m     82\u001b[0m x_new \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m     83\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mequivariant_layers[layer](x)\n\u001b[0;32m---> 84\u001b[0m     \u001b[38;5;241m+\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mequivariant_layers_flip\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m]\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx_flip\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     85\u001b[0m ) \u001b[38;5;241m/\u001b[39m np\u001b[38;5;241m.\u001b[39msqrt(\u001b[38;5;241m2\u001b[39m)\n\u001b[1;32m     86\u001b[0m x_flip \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m     87\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mequivariant_layers[layer](x_flip)\n\u001b[1;32m     88\u001b[0m     \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mequivariant_layers_flip[layer](x)\n\u001b[1;32m     89\u001b[0m ) \u001b[38;5;241m/\u001b[39m np\u001b[38;5;241m.\u001b[39msqrt(\u001b[38;5;241m2\u001b[39m)\n\u001b[1;32m     90\u001b[0m x \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39marray(x_new, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:699\u001b[0m, in \u001b[0;36mwrap_method_once.<locals>.wrapped_module_method\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    697\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(args[\u001b[38;5;241m0\u001b[39m], <PERSON><PERSON><PERSON>):\n\u001b[1;32m    698\u001b[0m   \u001b[38;5;28mself\u001b[39m, args \u001b[38;5;241m=\u001b[39m args[\u001b[38;5;241m0\u001b[39m], args[\u001b[38;5;241m1\u001b[39m:]\n\u001b[0;32m--> 699\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_wrapped_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    700\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    701\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m fun(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:1216\u001b[0m, in \u001b[0;36mModule._call_wrapped_method\u001b[0;34m(self, fun, args, kwargs)\u001b[0m\n\u001b[1;32m   1214\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _use_named_call:\n\u001b[1;32m   1215\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m jax\u001b[38;5;241m.\u001b[39mnamed_scope(_derive_profiling_name(\u001b[38;5;28mself\u001b[39m, fun)):\n\u001b[0;32m-> 1216\u001b[0m     y \u001b[38;5;241m=\u001b[39m \u001b[43mrun_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1217\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1218\u001b[0m   y \u001b[38;5;241m=\u001b[39m run_fun(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/nn/symmetric_linear.py:538\u001b[0m, in \u001b[0;36mDenseEquivariantIrrep.__call__\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m    536\u001b[0m     kernel \u001b[38;5;241m=\u001b[39m kernel\u001b[38;5;241m.\u001b[39mat[:, :, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkernel_indices]\u001b[38;5;241m.\u001b[39mset(kernel_params)\n\u001b[1;32m    537\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 538\u001b[0m     kernel \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparam\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    539\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mkernel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    540\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkernel_init\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    541\u001b[0m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfeatures\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_features\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mn_symm\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    542\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparam_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    543\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    545\u001b[0m x, kernel, bias \u001b[38;5;241m=\u001b[39m promote_dtype(x, kernel, bias, dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    546\u001b[0m dtype \u001b[38;5;241m=\u001b[39m x\u001b[38;5;241m.\u001b[39mdtype\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/linen/module.py:1872\u001b[0m, in \u001b[0;36mModule.param\u001b[0;34m(self, name, init_fn, unbox, *init_args, **init_kwargs)\u001b[0m\n\u001b[1;32m   1870\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m errors\u001b[38;5;241m.\u001b[39mNameInUseError(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mparam\u001b[39m\u001b[38;5;124m'\u001b[39m, name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[1;32m   1871\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscope \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m\n\u001b[0;32m-> 1872\u001b[0m v \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscope\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparam\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minit_fn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43munbox\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43munbox\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1873\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state\u001b[38;5;241m.\u001b[39mchildren[name] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mparams\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m   1874\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m v\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/core/scope.py:950\u001b[0m, in \u001b[0;36mScope.param\u001b[0;34m(self, name, init_fn, unbox, *init_args, **init_kwargs)\u001b[0m\n\u001b[1;32m    944\u001b[0m   value \u001b[38;5;241m=\u001b[39m meta\u001b[38;5;241m.\u001b[39munbox(value)\n\u001b[1;32m    945\u001b[0m \u001b[38;5;66;03m# Validate that the shape of the init_fn output is the same as the shape\u001b[39;00m\n\u001b[1;32m    946\u001b[0m \u001b[38;5;66;03m# of the existing parameter. This is to make sure that the hparams set up\u001b[39;00m\n\u001b[1;32m    947\u001b[0m \u001b[38;5;66;03m# in a Flax Module match the shapes coming in during apply, and if not,\u001b[39;00m\n\u001b[1;32m    948\u001b[0m \u001b[38;5;66;03m# catch it with an error message.\u001b[39;00m\n\u001b[1;32m    949\u001b[0m \u001b[38;5;66;03m# NOTE: We could consider moving this to `self.`\u001b[39;00m\n\u001b[0;32m--> 950\u001b[0m abs_value \u001b[38;5;241m=\u001b[39m \u001b[43mjax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meval_shape\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    951\u001b[0m \u001b[43m  \u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43minit_fn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrandom\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    952\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    953\u001b[0m abs_value_flat \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mtree_util\u001b[38;5;241m.\u001b[39mtree_leaves(abs_value)\n\u001b[1;32m    954\u001b[0m value_flat \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mtree_util\u001b[38;5;241m.\u001b[39mtree_leaves(value)\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api.py:2649\u001b[0m, in \u001b[0;36meval_shape\u001b[0;34m(fun, *args, **kwargs)\u001b[0m\n\u001b[1;32m   2647\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m: \u001b[38;5;28mhash\u001b[39m(fun)\n\u001b[1;32m   2648\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m: fun \u001b[38;5;241m=\u001b[39m partial(fun)\n\u001b[0;32m-> 2649\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mjit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meval_shape\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:490\u001b[0m, in \u001b[0;36m_make_jit_wrapper.<locals>.eval_shape\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    488\u001b[0m \u001b[38;5;129m@api_boundary\u001b[39m\n\u001b[1;32m    489\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21meval_shape\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m--> 490\u001b[0m   p, _ \u001b[38;5;241m=\u001b[39m \u001b[43m_infer_params\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjit_info\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    491\u001b[0m   out_s \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(s, UnspecifiedValue) \u001b[38;5;28;01melse\u001b[39;00m s \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m p\u001b[38;5;241m.\u001b[39mparams[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mout_shardings\u001b[39m\u001b[38;5;124m'\u001b[39m]]\n\u001b[1;32m    492\u001b[0m   \u001b[38;5;66;03m# TODO(yashkatariya): Add `Layout` to SDS.\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:740\u001b[0m, in \u001b[0;36m_infer_params\u001b[0;34m(fun, ji, args, kwargs)\u001b[0m\n\u001b[1;32m    738\u001b[0m entry \u001b[38;5;241m=\u001b[39m _infer_params_cached(fun, ji, signature, avals, pjit_mesh, resource_env)\n\u001b[1;32m    739\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m entry\u001b[38;5;241m.\u001b[39mpjit_params \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 740\u001b[0m   p, args_flat \u001b[38;5;241m=\u001b[39m \u001b[43m_infer_params_impl\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    741\u001b[0m \u001b[43m      \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mji\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpjit_mesh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresource_env\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_avals\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mavals\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    742\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m p\u001b[38;5;241m.\u001b[39mattrs_tracked:  \u001b[38;5;66;03m# if attrs, don't popoulate the cache\u001b[39;00m\n\u001b[1;32m    743\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m p, p\u001b[38;5;241m.\u001b[39mconsts \u001b[38;5;241m+\u001b[39m args_flat\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:629\u001b[0m, in \u001b[0;36m_infer_params_impl\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    625\u001b[0m abstract_mesh \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    626\u001b[0m     get_abstract_mesh_from_avals(in_type)\n\u001b[1;32m    627\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mget_abstract_mesh() \u001b[38;5;28;01melse\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mget_abstract_mesh())\n\u001b[1;32m    628\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m mesh_lib\u001b[38;5;241m.\u001b[39mset_abstract_mesh(abstract_mesh):\n\u001b[0;32m--> 629\u001b[0m   jaxpr, consts, out_avals, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43m_create_pjit_jaxpr\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    630\u001b[0m \u001b[43m      \u001b[49m\u001b[43mflat_fun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mattr_token\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdbg\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    631\u001b[0m \u001b[43m      \u001b[49m\u001b[43mHashableFunction\u001b[49m\u001b[43m(\u001b[49m\u001b[43mres_paths\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclosure\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    632\u001b[0m \u001b[43m      \u001b[49m\u001b[43mIgnoreKey\u001b[49m\u001b[43m(\u001b[49m\u001b[43mji\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minline\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    633\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mmutable_array_checks\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    634\u001b[0m     _check_no_aliased_closed_over_refs(dbg, (\u001b[38;5;241m*\u001b[39mjaxpr\u001b[38;5;241m.\u001b[39mconsts, \u001b[38;5;241m*\u001b[39mconsts), explicit_args)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:349\u001b[0m, in \u001b[0;36mcache.<locals>.memoized_fun\u001b[0;34m(fun, *args)\u001b[0m\n\u001b[1;32m    347\u001b[0m   fun\u001b[38;5;241m.\u001b[39mpopulate_stores(stores)\n\u001b[1;32m    348\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 349\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    350\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m explain \u001b[38;5;129;01mand\u001b[39;00m config\u001b[38;5;241m.\u001b[39mexplain_cache_misses\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    351\u001b[0m     explain(fun\u001b[38;5;241m.\u001b[39mf, cache \u001b[38;5;129;01mis\u001b[39;00m new_cache, cache, key)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:1310\u001b[0m, in \u001b[0;36m_create_pjit_jaxpr\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m   1308\u001b[0m     attrs_tracked \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m   1309\u001b[0m   \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1310\u001b[0m     jaxpr, global_out_avals, consts, attrs_tracked \u001b[38;5;241m=\u001b[39m \u001b[43mpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrace_to_jaxpr_dynamic\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1311\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdebug_info\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpe_debug\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1312\u001b[0m     \u001b[38;5;66;03m# assert attr_data is sentinel or attr_data matches attrs_tracked\u001b[39;00m\n\u001b[1;32m   1313\u001b[0m \n\u001b[1;32m   1314\u001b[0m \u001b[38;5;66;03m# TODO(dougalm,mattjj): enable debug info with attrs_tracked\u001b[39;00m\n\u001b[1;32m   1315\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m config\u001b[38;5;241m.\u001b[39mdynamic_shapes\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m attrs_tracked:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/profiler.py:333\u001b[0m, in \u001b[0;36mannotate_function.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(func)\n\u001b[1;32m    331\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    332\u001b[0m   \u001b[38;5;28;01mwith\u001b[39;00m TraceAnnotation(name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdecorator_kwargs):\n\u001b[0;32m--> 333\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    334\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m wrapper\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:2159\u001b[0m, in \u001b[0;36mtrace_to_jaxpr_dynamic\u001b[0;34m(fun, in_avals, debug_info, keep_inputs)\u001b[0m\n\u001b[1;32m   2157\u001b[0m in_tracers \u001b[38;5;241m=\u001b[39m [t \u001b[38;5;28;01mfor\u001b[39;00m t, keep \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in_tracers, keep_inputs) \u001b[38;5;28;01mif\u001b[39;00m keep]\n\u001b[1;32m   2158\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m core\u001b[38;5;241m.\u001b[39mset_current_trace(trace):\n\u001b[0;32m-> 2159\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mfun\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall_wrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43min_tracers\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2161\u001b[0m out_tracers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmap\u001b[39m(trace\u001b[38;5;241m.\u001b[39mto_jaxpr_tracer, ans)\n\u001b[1;32m   2162\u001b[0m _check_no_returned_refs(debug_info, out_tracers)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/linear_util.py:192\u001b[0m, in \u001b[0;36mWrappedFun.call_wrapped\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcall_wrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    191\u001b[0m \u001b[38;5;250m  \u001b[39m\u001b[38;5;124;03m\"\"\"Calls the transformed function\"\"\"\u001b[39;00m\n\u001b[0;32m--> 192\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mf_transformed\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:292\u001b[0m, in \u001b[0;36m_argnums_partial\u001b[0;34m(_fun, _dyn_argnums, _fixed_args, *dyn_args, **kwargs)\u001b[0m\n\u001b[1;32m    290\u001b[0m args \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mnext\u001b[39m(fixed_args_)\u001b[38;5;241m.\u001b[39mval \u001b[38;5;28;01mif\u001b[39;00m x \u001b[38;5;129;01mis\u001b[39;00m sentinel \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m args]\n\u001b[1;32m    291\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28mnext\u001b[39m(fixed_args_, sentinel) \u001b[38;5;129;01mis\u001b[39;00m sentinel\n\u001b[0;32m--> 292\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:72\u001b[0m, in \u001b[0;36mflatten_fun\u001b[0;34m(f, store, in_tree, *args_flat)\u001b[0m\n\u001b[1;32m     69\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m     70\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mflatten_fun\u001b[39m(f, store, in_tree, \u001b[38;5;241m*\u001b[39margs_flat):\n\u001b[1;32m     71\u001b[0m   py_args, py_kwargs \u001b[38;5;241m=\u001b[39m tree_unflatten(in_tree, args_flat)\n\u001b[0;32m---> 72\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mpy_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     73\u001b[0m   ans, out_tree \u001b[38;5;241m=\u001b[39m tree_flatten(ans)\n\u001b[1;32m     74\u001b[0m   store\u001b[38;5;241m.\u001b[39mstore(out_tree)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/api_util.py:652\u001b[0m, in \u001b[0;36mresult_paths\u001b[0;34m(_fun, _store, *args, **kwargs)\u001b[0m\n\u001b[1;32m    649\u001b[0m \u001b[38;5;129m@lu\u001b[39m\u001b[38;5;241m.\u001b[39mtransformation_with_aux2\n\u001b[1;32m    650\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mresult_paths\u001b[39m(_fun, _store, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    651\u001b[0m   \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlinear_util transform to get output pytree paths of pre-flattened function.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 652\u001b[0m   ans \u001b[38;5;241m=\u001b[39m \u001b[43m_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    653\u001b[0m   _store\u001b[38;5;241m.\u001b[39mstore([keystr(path) \u001b[38;5;28;01mfor\u001b[39;00m path, _ \u001b[38;5;129;01min\u001b[39;00m generate_key_paths(ans)])\n\u001b[1;32m    654\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m ans\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/flax/core/scope.py:951\u001b[0m, in \u001b[0;36mScope.param.<locals>.<lambda>\u001b[0;34m()\u001b[0m\n\u001b[1;32m    944\u001b[0m   value \u001b[38;5;241m=\u001b[39m meta\u001b[38;5;241m.\u001b[39munbox(value)\n\u001b[1;32m    945\u001b[0m \u001b[38;5;66;03m# Validate that the shape of the init_fn output is the same as the shape\u001b[39;00m\n\u001b[1;32m    946\u001b[0m \u001b[38;5;66;03m# of the existing parameter. This is to make sure that the hparams set up\u001b[39;00m\n\u001b[1;32m    947\u001b[0m \u001b[38;5;66;03m# in a Flax Module match the shapes coming in during apply, and if not,\u001b[39;00m\n\u001b[1;32m    948\u001b[0m \u001b[38;5;66;03m# catch it with an error message.\u001b[39;00m\n\u001b[1;32m    949\u001b[0m \u001b[38;5;66;03m# NOTE: We could consider moving this to `self.`\u001b[39;00m\n\u001b[1;32m    950\u001b[0m abs_value \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39meval_shape(\n\u001b[0;32m--> 951\u001b[0m   \u001b[38;5;28;01mlambda\u001b[39;00m: \u001b[43minit_fn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrandom\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43minit_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    952\u001b[0m )\n\u001b[1;32m    953\u001b[0m abs_value_flat \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mtree_util\u001b[38;5;241m.\u001b[39mtree_leaves(abs_value)\n\u001b[1;32m    954\u001b[0m value_flat \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mtree_util\u001b[38;5;241m.\u001b[39mtree_leaves(value)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/nn/initializers.py:338\u001b[0m, in \u001b[0;36mvariance_scaling.<locals>.init\u001b[0;34m(key, shape, dtype)\u001b[0m\n\u001b[1;32m    335\u001b[0m   \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    336\u001b[0m     \u001b[38;5;66;03m# constant is stddev of complex standard normal truncated to 2\u001b[39;00m\n\u001b[1;32m    337\u001b[0m     stddev \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39msqrt(variance) \u001b[38;5;241m/\u001b[39m jnp\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;241m.95311164380491208\u001b[39m, dtype)\n\u001b[0;32m--> 338\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_complex_truncated_normal\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshape\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;241m*\u001b[39m stddev\n\u001b[1;32m    339\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m distribution \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnormal\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    340\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m random\u001b[38;5;241m.\u001b[39mnormal(key, shape, dtype) \u001b[38;5;241m*\u001b[39m jnp\u001b[38;5;241m.\u001b[39msqrt(variance)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/nn/initializers.py:257\u001b[0m, in \u001b[0;36m_complex_truncated_normal\u001b[0;34m(key, upper, shape, dtype)\u001b[0m\n\u001b[1;32m    255\u001b[0m real_dtype \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;241m0\u001b[39m, dtype)\u001b[38;5;241m.\u001b[39mreal\u001b[38;5;241m.\u001b[39mdtype\n\u001b[1;32m    256\u001b[0m dtype \u001b[38;5;241m=\u001b[39m dtypes\u001b[38;5;241m.\u001b[39mto_complex_dtype(real_dtype)\n\u001b[0;32m--> 257\u001b[0m t \u001b[38;5;241m=\u001b[39m (\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mjnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexp\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mupper\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    258\u001b[0m \u001b[43m     \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mrandom\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muniform\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey_r\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshape\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mreal_dtype\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mastype\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    259\u001b[0m r \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39msqrt(\u001b[38;5;241m-\u001b[39mjnp\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;241m1\u001b[39m \u001b[38;5;241m-\u001b[39m t))\n\u001b[1;32m    260\u001b[0m theta \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m2\u001b[39m \u001b[38;5;241m*\u001b[39m jnp\u001b[38;5;241m.\u001b[39mpi \u001b[38;5;241m*\u001b[39m random\u001b[38;5;241m.\u001b[39muniform(key_theta, shape, real_dtype)\u001b[38;5;241m.\u001b[39mastype(dtype)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/numpy/array_methods.py:1061\u001b[0m, in \u001b[0;36m_forward_operator_to_aval.<locals>.op\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1060\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mop\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs):\n\u001b[0;32m-> 1061\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43maval\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m_\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mname\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/numpy/array_methods.py:578\u001b[0m, in \u001b[0;36m_defer_to_unrecognized_arg.<locals>.deferring_binary_op\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m    576\u001b[0m args \u001b[38;5;241m=\u001b[39m (other, \u001b[38;5;28mself\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m swap \u001b[38;5;28;01melse\u001b[39;00m (\u001b[38;5;28mself\u001b[39m, other)\n\u001b[1;32m    577\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(other, _accepted_binop_types):\n\u001b[0;32m--> 578\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mbinary_op\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    579\u001b[0m \u001b[38;5;66;03m# Note: don't use isinstance here, because we don't want to raise for\u001b[39;00m\n\u001b[1;32m    580\u001b[0m \u001b[38;5;66;03m# subclasses, e.g. NamedTuple objects that may override operators.\u001b[39;00m\n\u001b[1;32m    581\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(other) \u001b[38;5;129;01min\u001b[39;00m _rejected_binop_types:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/numpy/ufunc_api.py:179\u001b[0m, in \u001b[0;36mufunc.__call__\u001b[0;34m(self, out, where, *args)\u001b[0m\n\u001b[1;32m    177\u001b[0m   \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwhere argument of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    178\u001b[0m call \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__static_props[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcall\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_call_vectorized\n\u001b[0;32m--> 179\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:340\u001b[0m, in \u001b[0;36m_cpp_pjit.<locals>.cache_miss\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    335\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config\u001b[38;5;241m.\u001b[39mno_tracing\u001b[38;5;241m.\u001b[39mvalue:\n\u001b[1;32m    336\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mre-tracing function \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mjit_info\u001b[38;5;241m.\u001b[39mfun_sourceinfo\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m for \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    337\u001b[0m                      \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`jit`, but \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mno_tracing\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is set\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    339\u001b[0m (outs, out_flat, out_tree, args_flat, jaxpr, attrs_tracked, executable,\n\u001b[0;32m--> 340\u001b[0m  pgle_profiler) \u001b[38;5;241m=\u001b[39m \u001b[43m_python_pjit_helper\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjit_info\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    342\u001b[0m maybe_fastpath_data \u001b[38;5;241m=\u001b[39m _get_fastpath_data(\n\u001b[1;32m    343\u001b[0m     executable, out_tree, args_flat, out_flat, attrs_tracked, jaxpr\u001b[38;5;241m.\u001b[39meffects,\n\u001b[1;32m    344\u001b[0m     jaxpr\u001b[38;5;241m.\u001b[39mconsts, jit_info\u001b[38;5;241m.\u001b[39mabstracted_axes,\n\u001b[1;32m    345\u001b[0m     pgle_profiler)\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m outs, maybe_fastpath_data, _need_to_rebuild_with_fdo(pgle_profiler)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:200\u001b[0m, in \u001b[0;36m_python_pjit_helper\u001b[0;34m(fun, jit_info, *args, **kwargs)\u001b[0m\n\u001b[1;32m    198\u001b[0m   out_flat, compiled, profiler \u001b[38;5;241m=\u001b[39m _pjit_call_impl_python(\u001b[38;5;241m*\u001b[39margs_flat, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mp\u001b[38;5;241m.\u001b[39mparams)\n\u001b[1;32m    199\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 200\u001b[0m   out_flat \u001b[38;5;241m=\u001b[39m \u001b[43mpjit_p\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbind\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs_flat\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    201\u001b[0m   compiled \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    202\u001b[0m   profiler \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/core.py:463\u001b[0m, in \u001b[0;36mPrimitive.bind\u001b[0;34m(self, *args, **params)\u001b[0m\n\u001b[1;32m    461\u001b[0m trace_ctx\u001b[38;5;241m.\u001b[39mset_trace(eval_trace)\n\u001b[1;32m    462\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 463\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbind_with_trace\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprev_trace\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    464\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    465\u001b[0m   trace_ctx\u001b[38;5;241m.\u001b[39mset_trace(prev_trace)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/core.py:468\u001b[0m, in \u001b[0;36mPrimitive.bind_with_trace\u001b[0;34m(self, trace, args, params)\u001b[0m\n\u001b[1;32m    467\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mbind_with_trace\u001b[39m(\u001b[38;5;28mself\u001b[39m, trace, args, params):\n\u001b[0;32m--> 468\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtrace\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprocess_primitive\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:1887\u001b[0m, in \u001b[0;36mDynamicJaxprTrace.process_primitive\u001b[0;34m(self, primitive, tracers, params)\u001b[0m\n\u001b[1;32m   1885\u001b[0m jaxpr_tracers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmap\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mto_jaxpr_tracer, tracers)\n\u001b[1;32m   1886\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m primitive \u001b[38;5;129;01min\u001b[39;00m custom_staging_rules:\n\u001b[0;32m-> 1887\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcustom_staging_rules\u001b[49m\u001b[43m[\u001b[49m\u001b[43mprimitive\u001b[49m\u001b[43m]\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mjaxpr_tracers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1888\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdefault_process_primitive(primitive, jaxpr_tracers, params)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:1803\u001b[0m, in \u001b[0;36mpjit_staging_rule\u001b[0;34m(trace, *args, **params)\u001b[0m\n\u001b[1;32m   1802\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpjit_staging_rule\u001b[39m(trace, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mparams):\n\u001b[0;32m-> 1803\u001b[0m   jaxpr, in_fwd, out_shardings, out_layouts \u001b[38;5;241m=\u001b[39m \u001b[43m_pjit_forwarding\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1804\u001b[0m \u001b[43m      \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mjaxpr\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mout_shardings\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mout_layouts\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1805\u001b[0m   params \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m(params, jaxpr\u001b[38;5;241m=\u001b[39mjaxpr, out_shardings\u001b[38;5;241m=\u001b[39mout_shardings,\n\u001b[1;32m   1806\u001b[0m                 out_layouts\u001b[38;5;241m=\u001b[39mout_layouts)\n\u001b[1;32m   1807\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m (params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minline\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m   1808\u001b[0m       \u001b[38;5;28mall\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(i, UnspecifiedValue) \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124min_shardings\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m   1809\u001b[0m       \u001b[38;5;28mall\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(o, UnspecifiedValue) \u001b[38;5;28;01mfor\u001b[39;00m o \u001b[38;5;129;01min\u001b[39;00m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mout_shardings\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m   1810\u001b[0m       \u001b[38;5;28mall\u001b[39m(i \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124min_layouts\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m   1811\u001b[0m       \u001b[38;5;28mall\u001b[39m(o \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m o \u001b[38;5;129;01min\u001b[39;00m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mout_layouts\u001b[39m\u001b[38;5;124m\"\u001b[39m])):\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/pjit.py:1858\u001b[0m, in \u001b[0;36m_pjit_forwarding\u001b[0;34m(jaxpr, out_shardings, out_layouts)\u001b[0m\n\u001b[1;32m   1857\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_pjit_forwarding\u001b[39m(jaxpr, out_shardings, out_layouts):\n\u001b[0;32m-> 1858\u001b[0m   in_fwd: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mint\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m] \u001b[38;5;241m=\u001b[39m \u001b[43mpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jaxpr_forwarding\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjaxpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjaxpr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1859\u001b[0m   in_fwd \u001b[38;5;241m=\u001b[39m [fwd \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(os, UnspecifiedValue) \u001b[38;5;129;01mand\u001b[39;00m ol \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m fwd, os, ol\n\u001b[1;32m   1860\u001b[0m             \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in_fwd, out_shardings, out_layouts)]\n\u001b[1;32m   1861\u001b[0m   keep \u001b[38;5;241m=\u001b[39m [f \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m in_fwd]\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:1328\u001b[0m, in \u001b[0;36m_jaxpr_forwarding\u001b[0;34m(jaxpr)\u001b[0m\n\u001b[1;32m   1326\u001b[0m         fwds[v_orig] \u001b[38;5;241m=\u001b[39m v_new\n\u001b[1;32m   1327\u001b[0m idxs: \u001b[38;5;28mdict\u001b[39m[Var, \u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m {v: i \u001b[38;5;28;01mfor\u001b[39;00m i, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(jaxpr\u001b[38;5;241m.\u001b[39minvars)}\n\u001b[0;32m-> 1328\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [\u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(v) \u001b[38;5;129;01mis\u001b[39;00m Literal \u001b[38;5;28;01melse\u001b[39;00m idxs\u001b[38;5;241m.\u001b[39mget(fwds\u001b[38;5;241m.\u001b[39mget(v))  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m   1329\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m jaxpr\u001b[38;5;241m.\u001b[39moutvars]\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/partial_eval.py:1328\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m   1326\u001b[0m         fwds[v_orig] \u001b[38;5;241m=\u001b[39m v_new\n\u001b[1;32m   1327\u001b[0m idxs: \u001b[38;5;28mdict\u001b[39m[Var, \u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m {v: i \u001b[38;5;28;01mfor\u001b[39;00m i, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(jaxpr\u001b[38;5;241m.\u001b[39minvars)}\n\u001b[0;32m-> 1328\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [\u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(v) \u001b[38;5;129;01mis\u001b[39;00m Literal \u001b[38;5;28;01melse\u001b[39;00m idxs\u001b[38;5;241m.\u001b[39mget(fwds\u001b[38;5;241m.\u001b[39mget(v))  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m   1329\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m jaxpr\u001b[38;5;241m.\u001b[39moutvars]\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import tree_util\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 定义熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # 记录初始温度，用于后续温度递减计算\n", "        self.init_temperature = temperature\n", "        self.temperature = temperature\n", "\n", "    def _step_with_state(self, state):\n", "        # 基础能量梯度更新步骤\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "        \n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "        \n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "        \n", "        # 更新参数\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "# 添加进度条以及温度递减方案\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"运行优化并在 tqdm 进度条中显示 Temperature，Energy，E_var，E_err 和 Rel_err(%)\"\"\"\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 更新温度：使用初始温度乘以递减因子\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0) / 2.0)\n", "            self.advance(1)\n", "\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}', \n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "\n", "\n", "temperature_original = 1.0  # 初始温度\n", "reference_energy = -16.2631\n", "\n", "vmc = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    temperature=temperature_original,\n", "    hamiltonian=hamiltonian,\n", "    optimizer=optimizer,\n", "    diag_shift=0.01,\n", "    variational_state=vqs\n", ")\n", "\n", "start = time.time()\n", "vmc.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"优化耗时: {end - start:.2f}秒\")\n", "# Lx=3, Ly=3: 100% 1000/1000 [24:22<00:00, 1.46s/it, Temp=0.0000, Energy=-14.404755+0.002588j, E_var=3.695185, E_err=0.030036, Rel_err(%)=11.4197]"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}