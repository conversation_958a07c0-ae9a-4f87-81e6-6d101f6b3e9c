{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： True\n", "可用设备： [CudaDevice(id=0), CudaDevice(id=1)]\n"]}], "source": ["# %%\n", "# %%\n", "import os\n", "import jax\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "\n", "import os\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '1'\n", "\n", "import netket as nk\n", "import jax\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "# %%\n", "# %%\n", "import os\n", "import logging\n", "import sys\n", "import jax\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from functools import partial\n", "from netket.nn import log_cosh\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group, C\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator\n", "from netket.vqs import VariationalState\n", "from scipy import sparse as _sparse\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.operator import LocalOperator as _LocalOperator\n", "from tqdm.notebook import tqdm\n", "from jax import tree\n", "from netket.nn.blocks import SymmExpSum\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import jax.numpy as jnp\n", "from flax import linen as nn\n", "from jax.nn.initializers import zeros, lecun_normal, normal\n", "from netket.utils import HashableArray\n", "from netket.jax import logsumexp_cplx\n", "from netket.nn.activation import reim_selu\n", "from netket.nn.symmetric_linear import DenseSymmMatrix, DenseEquivariantIrrep\n", "from typing import Callable, Any, Optional\n", "from einops import rearrange\n", "\n", "# 默认初始化器\n", "default_gcnn_initializer = lecun_normal(in_axis=1, out_axis=0)\n", "default_attn_initializer = normal(0.02)\n", "\n", "class MultiHeadAttention(nn.Module):\n", "    \"\"\"对称多头注意力机制\"\"\"\n", "    \n", "    features: int\n", "    num_heads: int\n", "    head_dim: int\n", "    irreps: tuple  # 不可约表示矩阵列表\n", "    symmetries: HashableArray  # 对称操作数组\n", "    dropout_rate: float = 0.0\n", "    param_dtype: Any = np.complex128\n", "    \n", "    @nn.compact\n", "    def __call__(self, x, training=True):\n", "        # 输入形状: [batch_size, n_sites, features]\n", "        batch_size, n_sites, _ = x.shape\n", "        \n", "        # 定义Q、K、V线性变换（保持等变性）\n", "        q = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        k = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        v = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        # 重塑为多头格式\n", "        q = rearrange(q, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        k = rearrange(k, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        v = rearrange(v, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        \n", "        # 计算注意力分数\n", "        scale = 1.0 / jnp.sqrt(self.head_dim)\n", "        attn_weights = jnp.einsum('bhid,bhjd->bhij', q, jnp.conj(k)) * scale\n", "        \n", "        # 应用注意力权重（使用复值softmax，保持相位信息）\n", "        attn_weights_abs = jnp.abs(attn_weights)\n", "        attn_weights_phase = jnp.exp(1j * jnp.angle(attn_weights))\n", "        attn_weights_softmax = nn.softmax(attn_weights_abs, axis=-1)\n", "        attn_weights = attn_weights_softmax * attn_weights_phase\n", "        \n", "        # 应用dropout（如果需要）\n", "        if self.dropout_rate > 0 and training:\n", "            attn_weights = nn.dropout(\n", "                rng=self.make_rng('dropout'),\n", "                rate=self.dropout_rate,\n", "                x=attn_weights\n", "            )\n", "        \n", "        # 计算注意力输出\n", "        output = jnp.einsum('bhij,bhjd->bhid', attn_weights, v)\n", "        output = rearrange(output, 'b h n d -> b n (h d)')\n", "        \n", "        # 输出投影\n", "        output = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.features,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(output)\n", "        \n", "        return output\n", "\n", "class GCNN_Attention_Irrep(nn.Module):\n", "    \"\"\"结合注意力机制的群等变神经网络（GCNN-GCNN-Attention-Attention结构）\"\"\"\n", "    \n", "    symmetries: HashableArray  # 对称操作数组\n", "    irreps: tuple  # 不可约表示矩阵列表\n", "    gcnn_layers: int  # GCNN层数 \n", "    attention_layers: int  # 注意力层数\n", "    features: tuple  # 每层特征数\n", "    characters: Has<PERSON>leArray  # 指定所需对称表示的字符\n", "    num_heads: int = 4  # 注意力头数\n", "    head_dim: int = 32  # 每个头的维度\n", "    dropout_rate: float = 0.0  # dropout比率\n", "    parity: int = 1  # 宇称值\n", "    param_dtype: any = np.complex128  # 参数数据类型\n", "    input_mask: any = None  # 输入掩码\n", "    equal_amplitudes: bool = False  # 是否强制等幅\n", "    use_bias: bool = True  # 是否使用偏置\n", "\n", "    def setup(self):\n", "        total_layers = self.gcnn_layers + self.attention_layers\n", "        \n", "        # 第一层：对称化线性变换\n", "        self.dense_symm = DenseSymmMatrix(\n", "            symmetries=self.symmetries,\n", "            features=self.features[0],\n", "            param_dtype=self.param_dtype,\n", "            use_bias=self.use_bias,\n", "            kernel_init=default_gcnn_initializer,\n", "            bias_init=zeros,\n", "            mask=self.input_mask,\n", "        )\n", "        \n", "        # GCNN层 - 使用元组而不是列表\n", "        self.equivariant_layers = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.gcnn_layers)\n", "        )\n", "        \n", "        self.equivariant_layers_flip = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.gcnn_layers)\n", "        )\n", "        \n", "        # 添加特征维度的投影层，用于残差连接\n", "        self.gcnn_projections = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=False,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "            )\n", "            if self.features[layer] != self.features[layer + 1] else None\n", "            for layer in range(self.gcnn_layers)\n", "        )\n", "        \n", "        # 注意力模块 - 只为特定层创建\n", "        self.attention_modules = tuple(\n", "            MultiHeadAttention(\n", "                features=self.features[self.gcnn_layers + layer],  # 注意索引修改\n", "                num_heads=self.num_heads,\n", "                head_dim=self.head_dim,\n", "                irreps=self.irreps,\n", "                symmetries=self.symmetries,\n", "                dropout_rate=self.dropout_rate,\n", "                param_dtype=self.param_dtype,\n", "            )\n", "            for layer in range(self.attention_layers)\n", "        )\n", "        \n", "        # 添加注意力层的特征维度投影\n", "        self.attention_projections = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[self.gcnn_layers + layer + 1] if layer + 1 < self.attention_layers else self.features[self.gcnn_layers + layer],\n", "                use_bias=False,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "            )\n", "            if layer + 1 < self.attention_layers and self.features[self.gcnn_layers + layer] != self.features[self.gcnn_layers + layer + 1] else None\n", "            for layer in range(self.attention_layers)\n", "        )\n", "        \n", "        # 层归一化\n", "        self.layer_norms = tuple(\n", "            nn.LayerNorm(epsilon=1e-5, param_dtype=self.param_dtype)\n", "            for _ in range(total_layers)\n", "        )\n", "\n", "    @nn.compact\n", "    def __call__(self, x, training=True):\n", "        if x.ndim < 3:\n", "            x = jnp.expand_dims(x, -2)  # 添加特征维度\n", "\n", "        # 处理原始输入和翻转输入\n", "        x_flip = self.dense_symm(-1 * x)\n", "        x = self.dense_symm(x)\n", "        \n", "        # 先处理GCNN层\n", "        for layer in range(self.gcnn_layers):\n", "            # 应用激活函数\n", "            x = reim_selu(x)\n", "            x_flip = reim_selu(x_flip)\n", "\n", "            # 保存原始输入用于残差连接\n", "            residual_x = x\n", "            residual_x_flip = x_flip\n", "            \n", "            # 卷积路径\n", "            x_conv = (\n", "                self.equivariant_layers[layer](x)\n", "                + self.equivariant_layers_flip[layer](x_flip)\n", "            ) / jnp.sqrt(2)\n", "            \n", "            x_flip_conv = (\n", "                self.equivariant_layers[layer](x_flip)\n", "                + self.equivariant_layers_flip[layer](x)\n", "            ) / jnp.sqrt(2)\n", "            \n", "            # 将卷积输出作为基础\n", "            x = x_conv\n", "            x_flip = x_flip_conv\n", "            \n", "            # 层归一化\n", "            x = self.layer_norms[layer](x)\n", "            x_flip = self.layer_norms[layer](x_flip)\n", "            \n", "            # 残差连接 - 如果特征维度不匹配，先进行投影\n", "            if self.gcnn_projections[layer] is not None:\n", "                residual_x = self.gcnn_projections[layer](residual_x)\n", "                residual_x_flip = self.gcnn_projections[layer](residual_x_flip)\n", "            \n", "            x = x + residual_x\n", "            x_flip = x_flip + residual_x_flip\n", "        \n", "        # 然后处理注意力层\n", "        for layer in range(self.attention_layers):\n", "            # 应用激活函数\n", "            x = reim_selu(x)\n", "            x_flip = reim_selu(x_flip)\n", "\n", "            # 保存原始输入用于残差连接\n", "            residual_x = x\n", "            residual_x_flip = x_flip\n", "            \n", "            # 注意力路径（只处理x，不处理x_flip，以保持宇称对称性）\n", "            x_attn = self.attention_modules[layer](x, training=training)\n", "            \n", "            # 混合注意力结果\n", "            x = x + x_attn\n", "            \n", "            # 层归一化\n", "            norm_idx = self.gcnn_layers + layer\n", "            x = self.layer_norms[norm_idx](x)\n", "            x_flip = self.layer_norms[norm_idx](x_flip)\n", "            \n", "            # 如果需要投影，进行特征维度投影\n", "            if layer < len(self.attention_projections) and self.attention_projections[layer] is not None:\n", "                x = self.attention_projections[layer](x)\n", "                x_flip = self.attention_projections[layer](x_flip)\n", "                residual_x = self.attention_projections[layer](residual_x)\n", "                residual_x_flip = self.attention_projections[layer](residual_x_flip)\n", "            \n", "            # 残差连接\n", "            x = x + residual_x\n", "            x_flip = x_flip + residual_x_flip\n", "            \n", "        # 连接原始和翻转路径的输出\n", "        x = jnp.concatenate((x, x_flip), -1)\n", "\n", "        # 构建宇称为1的字符\n", "        par_chars = jnp.expand_dims(\n", "            jnp.concatenate(\n", "                (jnp.array(self.characters), jnp.array(self.characters)), 0\n", "            ),\n", "            (0, 1),\n", "        )\n", "\n", "        # 应用复值logsumexp\n", "        x = logsumexp_cplx(x, axis=(-2, -1), b=par_chars)\n", "\n", "        return x\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def create_gcnn_attention(\n", "    symmetries, \n", "    gcnn_layers=2,\n", "    attention_layers=2,\n", "    features=None, \n", "    num_heads=4,\n", "    head_dim=32,\n", "    dropout_rate=0.0,\n", "    mask=None, \n", "    characters=None\n", "):\n", "    \"\"\"创建带有注意力机制的GCNN模型的简化函数，支持GCNN-GCNN-Attention-Attention结构\"\"\"\n", "    \n", "    total_layers = gcnn_layers + attention_layers\n", "    \n", "    # 处理特征参数\n", "    if features is None:\n", "        features = (32,) * (total_layers + 1)  # 默认所有层的特征数为32\n", "    elif isinstance(features, int):\n", "        features = (features,) * (total_layers + 1)\n", "    \n", "    # 确保features长度足够\n", "    if len(features) < total_layers + 1:\n", "        features = list(features)\n", "        features.extend([features[-1]] * (total_layers + 1 - len(features)))\n", "        features = tuple(features)\n", "    \n", "    # 处理字符参数\n", "    if characters is None:\n", "        characters = HashableArray(np.ones(len(np.asarray(symmetries))))\n", "    else:\n", "        characters = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(characters)\n", "    \n", "    # 获取不可约表示\n", "    sym = HashableArray(np.asarray(symmetries))\n", "    irreps = tuple(HashableArray(irrep) for irrep in symmetries.irrep_matrices())\n", "    \n", "    # 处理掩码\n", "    input_mask = HashableArray(mask) if mask is not None else None\n", "    \n", "    # 创建和返回模型\n", "    return GCNN_Attention_Irrep(\n", "        symmetries=sym,\n", "        irreps=irreps,\n", "        gcnn_layers=gcnn_layers,\n", "        attention_layers=attention_layers,\n", "        features=features,\n", "        characters=characters,\n", "        num_heads=num_heads,\n", "        head_dim=head_dim,\n", "        dropout_rate=dropout_rate,\n", "        parity=1,\n", "        param_dtype=np.complex128,\n", "        input_mask=input_mask,\n", "        equal_amplitudes=False,\n", "    )\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 哈密顿量参数\n", "J1 = 0.8\n", "J2 = 1.0\n", "Q = 0  # 四自旋相互作用强度，替换h项\n", "\n", "# Shastry-Sutherland晶格定义\n", "Lx = 3\n", "Ly = 3\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "lattice.draw()\n", "\n", "# %%\n", "# Hilbert空间定义\n", "hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# Q项需要的C_ij算子定义\n", "Cij = 0.25 * umum - SiSj\n", "Cij2 = np.kron(<PERSON>ij, Cij)  # 四自旋交互项\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hi, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hi, dtype=jnp.complex128)\n", "\n", "# 添加四自旋Q项 - 每个单元格(plaquette)上的四自旋相互作用\n", "for unit_x in range(Lx):\n", "    for unit_y in range(Ly):\n", "        # 找到单元格中的4个顶点\n", "        base_idx = 4 * (unit_x + unit_y * Lx)\n", "        plaq_sites = [\n", "            base_idx,                  # 左下角 (0.5, 0.5) \n", "            base_idx + 1,              # 右下角 (1.5, 0.5)\n", "            base_idx + 2,              # 右上角 (1.5, 1.5)\n", "            base_idx + 3               # 左上角 (0.5, 1.5)\n", "        ]\n", "        \n", "        # 两种不同的顺序添加四自旋相互作用Q项\n", "        # 按照顺时针方向连接\n", "        sites_clockwise = [plaq_sites]\n", "        operatorQ = [(-Q * Cij2).tolist()]\n", "        H_Q += nk.operator.LocalOperator(hi, operatorQ, sites_clockwise)\n", "        \n", "        # 按照交叉方向连接\n", "        sites_cross = [[plaq_sites[0], plaq_sites[2], plaq_sites[1], plaq_sites[3]]]\n", "        H_Q += nk.operator.LocalOperator(hi, operatorQ, sites_cross)\n", "\n", "# 合并两部分哈密顿量\n", "ha = H_J + H_Q\n", "ha = ha.to_jax_operator()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of symmetry operations: 72\n"]}], "source": ["# ----------------- 构造CTWF量子态 -----------------\n", "# 这里设置 patch_size 为2，令 L_eff = n_sites / (2^2)。对于Shastry-Sutherland晶格，\n", "# n_sites = Lx * Ly * 4 = 36 -> L_eff = 9。\n", "# 按照论文参数：d_model=18, h=2，且堆叠1层CTWF Block\n", "# 使用增强位置编码的方式初始化CTWF模型\n", "# 例如，使用2D位置编码：\n", "\n", "\n", "\n", "\n", "# 对称化（这里采用平移及旋转对称性）\n", "nc = 4\n", "cyclic_4 = PointGroup(\n", "    [Identity()] + [rotation((360 / nc)*i) for i in range(1, nc)],\n", "    ndim=2,\n", ")\n", "C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4\n", "C4v_symmetry = lattice.space_group(C4v)\n", "# # 平移对称性\n", "# trans_symmetry = lattice.translation_group()\n", "\n", "# D4对称性\n", "d4_group = D(4)\n", "\n", "# 定义新的对称中心\n", "new_origin = np.array([1.0, 1.0])\n", "\n", "# 调整对称群，以 new_origin 为新的旋转中心\n", "new_d4_group = d4_group.change_origin(new_origin)\n", "D4_symmetry = lattice.point_group(new_d4_group)\n", "\n", "\n", "symmetries=C4v_symmetry\n", "print(f\"Number of symmetry operations: {len(symmetries)}\")\n", "\n", "local_cluster = jnp.arange(Lx * Ly * 4).tolist()\n", "mask = jnp.zeros(lattice.n_nodes, dtype=bool)\n", "for i in local_cluster:\n", "    mask = mask.at[i].set(True)\n", "    \n", "sgb = lattice.space_group_builder(point_group=C4v)\n", "momentum = [0.0, 0.0]\n", "chi = sgb.space_group_irreps(momentum)[0]\n", "\n", "\n", "from netket.nn.blocks import SymmExpSum\n", "# 使用2个GCNN层和2个注意力层\n", "model = create_gcnn_attention(\n", "    symmetries=symmetries,\n", "    gcnn_layers=2,           # GCNN层数\n", "    attention_layers=1,      # 注意力层数\n", "    features=(4, 4, 4, 4, 4),  # 保持所有层特征数相同以避免维度不匹配\n", "    num_heads=2,             # 注意力头数\n", "    head_dim=4,              # 每个头的维度\n", "    dropout_rate=0.0,        # dropout比例\n", "    mask=mask,\n", "    characters=chi\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of model parameters: 14448\n"]}], "source": ["# ----------------- 训练设置 -----------------\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hi, graph=lattice, n_chains=2**12, d_max=2)\n", "\n", "# 使用MCState构造变分量子态\n", "vqs = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model,\n", "    n_samples=2**12,\n", "    n_samples_per_rank=None,\n", "    n_discard_per_chain=0,\n", "    chunk_size=2**8,\n", "    training_kwargs={\"holomorphic\": False}  # 非全纯函数\n", ")\n", "\n", "n_params = nk.jax.tree_size(vqs.parameters)\n", "print(f\"Number of model parameters: {n_params}\")  # 应接近7884"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3efb33a0f51b4a7a8b8861493dab861f", "version_major": 2, "version_minor": 0}, "text/plain": ["Lx=3, Ly=3:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 116\u001b[0m\n\u001b[1;32m    106\u001b[0m vmc \u001b[38;5;241m=\u001b[39m CustomFreeEnergyVMC_SRt(\n\u001b[1;32m    107\u001b[0m     reference_energy\u001b[38;5;241m=\u001b[39mreference_energy,\n\u001b[1;32m    108\u001b[0m     temperature\u001b[38;5;241m=\u001b[39mtemperature_original,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m     variational_state\u001b[38;5;241m=\u001b[39mvqs\n\u001b[1;32m    113\u001b[0m )\n\u001b[1;32m    115\u001b[0m start \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[0;32m--> 116\u001b[0m \u001b[43mvmc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn_iter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1000\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    117\u001b[0m end \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m    118\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m优化耗时: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mend\u001b[38;5;250m \u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;250m \u001b[39mstart\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m秒\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[8], line 79\u001b[0m, in \u001b[0;36mCustomFreeEnergyVMC_SRt.run\u001b[0;34m(self, n_iter, out)\u001b[0m\n\u001b[1;32m     76\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(n_iter):\n\u001b[1;32m     77\u001b[0m     \u001b[38;5;66;03m# 更新温度：使用初始温度乘以递减因子\u001b[39;00m\n\u001b[1;32m     78\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtemperature \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minit_temperature \u001b[38;5;241m*\u001b[39m (jnp\u001b[38;5;241m.\u001b[39mexp(\u001b[38;5;241m-\u001b[39mi \u001b[38;5;241m/\u001b[39m \u001b[38;5;241m50.0\u001b[39m))\n\u001b[0;32m---> 79\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madvance\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     81\u001b[0m     energy_mean \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39menergy\u001b[38;5;241m.\u001b[39mmean\n\u001b[1;32m     82\u001b[0m     energy_var \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39menergy\u001b[38;5;241m.\u001b[39mvariance\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/driver/abstract_variational_driver.py:247\u001b[0m, in \u001b[0;36mAbstractVariationalDriver.advance\u001b[0;34m(self, steps)\u001b[0m\n\u001b[1;32m    239\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21madvance\u001b[39m(\u001b[38;5;28mself\u001b[39m, steps: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[1;32m    240\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    241\u001b[0m \u001b[38;5;124;03m    Performs `steps` optimization steps.\u001b[39;00m\n\u001b[1;32m    242\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    245\u001b[0m \n\u001b[1;32m    246\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 247\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39miter(steps):\n\u001b[1;32m    248\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/driver/abstract_variational_driver.py:232\u001b[0m, in \u001b[0;36mAbstractVariationalDriver.iter\u001b[0;34m(self, n_steps, step)\u001b[0m\n\u001b[1;32m    230\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, n_steps, step):\n\u001b[1;32m    231\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, step):\n\u001b[0;32m--> 232\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_forward_and_backward\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    233\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    234\u001b[0m             \u001b[38;5;28;01myield\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_count\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/experimental/driver/vmc_srt.py:275\u001b[0m, in \u001b[0;36mVMC_SRt._forward_and_backward\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    272\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mreset()\n\u001b[1;32m    274\u001b[0m \u001b[38;5;66;03m# Compute the local energy estimator and average Energy\u001b[39;00m\n\u001b[0;32m--> 275\u001b[0m local_energies \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlocal_estimators\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_ham\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    277\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_loss_stats \u001b[38;5;241m=\u001b[39m nkstats\u001b[38;5;241m.\u001b[39mstatistics(local_energies)\n\u001b[1;32m    279\u001b[0m samples \u001b[38;5;241m=\u001b[39m _flatten_samples(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39msamples)\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:614\u001b[0m, in \u001b[0;36mMCState.local_estimators\u001b[0;34m(self, op, chunk_size)\u001b[0m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;129m@timing\u001b[39m\u001b[38;5;241m.\u001b[39mtimed\n\u001b[1;32m    592\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mlocal_estimators\u001b[39m(\u001b[38;5;28mself\u001b[39m, op: AbstractOperator, \u001b[38;5;241m*\u001b[39m, chunk_size: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    593\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    594\u001b[0m \u001b[38;5;124;03m    Compute the local estimators for the operator :code:`op` (also known as local energies\u001b[39;00m\n\u001b[1;32m    595\u001b[0m \u001b[38;5;124;03m    when :code:`op` is the Hamiltonian) at the current configuration samples :code:`self.samples`.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    612\u001b[0m \u001b[38;5;124;03m            of the model. (Default: :code:`self.chunk_size`)\u001b[39;00m\n\u001b[1;32m    613\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 614\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlocal_estimators\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunk_size\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/netket/vqs/mc/mc_state/state.py:799\u001b[0m, in \u001b[0;36mlocal_estimators\u001b[0;34m(state, op, chunk_size)\u001b[0m\n\u001b[1;32m    794\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    795\u001b[0m     kernel \u001b[38;5;241m=\u001b[39m nkjax\u001b[38;5;241m.\u001b[39mHashablePartial(\n\u001b[1;32m    796\u001b[0m         get_local_kernel(state, op, chunk_size), chunk_size\u001b[38;5;241m=\u001b[39mchunk_size\n\u001b[1;32m    797\u001b[0m     )\n\u001b[0;32m--> 799\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_local_estimators_kernel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkernel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_apply_fun\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshape\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvariables\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_args\u001b[49m\n\u001b[1;32m    801\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "# %%\n", "import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import tree_util\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 定义熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化\n", "# ...existing code...\n", "def clip_gradients(gradients, max_norm):\n", "    # 将梯度中的叶子节点转换为列表\n", "    grads_flat, tree_def = jax.tree_util.tree_flatten(gradients)\n", "    # 计算全局范数\n", "    global_norm = jnp.sqrt(sum([jnp.sum(jnp.square(g)) for g in grads_flat]))\n", "    # 计算裁切系数\n", "    clip_coef = jnp.minimum(1.0, max_norm / (global_norm + 1e-6))\n", "    # 应用裁切\n", "    return jax.tree_util.tree_map(lambda g: g * clip_coef, gradients)\n", "\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # 记录初始温度，用于后续温度递减计算\n", "        self.init_temperature = temperature\n", "        self.temperature = temperature\n", "        self.max_grad_norm = 0.7  # 最大范数，可以根据需要进行调整\n", "\n", "    def _step_with_state(self, state):\n", "        # 基础能量梯度更新步骤\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "\n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = jax.tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "\n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = jax.tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "\n", "        # 对梯度进行裁切\n", "         # 对梯度进行裁剪\n", "        # 在FreeEnergyVMC_SRt类中加强梯度裁剪\n", "        total_grad = tree_util.tree_map(lambda g_e, g_s: jnp.clip(g_e - g_s, -self.clip_norm, self.clip_norm),\n", "                              new_state.gradient, mT_grad_S)\n", "\n", "        # 更新参数\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "# ...existing code...\n", "\n", "\n", "# 添加进度条以及温度递减方案\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"运行优化并在 tqdm 进度条中显示 Temperature，Energy，E_var，E_err 和 Rel_err(%)\"\"\"\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 更新温度：使用初始温度乘以递减因子\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0))\n", "            self.advance(1)\n", "\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}', \n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "    \n", "\n", "\n", "temperature_original = 1.0  # 初始温度\n", "reference_energy = -16.2631\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.03)\n", "# optimizer = nk.optimizer.<PERSON>(learning_rate=0.03)\n", "\n", "\n", "\n", "vmc = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    temperature=temperature_original,\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=0.05,\n", "    variational_state=vqs\n", ")\n", "\n", "start = time.time()\n", "vmc.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"优化耗时: {end - start:.2f}秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}