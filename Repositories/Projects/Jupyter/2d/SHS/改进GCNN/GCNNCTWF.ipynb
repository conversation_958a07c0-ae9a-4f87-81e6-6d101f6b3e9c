{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： True\n", "可用设备： [CudaDevice(id=0), CudaDevice(id=1)]\n"]}], "source": ["import os\n", "import jax\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import logging\n", "import sys\n", "from functools import partial\n", "from tqdm.notebook import tqdm\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '1'\n", "\n", "import netket as nk\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from netket.nn.blocks import SymmExpSum\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator, LocalOperator as _LocalOperator\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.nn.activation import reim_selu\n", "from netket.jax import logsumexp_cplx\n", "from netket.nn.symmetric_linear import DenseSymmMatrix, DenseEquivariantIrrep\n", "from netket.utils import HashableArray\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group, C\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "\n", "# 显示分片模式和可用设备\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from jax.nn.initializers import zeros, lecun_normal, normal\n", "from typing import Any\n", "\n", "# 默认初始化器\n", "default_gcnn_initializer = lecun_normal(in_axis=1, out_axis=0)\n", "default_attn_initializer = normal(0.02)\n", "\n", "class MultiHeadAttention(nn.Module):\n", "    \"\"\"对称多头注意力机制（无dropout）\"\"\"\n", "    features: int\n", "    num_heads: int\n", "    head_dim: int\n", "    irreps: tuple  # 不可约表示矩阵列表\n", "    symmetries: HashableArray  # 对称操作数组\n", "    param_dtype: Any = np.complex128\n", "    \n", "    @nn.compact\n", "    def __call__(self, x, training=True):\n", "        # 输入形状: [batch_size, n_sites, features]\n", "        batch_size, n_sites, _ = x.shape\n", "        \n", "        # 定义 Q、K、V 等变线性变换\n", "        q = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        k = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        v = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.num_heads * self.head_dim,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(x)\n", "        \n", "        # 重塑为多头格式\n", "        q = rearrange(q, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        k = rearrange(k, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        v = rearrange(v, 'b n (h d) -> b h n d', h=self.num_heads)\n", "        \n", "        # 计算注意力分数\n", "        scale = 1.0 / jnp.sqrt(self.head_dim)\n", "        attn_weights = jnp.einsum('bhid,bhjd->bhij', q, jnp.conj(k)) * scale\n", "        \n", "        # 使用复值 softmax 保持相位信息\n", "        attn_weights_abs = jnp.abs(attn_weights)\n", "        attn_weights_phase = jnp.exp(1j * jnp.angle(attn_weights))\n", "        attn_weights_softmax = nn.softmax(attn_weights_abs, axis=-1)\n", "        attn_weights = attn_weights_softmax * attn_weights_phase\n", "        \n", "        # 计算注意力输出\n", "        output = jnp.einsum('bhij,bhjd->bhid', attn_weights, v)\n", "        output = rearrange(output, 'b h n d -> b n (h d)')\n", "        \n", "        # 输出投影\n", "        output = DenseEquivariantIrrep(\n", "            irreps=self.irreps,\n", "            features=self.features,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=default_attn_initializer,\n", "        )(output)\n", "        \n", "        return output\n", "\n", "\n", "class GCNN_Attention_Irrep(nn.Module):\n", "    \"\"\"结合注意力机制的群等变神经网络（无dropout）\"\"\"\n", "    \n", "    symmetries: HashableArray  # 对称操作数组\n", "    irreps: tuple              # 不可约表示矩阵列表\n", "    layers: int                # 层数 \n", "    features: tuple            # 每层特征数\n", "    characters: Has<PERSON>leArray  # 指定所需对称表示的字符\n", "    attention_layers: int = 2  # 注意力层数量\n", "    num_heads: int = 4         # 注意力头数\n", "    head_dim: int = 32         # 每个头的维度\n", "    parity: int = 1            # 宇称值\n", "    param_dtype: Any = np.complex128  # 参数数据类型\n", "    input_mask: Any = None     # 输入掩码\n", "    equal_amplitudes: bool = False  # 是否强制等幅\n", "    use_bias: bool = True      # 是否使用偏置\n", "\n", "    def setup(self):\n", "        # 第一层：对称化线性变换\n", "        self.dense_symm = DenseSymmMatrix(\n", "            symmetries=self.symmetries,\n", "            features=self.features[0],\n", "            param_dtype=self.param_dtype,\n", "            use_bias=self.use_bias,\n", "            kernel_init=default_gcnn_initializer,\n", "            bias_init=zeros,\n", "            mask=self.input_mask,\n", "        )\n", "        \n", "        # GCNN 等变层\n", "        self.equivariant_layers = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.layers - 1)\n", "        )\n", "        \n", "        self.equivariant_layers_flip = tuple(\n", "            DenseEquivariantIrrep(\n", "                irreps=self.irreps,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                kernel_init=default_gcnn_initializer,\n", "                bias_init=zeros,\n", "            )\n", "            for layer in range(self.layers - 1)\n", "        )\n", "        \n", "        # 注意力模块 – 仅在最后几层中使用\n", "        self.attention_modules = tuple(\n", "            MultiHeadAttention(\n", "                features=self.features[layer + 1],\n", "                num_heads=self.num_heads,\n", "                head_dim=self.head_dim,\n", "                irreps=self.irreps,\n", "                symmetries=self.symmetries,\n", "                param_dtype=self.param_dtype,\n", "            ) if layer >= (self.layers - 1 - self.attention_layers) else None\n", "            for layer in range(self.layers - 1)\n", "        )\n", "        \n", "        # 层归一化\n", "        self.layer_norms = tuple(\n", "            nn.LayerNorm(epsilon=1e-5, param_dtype=self.param_dtype)\n", "            for _ in range(self.layers - 1)\n", "        )\n", "\n", "    @nn.compact\n", "    def __call__(self, x, training=True):\n", "        if x.ndim < 3:\n", "            x = jnp.expand_dims(x, -2)  # 添加特征维度\n", "\n", "        # 分离正反两条路径\n", "        x_flip = self.dense_symm(-1 * x)\n", "        x = self.dense_symm(x)\n", "\n", "        for layer in range(self.layers - 1):\n", "            x = reim_selu(x)\n", "            x_flip = reim_selu(x_flip)\n", "\n", "            # 保存残差\n", "            residual_x = x\n", "            residual_x_flip = x_flip\n", "            \n", "            # 卷积路径\n", "            x_conv = (\n", "                self.equivariant_layers[layer](x)\n", "                + self.equivariant_layers_flip[layer](x_flip)\n", "            ) / jnp.sqrt(2)\n", "            x_flip_conv = (\n", "                self.equivariant_layers[layer](x_flip)\n", "                + self.equivariant_layers_flip[layer](x)\n", "            ) / jnp.sqrt(2)\n", "            \n", "            x, x_flip = x_conv, x_flip_conv\n", "            \n", "            # 在末端层应用注意力机制\n", "            if layer >= (self.layers - 1 - self.attention_layers):\n", "                x_attn = self.attention_modules[layer](x, training=training)\n", "                x = x + 0.5 * x_attn\n", "                # 层归一化\n", "                x = self.layer_norms[layer](x)\n", "                x_flip = self.layer_norms[layer](x_flip)\n", "                # 残差连接\n", "                x = x + residual_x\n", "                x_flip = x_flip + residual_x_flip\n", "            \n", "        # 拼接正反两条路径\n", "        x = jnp.concatenate((x, x_flip), -1)\n", "        # 构造宇称为1的字符并应用复值 logsumexp\n", "        par_chars = jnp.expand_dims(\n", "            jnp.concatenate(\n", "                (jnp.array(self.characters), jnp.array(self.characters)), 0\n", "            ),\n", "            (0, 1),\n", "        )\n", "        x = logsumexp_cplx(x, axis=(-2, -1), b=par_chars)\n", "        return x"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def create_gcnn_attention(\n", "    symmetries, \n", "    layers, \n", "    features, \n", "    attention_layers=2,\n", "    num_heads=4,\n", "    head_dim=32,\n", "    mask=None, \n", "    characters=None\n", "):\n", "    \"\"\"简化创建带注意力机制 GCNN 模型的函数（无dropout）\"\"\"\n", "    if isinstance(features, int):\n", "        features = (features,) * layers\n", "    if characters is None:\n", "        characters = HashableArray(np.ones(len(np.asarray(symmetries))))\n", "    else:\n", "        characters = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(characters)\n", "    \n", "    sym = HashableArray(np.asarray(symmetries))\n", "    irreps = tuple(HashableArray(irrep) for irrep in symmetries.irrep_matrices())\n", "    input_mask = HashableArray(mask) if mask is not None else None\n", "    \n", "    return GCNN_Attention_Irrep(\n", "        symmetries=sym,\n", "        irreps=irreps,\n", "        layers=layers,\n", "        features=features,\n", "        characters=characters,\n", "        attention_layers=attention_layers,\n", "        num_heads=num_heads,\n", "        head_dim=head_dim,\n", "        parity=1,\n", "        param_dtype=np.complex128,\n", "        input_mask=input_mask,\n", "        equal_amplitudes=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 哈密顿量参数\n", "J1 = 0.03\n", "J2 = 0.05\n", "Q = 1-J2  # 四自旋相互作用强度，替换h项\n", "\n", "# Shastry-Sutherland晶格定义\n", "Lx = 5\n", "Ly = 5\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "# lattice.draw()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hilbert空间定义\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# Q项需要的C_ij算子定义\n", "ProjOp = jnp.array(SiSj) - 0.25 * jnp.array(umum)\n", "ProjOp2 = jnp.kron(ProjOp, ProjOp)\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hilbert, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hilbert, dtype=jnp.complex128)\n", "\n", "# 获取晶格尺寸\n", "Lx, Ly = lattice.extent[0], lattice.extent[1]\n", "\n", "# 遍历所有单元格\n", "for x in range(Lx):\n", "    for y in range(Ly):\n", "        # 计算当前单元格的基本索引\n", "        base = 4 * (y + x * Ly)\n", "        \n", "        # 当前单元格内的四个格点\n", "        site0 = base      # 左下角 (0.5, 0.5)\n", "        site1 = base + 1  # 右下角 (1.5, 0.5)\n", "        site2 = base + 2  # 右上角 (1.5, 1.5)\n", "        site3 = base + 3  # 左上角 (0.5, 1.5)\n", "        \n", "        # 找到相邻单元格（考虑周期性边界条件）\n", "        right_x = (x + 1) % Lx\n", "        right_base = 4 * (y + right_x * Ly)\n", "        \n", "        left_x = (x - 1 + Lx) % Lx\n", "        left_base = 4 * (y + left_x * Ly)\n", "        \n", "        up_y = (y + 1) % Ly\n", "        up_base = 4 * (up_y + x * Ly)\n", "        \n", "        down_y = (y - 1 + Ly) % Ly\n", "        down_base = 4 * (down_y + x * Ly)\n", "        \n", "        # 1. 单元格内部的水平方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site1, site3, site2]])\n", "        \n", "        # 2. 单元格内部的垂直方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site3, site1, site2]])\n", "        \n", "        # 3. 与右侧单元格形成的水平plaquette（处理x方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site1, right_base, site2, right_base + 3]])\n", "        \n", "        # 4. 与上方单元格形成的垂直plaquette（处理y方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site3, up_base, site2, up_base + 1]])\n", "\n", "# 合并两部分哈密顿量\n", "ha = H_J + 2*H_Q\n", "ha = ha.to_jax_operator()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of symmetry operations: 72\n", "Number of model parameters: 16472\n"]}], "source": ["# 定义对称群（以 C4v 为例）并获取 irreps\n", "nc = 4\n", "cyclic_4 = PointGroup(\n", "    [Identity()] + [rotation((360 / nc)*i) for i in range(1, nc)],\n", "    ndim=2,\n", ")\n", "C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4\n", "C4v_symmetry = lattice.space_group(C4v)\n", "print(f\"Number of symmetry operations: {len(C4v_symmetry)}\")\n", "\n", "# 构造局部簇及掩码\n", "local_cluster = jnp.arange(lattice.n_nodes).tolist()\n", "mask = jnp.zeros(lattice.n_nodes, dtype=bool)\n", "for i in local_cluster:\n", "    mask = mask.at[i].set(True)\n", "\n", "# 获取 irreps (此处示例采用 netket 内置方法获得 irreps)\n", "sgb = lattice.space_group_builder(point_group=C4v)\n", "momentum = [0.0, 0.0]\n", "chi = sgb.space_group_irreps(momentum)[0]\n", "\n", "# 构建模型（注意：attention_layers, num_heads, head_dim 参数可根据实际需求调整）\n", "model = create_gcnn_attention(\n", "    symmetries=C4v_symmetry,\n", "    layers=4,\n", "    features=4,\n", "    attention_layers=1,  # 使用 1 个注意力层\n", "    num_heads=2,\n", "    head_dim=4,\n", "    mask=mask,\n", "    characters=chi\n", ")\n", "\n", "# 定义采样器与变分量子态\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hilbert, graph=lattice, n_chains=2**12, d_max=2)\n", "vqs = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model,\n", "    n_samples=2**12,\n", "    n_samples_per_rank=None,\n", "    n_discard_per_chain=0,\n", "    chunk_size=2**8,\n", "    training_kwargs={\"holomorphic\": False}  # 非全纯模型\n", ")\n", "\n", "n_params = nk.jax.tree_size(vqs.parameters)\n", "print(f\"Number of model parameters: {n_params}\")  # 应接近预期参数量"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7727ee6ea9634a00bdd6a8c75bfbc3de", "version_major": 2, "version_minor": 0}, "text/plain": ["Lx=3, Ly=3:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization time: 1557.45 seconds\n"]}], "source": ["import jax\n", "from jax import tree_util\n", "\n", "# 熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 定义自由能优化驱动，继承 nk.experimental.driver.vmc_srt.VMC_SRt\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.init_temperature = temperature  # 初始温度\n", "        self.temperature = temperature\n", "        self.max_grad_norm = 0.7  # 最大梯度范数\n", "\n", "    def _step_with_state(self, state):\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "\n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = jax.tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "\n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = jax.tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "\n", "        # 梯度裁剪\n", "        total_grad = tree_util.tree_map(lambda g: jnp.clip(g, -self.clip_norm, self.clip_norm), total_grad)\n", "\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "# 带进度条与温度递减策略的训练驱动\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 温度递减\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0))\n", "            self.advance(1)\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}',\n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "\n", "# 初始化优化器和训练驱动\n", "temperature_original = 1.0  # 初始温度\n", "reference_energy = -16.2631\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.02)   # 或使用 Adam\n", "\n", "vmc = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    temperature=temperature_original,\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=0.05,\n", "    variational_state=vqs\n", ")\n", "\n", "start = time.time()\n", "vmc.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"Optimization time: {end - start:.2f} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}