{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： True\n", "可用设备： [CudaDevice(id=0), CudaDevice(id=1), CudaDevice(id=2), CudaDevice(id=3)]\n"]}], "source": ["# %%\n", "import os\n", "import jax\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '1'\n", "\n", "import netket as nk\n", "import jax\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# %%\n", "import os\n", "import logging\n", "import sys\n", "import jax\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from functools import partial\n", "from netket.nn import log_cosh\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator\n", "from netket.vqs import VariationalState\n", "from scipy import sparse as _sparse\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.operator import LocalOperator as _LocalOperator\n", "from tqdm.notebook import tqdm\n", "from jax import tree"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import jax\n", "import jax.numpy as jnp\n", "from flax import linen as nn\n", "from functools import partial\n", "from einops import rearrange\n", "\n", "class FMHA(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L_eff: int\n", "\n", "    def setup(self):\n", "        self.v = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                          param_dtype=jnp.float64, dtype=jnp.float64)\n", "        self.J = self.param(\"J\", nn.initializers.xavier_uniform(), (self.h, self.L_eff, self.L_eff), jnp.float64)\n", "\n", "        self.W = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                          param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "    def __call__(self, x):\n", "        v = self.v(x)\n", "        v = rearrange(v, 'batch L_eff (h d_eff) -> batch L_eff h d_eff', h=self.h)\n", "        v = rearrange(v, 'batch L_eff h d_eff -> batch h L_eff d_eff')\n", "        x = jnp.matmul(self.J, v)\n", "        x = rearrange(x, 'batch h L_eff d_eff -> batch L_eff h d_eff')\n", "        x = rearrange(x, 'batch L_eff h d_eff -> batch L_eff (h d_eff)')\n", "        x = self.W(x)\n", "        return x\n", "\n", "\n", "def extract_patches2d(x, b):\n", "    batch = x.shape[0]\n", "    sq_L_eff = int((x.shape[1] // b**2)**0.5)\n", "    x = x.reshape(batch, sq_L_eff, b, sq_L_eff, b)   # [batch, L_eff, b, L_eff, b]\n", "    x = x.transpose(0, 1, 3, 2, 4)              # [batch, L_eff, L_eff, b, b]\n", "    x = x.reshape(batch, sq_L_eff, sq_L_eff, -1)        # flatten patches: [batch, L_eff, L_eff, b*b]\n", "    x = x.reshape(batch, sq_L_eff*sq_L_eff, -1)         # [batch, L_eff*L_eff, b*b]\n", "    return x\n", "\n", "def log_cosh(x):\n", "    sgn_x = -2 * jnp.signbit(x.real) + 1\n", "    x = x * sgn_x\n", "    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)\n", "\n", "class Encoder(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L_eff: int\n", "\n", "    def setup(self):\n", "        self.attn = FMHA(d_model=self.d_model, h=self.h, L_eff=self.L_eff)\n", "        self.layer_norm_1 = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.layer_norm_2 = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.ff = nn.Sequential([\n", "            nn.Dense(4 * self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                     param_dtype=jnp.float64, dtype=jnp.float64),\n", "            nn.gelu,\n", "            nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                     param_dtype=jnp.float64, dtype=jnp.float64),\n", "        ])\n", "\n", "    def __call__(self, x):\n", "        x = x + self.attn(self.layer_norm_1(x))\n", "        x = x + self.ff(self.layer_norm_2(x))\n", "        return x\n", "\n", "\n", "class OutputHead(nn.Module):\n", "    d_model: int\n", "\n", "    def setup(self):\n", "        self.out_layer_norm = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.norm0 = nn.LayerNorm(use_scale=True, use_bias=True, dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.norm1 = nn.LayerNorm(use_scale=True, use_bias=True, dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.output_layer0 = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(), bias_init=jax.nn.initializers.zeros,\n", "                                      param_dtype=jnp.float64, dtype=jnp.float64)\n", "        self.output_layer1 = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(), bias_init=jax.nn.initializers.zeros,\n", "                                      param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "    def __call__(self, x):\n", "        z = self.out_layer_norm(x.sum(axis=1))\n", "        amp = self.norm0(self.output_layer0(z))\n", "        sign = self.norm1(self.output_layer1(z))\n", "        out = amp + 1j * sign\n", "\n", "        return jnp.sum(log_cosh(out), axis=-1)\n", "\n", "class ViTFNQS(nn.Module):\n", "    num_layers: int\n", "    d_model: int\n", "    heads: int\n", "    n_sites: int         # 总格点数\n", "    patch_size: int      # patch 尺寸\n", "\n", "    def setup(self):\n", "        self.L_eff = self.n_sites // (self.patch_size ** 2)\n", "        self.embed = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                              param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "        self.blocks = [Encoder(\n", "            d_model=self.d_model,\n", "            h=self.heads,\n", "            L_eff=self.L_eff\n", "        )for _ in range(self.num_layers)]\n", "        self.output = OutputHead(self.d_model)\n", "\n", "    def __call__(self, spins):\n", "        x = jnp.atleast_2d(spins)\n", "        x = extract_patches2d(x, self.patch_size)\n", "\n", "        # 直接进入嵌入和编码器模块\n", "        x = self.embed(x)\n", "        for block in self.blocks:\n", "            x = block(x)\n", "        out = self.output(x)\n", "        return out\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 哈密顿量参数\n", "J1 = 0.03\n", "J2 = 0.0\n", "Q = 1.0  # 四自旋相互作用强度，替换h项\n", "\n", "# Shastry-Sutherland晶格定义\n", "Lx = 4\n", "Ly = 4\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "lattice.draw()\n", "\n", "# %%\n", "# Hilbert空间定义\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# 定义(Si·Sj - 1/4)算符\n", "ProjOp = jnp.array(SiSj) - 0.25 * jnp.array(umum)\n", "ProjOp2 = jnp.kron(ProjOp, ProjOp)\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hilbert, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hilbert, dtype=jnp.complex128)\n", "\n", "# 获取晶格尺寸\n", "Lx, Ly = lattice.extent[0], lattice.extent[1]\n", "\n", "# 遍历所有单元格\n", "for x in range(Lx):\n", "    for y in range(Ly):\n", "        # 计算当前单元格的基本索引\n", "        base = 4 * (y + x * Ly)\n", "        \n", "        # 当前单元格内的四个格点\n", "        site0 = base      # 左下角 (0.5, 0.5)\n", "        site1 = base + 1  # 右下角 (1.5, 0.5)\n", "        site2 = base + 2  # 右上角 (1.5, 1.5)\n", "        site3 = base + 3  # 左上角 (0.5, 1.5)\n", "        \n", "        # 找到相邻单元格（考虑周期性边界条件）\n", "        right_x = (x + 1) % Lx\n", "        right_base = 4 * (y + right_x * Ly)\n", "        \n", "        left_x = (x - 1 + Lx) % Lx\n", "        left_base = 4 * (y + left_x * Ly)\n", "        \n", "        up_y = (y + 1) % Ly\n", "        up_base = 4 * (up_y + x * Ly)\n", "        \n", "        down_y = (y - 1 + Ly) % Ly\n", "        down_base = 4 * (down_y + x * Ly)\n", "        \n", "        # 1. 单元格内部的水平方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site1, site3, site2]])\n", "        \n", "        # 2. 单元格内部的垂直方向plaquette\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site0, site3, site1, site2]])\n", "        \n", "        # 3. 与右侧单元格形成的水平plaquette（处理x方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site1, right_base, site2, right_base + 3]])\n", "        \n", "        # 4. 与上方单元格形成的垂直plaquette（处理y方向周期性）\n", "        H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "                                        [[site3, up_base, site2, up_base + 1]])\n", "        \n", "        # # 5. 与左侧单元格形成的水平plaquette（处理x方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, left_base + 1, site3, left_base + 2]])\n", "        \n", "        # # 6. 与下方单元格形成的垂直plaquette（处理y方向周期性）\n", "        # H_Q += nk.operator.LocalOperator(hilbert, [(-Q * ProjOp2).tolist()],\n", "        #                              [[site0, down_base + 3, site1, down_base + 2]])\n", "\n", "# 合并两部分哈密顿量\n", "hamiltonian = H_J + 2*H_Q\n", "hamiltonian = hamiltonian.to_jax_operator()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["对称性操作数量: 128\n"]}], "source": ["# %%\n", "N_layers =  4         # 编码器层数\n", "d_model = 48          # 特征维度\n", "n_heads = 12           # 注意力头数\n", "mlp_dim = d_model*2     # MLP维度\n", "patch_size = 2        # patch大小\n", "n_samples = 2**12     # 样本数量\n", "chunk_size = 2**8    # 批处理大小\n", "\n", "# 创建未对称化的ViT量子态模型\n", "model_no_symm = ViTFNQS(\n", "    num_layers=N_layers,\n", "    d_model=d_model,\n", "    heads=n_heads,\n", "    n_sites=lattice.n_nodes,\n", "    patch_size=patch_size\n", ")\n", "\n", "\n", "nc = 4\n", "cyclic_4 = PointGroup(\n", "    [Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],\n", "    ndim=2,\n", ")\n", "\n", "C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4\n", "symmetries = lattice.space_group(C4v)\n", "print(f\"对称性操作数量: {len(symmetries)}\")\n", "\n", "\n", "# 使用SymmExpSum对模型进行对称化\n", "from netket.nn.blocks import SymmExpSum\n", "model = SymmExpSum(\n", "    module=model_no_symm, \n", "    symm_group=symmetries, \n", "    character_id=None\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import netket.optimizer as nk_opt\n", "\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hilbert, graph=lattice, n_chains=n_samples, d_max=2)\n", "optimizer = nk_opt.Sgd(learning_rate=0.05)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型参数数量: 111792\n"]}], "source": ["# %%\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "from netket.callbacks import EarlyStopping\n", "\n", "\n", "# 初始化变分量子态\n", "vqs = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model,\n", "    n_samples=n_samples,\n", "    n_samples_per_rank=None,\n", "    n_discard_per_chain=0,\n", "    chunk_size=chunk_size,\n", "    training_kwargs={\"holomorphic\": False}  # 非全纯函数\n", ")\n", "\n", "n_params = nk.jax.tree_size(vqs.parameters)\n", "print(f\"模型参数数量: {n_params}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cfe3f617d26043f68e4863f817e57312", "version_major": 2, "version_minor": 0}, "text/plain": ["Lx=4, Ly=4:   0%|          | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import tree_util\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 定义熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # 记录初始温度，用于后续温度递减计算\n", "        self.init_temperature = temperature\n", "        self.temperature = temperature\n", "\n", "    def _step_with_state(self, state):\n", "        # 基础能量梯度更新步骤\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "        \n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "        \n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "        \n", "        # 更新参数\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "# 添加进度条以及温度递减方案\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"运行优化并在 tqdm 进度条中显示 Temperature，Energy，E_var，E_err 和 Rel_err(%)\"\"\"\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 更新温度：使用初始温度乘以递减因子\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0) / 2.0)\n", "            self.advance(1)\n", "\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}', \n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self\n", "\n", "\n", "temperature_original = 1.0  # 初始温度\n", "reference_energy = -16.2618\n", "\n", "vmc = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    temperature=temperature_original,\n", "    hamiltonian=hamiltonian,\n", "    optimizer=optimizer,\n", "    diag_shift=0.01,\n", "    variational_state=vqs\n", ")\n", "\n", "start = time.time()\n", "vmc.run(n_iter=1000)\n", "end = time.time()\n", "print(f\"优化耗时: {end - start:.2f}秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}