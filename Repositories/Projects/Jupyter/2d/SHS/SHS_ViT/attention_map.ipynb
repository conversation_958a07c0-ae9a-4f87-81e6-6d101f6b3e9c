{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"umap\")\n", "import umap.umap_ as umap\n", "UMAP = umap.UMAP\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import jax\n", "import jax.numpy as jnp\n", "from sklearn.cluster import KMeans\n", "\n", "# 简化的隐藏表示提取函数\n", "def extract_hidden_representations(model, params, samples):\n", "    \"\"\"提取样本的隐藏表示\"\"\"\n", "    # 获取模型应用函数\n", "    def get_embed(x):\n", "        # 在实际代码中处理一个样本\n", "        if model.two_dimensional:\n", "            patches = extract_patches2d(x.reshape(1, -1), model.patch_size)[0]\n", "        else:\n", "            patches = extract_patches1d(x.reshape(1, -1), model.patch_size)[0]\n", "        \n", "        # 对于每个patch应用嵌入层\n", "        patch_embeddings = jnp.matmul(patches, params[\"embed\"][\"kernel\"]) + params[\"embed\"][\"bias\"]\n", "        return jnp.mean(patch_embeddings, axis=0)\n", "    \n", "    # 对每个样本应用函数\n", "    features = np.array([jax.device_get(get_embed(s)) for s in samples])\n", "    return features\n", "\n", "# 生成可视化\n", "def visualize_hidden_representations(model, params, samples):\n", "    \"\"\"创建隐藏表示的可视化，确保颜色条正确放置\"\"\"\n", "    # 生成随机参数(初始状态的模拟)\n", "    rng_key = jax.random.PRNG<PERSON>ey(42)\n", "    random_params = jax.tree_util.tree_map(\n", "        lambda x: jax.random.normal(rng_key, x.shape, x.dtype),\n", "        params\n", "    )\n", "    \n", "    # 提取隐藏表示\n", "    print(\"提取训练后的隐藏表示...\")\n", "    features_trained = extract_hidden_representations(model, params, samples)\n", "    print(f\"训练后特征形状: {features_trained.shape}\")\n", "    \n", "    print(\"提取随机初始化的隐藏表示...\")\n", "    features_random = extract_hidden_representations(model, random_params, samples)\n", "    print(f\"随机特征形状: {features_random.shape}\")\n", "    \n", "    # 使用UMAP进行降维\n", "    print(\"对随机特征应用UMAP降维...\")\n", "    reducer_random = UMAP(random_state=42)\n", "    embedding_random = reducer_random.fit_transform(features_random)\n", "    \n", "    print(\"对训练后特征应用UMAP降维...\")\n", "    reducer_trained = UMAP(random_state=42)\n", "    embedding_trained = reducer_trained.fit_transform(features_trained)\n", "    \n", "    # 使用第一个UMAP维度作为着色的代理\n", "    color_values = embedding_trained[:, 0]\n", "    normalized_colors = (color_values - np.min(color_values)) / (np.max(color_values) - np.min(color_values))\n", "    \n", "    # 创建一个新的图，设置合适的宽高比以容纳colorbar\n", "    fig = plt.figure(figsize=(16, 6))\n", "    \n", "    # 创建子图，并为colorbar留出空间\n", "    ax1 = fig.add_subplot(121)  # 1行2列的第1个\n", "    ax2 = fig.add_subplot(122)  # 1行2列的第2个\n", "    \n", "    # 图10(a): 随机初始化参数的隐藏表示\n", "    scatter1 = ax1.scatter(embedding_random[:, 0], embedding_random[:, 1], \n", "                         c=normalized_colors, cmap='viridis', alpha=0.8, s=20)\n", "    ax1.set_title('Random Initialization', fontsize=14)\n", "    ax1.set_xlabel('UMAP dimension 1', fontsize=12)\n", "    ax1.set_ylabel('UMAP dimension 2', fontsize=12)\n", "    \n", "    # 图10(b): 优化后参数的隐藏表示\n", "    scatter2 = ax2.scatter(embedding_trained[:, 0], embedding_trained[:, 1], \n", "                         c=normalized_colors, cmap='viridis', alpha=0.8, s=20)\n", "    ax2.set_title('After Optimization', fontsize=14)\n", "    ax2.set_xlabel('UMAP dimension 1', fontsize=12)\n", "    ax2.set_ylabel('UMAP dimension 2', fontsize=12)\n", "    \n", "    # 添加颜色条在图的右侧\n", "    cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])  # [left, bottom, width, height]\n", "    cbar = fig.colorbar(scatter2, cax=cbar_ax)\n", "    cbar.set_label('Normalized Feature Value', rotation=270, labelpad=15)\n", "    \n", "    # 调整布局，确保所有元素可见\n", "    fig.tight_layout(rect=[0, 0, 0.9, 1])  # [left, bottom, right, top]\n", "    \n", "    return fig\n", "\n", "# 可视化注意力图\n", "def visualize_attention_maps(params, n_layers=2, n_heads=12):\n", "    \"\"\"可视化Transformer的注意力映射\"\"\"\n", "    # 提取注意力权重\n", "    attention_maps = []\n", "    \n", "    for l in range(n_layers):\n", "        # 获取当前层的注意力权重\n", "        J = params[\"encoder\"][f\"layers_{l}\"][\"attn\"][\"J\"]\n", "        attention_maps.append(J)\n", "    \n", "    # 转换为numpy数组以便绘图\n", "    attention_maps = np.array(jax.device_get(attention_maps))  # shape: [n_layers, n_heads, L_eff]\n", "    print(f\"注意力图形状: {attention_maps.shape}\")\n", "    \n", "    # 创建图11(a)：各层各头的注意力图\n", "    fig, axs = plt.subplots(n_layers, n_heads, figsize=(20, 5*n_layers))\n", "    if n_layers == 1:\n", "        axs = np.array([axs])\n", "    \n", "    for l in range(n_layers):\n", "        for h in range(n_heads):\n", "            # 直接显示1D注意力权重\n", "            attention_weights = attention_maps[l, h]\n", "            \n", "            # 尝试重塑为方形网格进行可视化\n", "            side = int(np.sqrt(attention_weights.shape[0]))\n", "            if side * side == attention_weights.shape[0]:\n", "                attention_grid = attention_weights.reshape(side, side)\n", "                im = axs[l, h].imshow(attention_grid, cmap='viridis')\n", "            else:\n", "                # 如果无法重塑为方形，显示为线性图\n", "                axs[l, h].bar(range(len(attention_weights)), attention_weights)\n", "                \n", "            axs[l, h].set_title(f'Layer {l+1}, Head {h+1}')\n", "            axs[l, h].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 创建图11(b)：所有注意力图绝对值的平均\n", "    mean_attention = np.mean(np.abs(attention_maps), axis=(0, 1))  # shape: [L_eff]\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    \n", "    # 尝试将均值注意力重塑为方形网格\n", "    side = int(np.sqrt(mean_attention.shape[0]))\n", "    if side * side == mean_attention.shape[0]:\n", "        mean_attention_grid = mean_attention.reshape(side, side)\n", "        im = plt.imshow(mean_attention_grid, cmap='viridis')\n", "        plt.title('Mean Attention Map', fontsize=14)\n", "        plt.axis('off')\n", "    else:\n", "        # 如果无法重塑为方形，显示为线性图\n", "        plt.bar(range(len(mean_attention)), mean_attention)\n", "        plt.title('Mean Attention Weights', fontsize=14)\n", "        plt.xlabel('Position', fontsize=12)\n", "        plt.ylabel('Mean Absolute Value', fontsize=12)\n", "    \n", "    plt.colorbar(im if 'im' in locals() else None, label='Mean Absolute Attention')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 聚类分析\n", "def analyze_hidden_clusters(model, params, samples, n_clusters=5):\n", "    \"\"\"分析隐藏表示中的聚类结构\"\"\"\n", "    from sklearn.cluster import KMeans\n", "    \n", "    # 提取隐藏表示\n", "    features = extract_hidden_representations(model, params, samples)\n", "    \n", "    # 使用K-means进行聚类\n", "    kmeans = KMeans(n_clusters=n_clusters, random_state=42)\n", "    clusters = kmeans.fit_predict(features)\n", "    \n", "    # 使用UMAP降维可视化\n", "    reducer = UMAP(random_state=42)\n", "    embedding = reducer.fit_transform(features)\n", "    \n", "    # 可视化聚类\n", "    plt.figure(figsize=(10, 8))\n", "    scatter = plt.scatter(embedding[:, 0], embedding[:, 1], c=clusters, cmap='tab10', s=30, alpha=0.8)\n", "    plt.colorbar(scatter, label='Cluster ID')\n", "    plt.xlabel('UMAP dimension 1', fontsize=12)\n", "    plt.ylabel('UMAP dimension 2', fontsize=12)\n", "    plt.title('Hidden Representation Clusters', fontsize=14)\n", "    \n", "    # 打印聚类统计信息\n", "    print(\"Cluster Statistics:\")\n", "    for i in range(n_clusters):\n", "        mask = (clusters == i)\n", "        cluster_size = np.sum(mask)\n", "        print(f\"Cluster {i}: {cluster_size} samples\")\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return clusters\n", "\n", "# 执行可视化\n", "try:\n", "    # 生成样本\n", "    samples = vqs.sample(n_samples=1000)\n", "    \n", "    # 创建隐藏表示的可视化\n", "    print(\"正在创建隐藏表示可视化...\")\n", "    fig = visualize_hidden_representations(model_no_symm, vqs.parameters, samples)\n", "    plt.savefig('figure10_hidden_representations.png', dpi=300)\n", "    plt.show()\n", "    \n", "    # 可视化注意力图\n", "    print(\"正在创建注意力图可视化...\")\n", "    visualize_attention_maps(vqs.parameters, n_layers=N_layers, n_heads=n_heads)\n", "    \n", "    # 执行聚类分析\n", "    print(\"正在进行隐藏表示聚类分析...\")\n", "    clusters = analyze_hidden_clusters(model_no_symm, vqs.parameters, samples, n_clusters=5)\n", "    \n", "except Exception as e:\n", "    print(f\"错误: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}