{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wed Apr  2 05:07:22 2025       \n", "+---------------------------------------------------------------------------------------+\n", "| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |\n", "|-----------------------------------------+----------------------+----------------------+\n", "| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |\n", "|                                         |                      |               MIG M. |\n", "|=========================================+======================+======================|\n", "|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:03:00.0 Off |                    0 |\n", "| N/A   53C    P0             247W / 400W |   9016MiB / 40960MiB |    100%      Default |\n", "|                                         |                      |             Disabled |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   1  NVIDIA A100-SXM4-40GB          On  | 00000000:81:00.0 Off |                    0 |\n", "| N/A   54C    P0             208W / 400W |   9014MiB / 40960MiB |    100%      Default |\n", "|                                         |                      |             Disabled |\n", "+-----------------------------------------+----------------------+----------------------+\n", "                                                                                         \n", "+---------------------------------------------------------------------------------------+\n", "| Processes:                                                                            |\n", "|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |\n", "|        ID   ID                                                             Usage      |\n", "|=======================================================================================|\n", "|    0   N/A  N/A    745621      C   /usr/bin/python                            9008MiB |\n", "|    1   N/A  N/A    745621      C   /usr/bin/python                            9006MiB |\n", "+---------------------------------------------------------------------------------------+\n"]}], "source": ["# !kill -9 - 621098\n", "!nvidia-smi"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["启用分片模式： True\n", "可用设备： [CudaDevice(id=0), CudaDevice(id=1), CudaDevice(id=2), CudaDevice(id=3)]\n"]}], "source": ["# %%\n", "import os\n", "import jax\n", "\n", "# 设置环境变量\n", "os.environ[\"XLA_FLAGS\"] = \"--xla_gpu_cuda_data_dir=/usr/local/cuda\"\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"gpu\"\n", "os.environ[\"XLA_PYTHON_CLIENT_PREALLOCATE\"] = \"false\"\n", "\n", "import os\n", "os.environ['NETKET_EXPERIMENTAL_SHARDING'] = '1'\n", "\n", "import netket as nk\n", "import jax\n", "print(\"启用分片模式：\", nk.config.netket_experimental_sharding)\n", "print(\"可用设备：\", jax.devices())"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# %%\n", "import os\n", "import logging\n", "import sys\n", "import jax\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "import netket.nn as nknn\n", "import flax\n", "import flax.linen as nn\n", "import jax.numpy as jnp\n", "import math\n", "from math import pi\n", "from functools import partial\n", "from netket.nn import log_cosh\n", "from einops import rearrange\n", "from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group, C\n", "from netket.utils.group import PointGroup, Identity, PermutationGroup\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from netket.optimizer.qgt import QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly\n", "from netket.operator import AbstractOperator\n", "from netket.vqs import VariationalState\n", "from scipy import sparse as _sparse\n", "from netket.utils.types import DType as _DType\n", "from netket.hilbert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as _DiscreteHilbert\n", "from netket.operator import LocalOperator as _LocalOperator\n", "from tqdm.notebook import tqdm\n", "from jax import tree\n", "from netket.nn.blocks import SymmExpSum"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import jax\n", "import jax.numpy as jnp\n", "from flax import linen as nn\n", "from functools import partial\n", "from einops import rearrange\n", "\n", "def roll(J, shift, axis=-1):\n", "    return jnp.roll(J, shift, axis=axis)\n", "\n", "@partial(jax.vmap, in_axes=(None, 0, None), out_axes=1)\n", "@partial(jax.vmap, in_axes=(None, None, 0), out_axes=1)\n", "def roll2d(spins, i, j):\n", "    side = int(spins.shape[-1] ** 0.5)\n", "    spins = spins.reshape(spins.shape[0], side, side)\n", "    spins = jnp.roll(jnp.roll(spins, i, axis=-2), j, axis=-1)\n", "    return spins.reshape(spins.shape[0], -1)\n", "\n", "class FMHA(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L_eff: int\n", "    transl_invariant: bool = True\n", "    two_dimensional: bool = False\n", "\n", "    def setup(self):\n", "        self.v = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                          param_dtype=jnp.float64, dtype=jnp.float64)\n", "        if self.transl_invariant:\n", "            self.J = self.param(\"J\", nn.initializers.xavier_uniform(), (self.h, self.L_eff), jnp.float64)\n", "            if self.two_dimensional:\n", "                sq_L_eff = int(self.L_eff ** 0.5)\n", "                assert sq_L_eff * sq_L_eff == self.L_eff\n", "                self.J = roll2d(self.J, jnp.arange(sq_L_eff), jnp.arange(sq_L_eff))\n", "                self.J = self.<PERSON>.reshape(self.h, -1, self.L_eff)\n", "            else:\n", "                self.J = jax.vmap(roll, (None, 0), out_axes=1)(self.J, jnp.arange(self.L_eff))\n", "        else:\n", "            self.J = self.param(\"J\", nn.initializers.xavier_uniform(), (self.h, self.L_eff, self.L_eff), jnp.float64)\n", "\n", "        self.W = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                          param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "    def __call__(self, x):\n", "        v = self.v(x)\n", "        v = rearrange(v, 'batch L_eff (h d_eff) -> batch L_eff h d_eff', h=self.h)\n", "        v = rearrange(v, 'batch L_eff h d_eff -> batch h L_eff d_eff')\n", "        x = jnp.matmul(self.J, v)\n", "        x = rearrange(x, 'batch h L_eff d_eff -> batch L_eff h d_eff')\n", "        x = rearrange(x, 'batch L_eff h d_eff -> batch L_eff (h d_eff)')\n", "        x = self.W(x)\n", "        return x\n", "\n", "def extract_patches1d(x, b):\n", "    return rearrange(x, 'batch (L_eff b) -> batch L_eff b', b=b)\n", "\n", "def extract_patches2d(x, b):\n", "    batch = x.shape[0]\n", "    sq_L_eff = int((x.shape[1] // b**2)**0.5)\n", "    x = x.reshape(batch, sq_L_eff, b, sq_L_eff, b)   # [batch, L_eff, b, L_eff, b]\n", "    x = x.transpose(0, 1, 3, 2, 4)              # [batch, L_eff, L_eff, b, b]\n", "    x = x.reshape(batch, sq_L_eff, sq_L_eff, -1)        # flatten patches: [batch, L_eff, L_eff, b*b]\n", "    x = x.reshape(batch, sq_L_eff*sq_L_eff, -1)         # [batch, L_eff*L_eff, b*b]\n", "    return x\n", "\n", "def log_cosh(x):\n", "    sgn_x = -2 * jnp.signbit(x.real) + 1\n", "    x = x * sgn_x\n", "    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)\n", "\n", "class EncoderBlock(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L_eff: int\n", "    transl_invariant: bool = True\n", "    two_dimensional: bool = True\n", "\n", "    def setup(self):\n", "        self.attn = FMHA(d_model=self.d_model, h=self.h, L_eff=self.L_eff,\n", "                         transl_invariant=self.transl_invariant, two_dimensional=self.two_dimensional)\n", "        self.layer_norm_1 = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.layer_norm_2 = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.ff = nn.Sequential([\n", "            nn.Dense(4 * self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                     param_dtype=jnp.float64, dtype=jnp.float64),\n", "            nn.gelu,\n", "            nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                     param_dtype=jnp.float64, dtype=jnp.float64),\n", "        ])\n", "\n", "    def __call__(self, x):\n", "        x = x + self.attn(self.layer_norm_1(x))\n", "        x = x + self.ff(self.layer_norm_2(x))\n", "        return x\n", "\n", "class Encoder(nn.Module):\n", "    num_layers: int\n", "    d_model: int\n", "    h: int\n", "    L_eff: int\n", "    transl_invariant: bool = True\n", "    two_dimensional: bool = True\n", "\n", "    def setup(self):\n", "        self.layers = [EncoderBlock(d_model=self.d_model, h=self.h, L_eff=self.L_eff,\n", "                                      transl_invariant=self.transl_invariant, two_dimensional=self.two_dimensional)\n", "                       for _ in range(self.num_layers)]\n", "\n", "    def __call__(self, x):\n", "        for layer in self.layers:\n", "            x = layer(x)\n", "        return x\n", "\n", "class OuputHead(nn.Module):\n", "    d_model: int\n", "    complex: bool = True\n", "\n", "    def setup(self):\n", "        self.out_layer_norm = nn.LayerNorm(dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.norm0 = nn.LayerNorm(use_scale=True, use_bias=True, dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.norm1 = nn.LayerNorm(use_scale=True, use_bias=True, dtype=jnp.float64, param_dtype=jnp.float64)\n", "        self.output_layer0 = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(), bias_init=jax.nn.initializers.zeros,\n", "                                      param_dtype=jnp.float64, dtype=jnp.float64)\n", "        self.output_layer1 = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(), bias_init=jax.nn.initializers.zeros,\n", "                                      param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "    def __call__(self, x):\n", "        z = self.out_layer_norm(x.sum(axis=1))\n", "        amp = self.norm0(self.output_layer0(z))\n", "        if self.complex:\n", "            sign = self.norm1(self.output_layer1(z))\n", "            out = amp + 1j * sign\n", "        else:\n", "            out = amp\n", "        return jnp.sum(log_cosh(out), axis=-1)\n", "\n", "class ViTFNQS(nn.Module):\n", "    num_layers: int\n", "    d_model: int\n", "    heads: int\n", "    n_sites: int         # 总格点数\n", "    patch_size: int      # patch 尺寸\n", "    complex: bool = False\n", "    transl_invariant: bool = True\n", "    two_dimensional: bool = True\n", "\n", "    def setup(self):\n", "        if self.two_dimensional:\n", "            self.L_eff = self.n_sites // (self.patch_size ** 2)\n", "        else:\n", "            self.L_eff = self.n_sites // self.patch_size\n", "\n", "        self.embed = nn.Dense(self.d_model, kernel_init=nn.initializers.xavier_uniform(),\n", "                              param_dtype=jnp.float64, dtype=jnp.float64)\n", "\n", "        self.encoder = Encoder(\n", "            num_layers=self.num_layers,\n", "            d_model=self.d_model,\n", "            h=self.heads,\n", "            L_eff=self.L_eff,\n", "            transl_invariant=self.transl_invariant,\n", "            two_dimensional=self.two_dimensional\n", "        )\n", "        self.output = OuputHead(self.d_model, complex=self.complex)\n", "\n", "    def __call__(self, spins):\n", "        x = jnp.atleast_2d(spins)\n", "        if self.two_dimensional:\n", "            x = extract_patches2d(x, self.patch_size)\n", "        else:\n", "            x = extract_patches1d(x, self.patch_size)\n", "        \n", "        # 直接进入嵌入和编码器模块\n", "        x = self.embed(x)\n", "        x = self.encoder(x)\n", "        out = self.output(x)\n", "        return out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Axes: title={'center': '2D Lattice (Distance Order: 1)'}, xlabel='X', ylabel='Y'>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# %%\n", "# 哈密顿量参数\n", "J1 = 0.8\n", "J2 = 1.0\n", "Q = 0  # 四自旋相互作用强度，替换h项\n", "\n", "# Shastry-Sutherland晶格定义\n", "Lx = 3\n", "Ly = 3\n", "\n", "# 自定义边\n", "custom_edges = [\n", "    (0, 1, [1.0, 0.0], 0),\n", "    (1, 0, [1.0, 0.0], 0),\n", "    (1, 2, [0.0, 1.0], 0),\n", "    (2, 1, [0.0, 1.0], 0),\n", "    (3, 2, [1.0, 0.0], 0),\n", "    (2, 3, [1.0, 0.0], 0),\n", "    (0, 3, [0.0, 1.0], 0),\n", "    (3, 0, [0.0, 1.0], 0),\n", "    (2, 0, [1.0, -1.0], 1),\n", "    (3, 1, [1.0, 1.0], 1),\n", "]\n", "\n", "# 创建晶格\n", "lattice = nk.graph.La<PERSON>ce(\n", "    basis_vectors=[[2.0, 0.0], [0.0, 2.0]],\n", "    extent=(Lx, Ly),\n", "    site_offsets=[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]],\n", "    custom_edges=custom_edges,\n", "    pbc=[True, True]\n", ")\n", "\n", "# 可视化晶格\n", "lattice.draw()\n", "\n", "# %%\n", "# Hilbert空间定义\n", "hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# 自旋-1/2矩阵\n", "sigmax = jnp.array([[0, 0.5], [0.5, 0]])\n", "sigmay = jnp.array([[0, -0.5j], [0.5j, 0]])\n", "sigmaz = jnp.array([[0.5, 0], [0, -0.5]])\n", "unitm = jnp.array([[1.0, 0.0], [0.0, 1.0]])\n", "\n", "# 自旋-自旋相互作用\n", "sxsx = np.kron(sigmax, sigmax)\n", "sysy = np.kron(sigmay, sigmay)\n", "szsz = np.kron(sigmaz, sigmaz)\n", "umum = np.kron(unitm, unitm)\n", "SiSj = sxsx + sysy + szsz\n", "\n", "# Q项需要的C_ij算子定义\n", "Cij = 0.25 * umum - SiSj\n", "Cij2 = np.kron(<PERSON>ij, Cij)  # 四自旋交互项\n", "\n", "# 构建J1-J2部分的哈密顿量\n", "bond_operator = [\n", "    (J1 * SiSj).tolist(),\n", "    (J2 * SiSj).tolist(),\n", "]\n", "bond_color = [0, 1]\n", "\n", "# 创建图哈密顿量 - 不包含Q项\n", "H_J = nk.operator.GraphOperator(hi, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color)\n", "\n", "# 创建Q项哈密顿量\n", "H_Q = nk.operator.LocalOperator(hi, dtype=jnp.complex128)\n", "\n", "# 添加四自旋Q项 - 每个单元格(plaquette)上的四自旋相互作用\n", "for unit_x in range(Lx):\n", "    for unit_y in range(Ly):\n", "        # 找到单元格中的4个顶点\n", "        base_idx = 4 * (unit_x + unit_y * Lx)\n", "        plaq_sites = [\n", "            base_idx,                  # 左下角 (0.5, 0.5) \n", "            base_idx + 1,              # 右下角 (1.5, 0.5)\n", "            base_idx + 2,              # 右上角 (1.5, 1.5)\n", "            base_idx + 3               # 左上角 (0.5, 1.5)\n", "        ]\n", "        \n", "        # 两种不同的顺序添加四自旋相互作用Q项\n", "        # 按照顺时针方向连接\n", "        sites_clockwise = [plaq_sites]\n", "        operatorQ = [(-Q * Cij2).tolist()]\n", "        H_Q += nk.operator.LocalOperator(hi, operatorQ, sites_clockwise)\n", "        \n", "        # 按照交叉方向连接\n", "        sites_cross = [[plaq_sites[0], plaq_sites[2], plaq_sites[1], plaq_sites[3]]]\n", "        H_Q += nk.operator.LocalOperator(hi, operatorQ, sites_cross)\n", "\n", "# 合并两部分哈密顿量\n", "ha = H_J + H_Q\n", "ha = ha.to_jax_operator()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import tree_util\n", "from tqdm.notebook import tqdm\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 定义熵梯度计算函数\n", "def T_logp2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)\n", "\n", "def T_logp_2(params, inputs, temperature, model_instance):\n", "    variables = {\"params\": params}\n", "    preds = model_instance.apply(variables, inputs)\n", "    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2\n", "\n", "# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化\n", "class FreeEnergyVMC_SRt(VMC_SRt):\n", "    def __init__(self, temperature, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # 记录初始温度，用于后续温度递减计算\n", "        self.init_temperature = temperature\n", "        self.temperature = temperature\n", "\n", "    def _step_with_state(self, state):\n", "        # 基础能量梯度更新步骤\n", "        new_state = super()._step_with_state(state)\n", "        params = new_state.parameters\n", "        inputs = new_state.samples\n", "        \n", "        # 计算熵梯度部分\n", "        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)\n", "        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)\n", "        \n", "        # 自由能梯度：能量梯度减去熵梯度\n", "        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s, new_state.gradient, mT_grad_S)\n", "        \n", "        # 更新参数\n", "        new_params = self.optimizer.update(total_grad, params)\n", "        new_state = new_state.replace(parameters=new_params)\n", "        return new_state\n", "\n", "# 添加进度条以及温度递减方案\n", "class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):\n", "    def __init__(self, reference_energy, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        self.reference_energy = reference_energy\n", "\n", "    def run(self, n_iter, out=None):\n", "        \"\"\"运行优化并在 tqdm 进度条中显示 Temperature，Energy，E_var，E_err 和 Rel_err(%)\"\"\"\n", "        outer_pbar = tqdm(total=n_iter, desc=f\"Lx={Lx}, Ly={Ly}\")\n", "        for i in range(n_iter):\n", "            # 更新温度：使用初始温度乘以递减因子\n", "            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0) / 2.0)\n", "            self.advance(1)\n", "\n", "            energy_mean = self.energy.mean\n", "            energy_var = self.energy.variance\n", "            energy_error = self.energy.error_of_mean\n", "            relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100\n", "\n", "            outer_pbar.set_postfix({\n", "                'Temp': f'{self.temperature:.4f}',\n", "                'Energy': f'{energy_mean:.6f}', \n", "                'E_var': f'{energy_var:.6f}',\n", "                'E_err': f'{energy_error:.6f}',\n", "                'Rel_err(%)': f'{relative_error:.4f}',\n", "            })\n", "            outer_pbar.update(1)\n", "        outer_pbar.close()\n", "        return self"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 假设 lattice, hi, hamiltonian, model_no_symm, reference_energy 已经定义\n", "n_samples = 2**12       # 采样数量\n", "chunk_size = 2**10       # 批处理大小\n", "learning_rate = 0.05\n", "diag_shift = 0.001\n", "\n", "# 创建采样器\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hi, graph=lattice, n_chains=2**12, d_max=2)\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_no_symm = CTWFNQS(\n", "    num_layers=2,\n", "    d_model=18,      # token embedding dimension = c=18\n", "    h=2,             # 注意力头数=2，每头token维度 = 9\n", "    n_sites=lattice.n_nodes,\n", "    patch_size=2,\n", "    complex_out=True,\n", "    two_dimensional=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reference_energy=-16.18"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n========== 阶段1: 无对称性约束 ==========\")\n", "# 创建只有恒等操作的对称性组\n", "identity_group = PermutationGroup([Identity()], degree=lattice.n_nodes)\n", "\n", "# 使用SymmExpSum包装基础模型，只有恒等操作\n", "model_stage1 = SymmExpSum(\n", "    module=model_no_symm, \n", "    symm_group=identity_group,\n", "    character_id=None\n", ")\n", "\n", "vqs1 = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model_stage1,\n", "    n_samples=n_samples,\n", "    n_discard_per_chain=0,  # 丢弃初始样本以减少自相关性\n", "    chunk_size=chunk_size,\n", "    training_kwargs={\"holomorphic\": False}\n", ")\n", "\n", "# 假设已经定义了 CustomVMC_SRt 类，用于自定义变分 Monte Carlo 优化，可以直接使用\n", "vmc1 = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=diag_shift,\n", "    variational_state=vqs1,\n", "    temperature=1.0\n", ")\n", "\n", "vmc1.run(n_iter=1000)\n", "energy1 = vmc1.energy.mean.real\n", "print(f\"阶段1完成，能量: {energy1:.6f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n========== 阶段2: 添加平移对称性 ==========\")\n", "# 创建具有平移对称性的对称群\n", "trans_symmetry = lattice.translation_group()\n", "print(\"平移对称群元素数量:\", len(trans_symmetry))\n", "\n", "model_trans = SymmExpSum(\n", "    module=model_no_symm, \n", "    symm_group=trans_symmetry,\n", "    character_id=None\n", ")\n", "\n", "vqs2 = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model_trans,\n", "    n_samples=n_samples,\n", "    n_discard_per_chain=10,  # 较少的丢弃样本\n", "    chunk_size=chunk_size,\n", "    training_kwargs={\"holomorphic\": False}\n", ")\n", "\n", "# 尝试直接复制阶段1的参数到阶段2\n", "\n", "vqs2.parameters = vqs1.parameters\n", "print(\"成功复制阶段1参数到阶段2\")\n", "\n", "\n", "vmc2 = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=diag_shift,\n", "    variational_state=vqs2,\n", "    temperature=1.0\n", ")\n", "\n", "vmc2.run(n_iter=400)\n", "energy2 = vmc2.energy.mean.real\n", "print(f\"阶段2完成，能量: {energy2:.6f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n========== 阶段3: 添加D4对称性 ==========\")\n", "# 使用内置的 C4 对称群\n", "from netket.utils.group import C  # 引入 C4 旋转操作\n", "d4_group = D(4)\n", "\n", "# 定义新的对称中心\n", "new_origin = np.array([3.0, 3.0])\n", "\n", "# 调整对称群，以 new_origin 为新的旋转中心\n", "new_d4_group = d4_group.change_origin(new_origin)\n", "D4_symmetry = lattice.point_group(new_d4_group)\n", "print(\"C4对称群元素数量:\", len(D4_symmetry))\n", "\n", "model_c4 = SymmExpSum(\n", "    module=model_no_symm, \n", "    symm_group=D4_symmetry,\n", "    character_id=None\n", ")\n", "\n", "vqs3 = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=model_c4,\n", "    n_samples=n_samples,\n", "    n_discard_per_chain=10,\n", "    chunk_size=chunk_size,\n", "    training_kwargs={\"holomorphic\": False}\n", ")\n", "\n", "# 尝试复制阶段2的参数到阶段3\n", "try:\n", "    vqs3.parameters = vqs2.parameters\n", "    print(\"成功复制阶段2参数到阶段3\")\n", "except Exception as e:\n", "    print(f\"参数复制失败，使用随机初始化: {str(e)}\")\n", "\n", "vmc3 = CustomFreeEnergyVMC_SRt(\n", "    reference_energy=reference_energy,\n", "    stage_name=\"3 (C4对称性)\",\n", "    hamiltonian=ha,\n", "    optimizer=optimizer,\n", "    diag_shift=diag_shift,\n", "    variational_state=vqs3,\n", "    temperature=1.0,\n", "    stability_window=30,\n", "    stability_threshold=0.0005\n", ")\n", "\n", "vmc3.run(n_iter=200)\n", "energy3 = vmc3.energy.mean.real\n", "print(f\"阶段3完成，能量: {energy3:.6f}\")\n", "# 阶段:阶段1（无对称性）:100% 1000/1000 [1:07:19<00:00, 4.03s/it, Temp=0.0000, Energy=-16.148373, E_var=0.274781, E_err=0.008191, Rel_err(%)=0.6975]\n", "# 阶段:阶段2（平移对称性）:100% 400/400 [57:48<00:00, 8.65s/it, Temp=0.0002, Energy=-16.225645, E_var=0.095039, E_err=0.004817, Rel_err(%)=0.2223]\n", "#阶段:阶段3（C4旋转对称性）:36% 72/200 [09:34<16:46, 7.86s/it, Temp=0.1209, Energy=-16.050443, E_var=0.474660, E_err=0.010765, Rel_err(%)=1.2997]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 1500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_training_history(history, iter_min=None, iter_max=None, line_width=2.5, y_margin_percent=5):\n", "    \"\"\"\n", "    Plot the training history across different stages with continuous iterations.\n", "    \n", "    Args:\n", "        history: Dictionary containing training data for each stage\n", "        iter_min: Minimum iteration to display (optional)\n", "        iter_max: Maximum iteration to display (optional)\n", "        line_width: Width of the plotted lines\n", "        y_margin_percent: Add this percentage margin to y-axis limits for better visualization\n", "    \"\"\"\n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    # Define stage colors and labels\n", "    stage_colors = {'stage1': 'blue', 'stage2': 'green', 'stage3': 'red', 'stage4': 'purple'}\n", "    stage_names = {\n", "        'stage1': 'Stage 1 (No Symmetry)', \n", "        'stage2': 'Stage 2 (Translational Symmetry)',\n", "        'stage3': 'Stage 3 (D4 Symmetry)', \n", "        'stage4': 'Stage 4'\n", "    }\n", "    \n", "    # Process data to calculate cumulative iterations\n", "    all_iterations = []\n", "    all_energies = []\n", "    all_errors = []\n", "    cumulative_iter = 0\n", "    stage_transitions = []\n", "    \n", "    for stage, data in sorted(history.items()):\n", "        if stage != \"final_energy\" and data is not None:\n", "            stage_iters = len(data[\"energy\"])\n", "            iterations = [cumulative_iter + i for i in range(stage_iters)]\n", "            all_iterations.extend(iterations)\n", "            all_energies.extend(data[\"energy\"])\n", "            all_errors.extend(data[\"relative_error\"])\n", "            \n", "            if cumulative_iter > 0:\n", "                stage_transitions.append(cumulative_iter)\n", "                \n", "            cumulative_iter += stage_iters\n", "    \n", "    # Energy plot\n", "    ax1 = plt.subplot(2, 2, 1)\n", "    cumulative_iter = 0\n", "    \n", "    for stage, data in sorted(history.items()):\n", "        if stage != \"final_energy\" and data is not None:\n", "            stage_iters = len(data[\"energy\"])\n", "            iterations = [cumulative_iter + i for i in range(stage_iters)]\n", "            \n", "            # Plot the energy with increased line width\n", "            plt.plot(iterations, data[\"energy\"], \n", "                     label=stage_names.get(stage, stage), \n", "                     color=stage_colors.get(stage, 'black'),\n", "                     linewidth=line_width)\n", "            \n", "            # Add vertical line for stage transition\n", "            if cumulative_iter > 0:\n", "                plt.axvline(x=cumulative_iter, color='gray', linestyle='--', alpha=0.5)\n", "            \n", "            cumulative_iter += stage_iters\n", "    \n", "    # Set x-axis limits\n", "    if iter_min is not None or iter_max is not None:\n", "        x_min = iter_min if iter_min is not None else min(all_iterations)\n", "        x_max = iter_max if iter_max is not None else max(all_iterations)\n", "        ax1.set_xlim(x_min, x_max)\n", "        \n", "        # Dynamically adjust y-axis based on visible data\n", "        visible_energies = [e for i, e in zip(all_iterations, all_energies) if x_min <= i <= x_max]\n", "        if visible_energies:\n", "            y_min = min(visible_energies)\n", "            y_max = max(visible_energies)\n", "            y_range = y_max - y_min\n", "            margin = y_range * (y_margin_percent / 100)\n", "            ax1.set_ylim(y_min - margin, y_max + margin)\n", "    \n", "    plt.xlabel(\"Iterations\")\n", "    plt.ylabel(\"Energy\")\n", "    plt.legend(loc='best')\n", "    plt.title(\"Energy During Training Process\")\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    \n", "    # Relative error plot\n", "    ax2 = plt.subplot(2, 2, 2)\n", "    cumulative_iter = 0\n", "    \n", "    for stage, data in sorted(history.items()):\n", "        if stage != \"final_energy\" and data is not None:\n", "            stage_iters = len(data[\"relative_error\"])\n", "            iterations = [cumulative_iter + i for i in range(stage_iters)]\n", "            \n", "            # Plot the relative error with increased line width\n", "            plt.plot(iterations, data[\"relative_error\"], \n", "                     label=stage_names.get(stage, stage), \n", "                     color=stage_colors.get(stage, 'black'),\n", "                     linewidth=line_width)\n", "            \n", "            # Add vertical line for stage transition\n", "            if cumulative_iter > 0:\n", "                plt.axvline(x=cumulative_iter, color='gray', linestyle='--', alpha=0.5)\n", "            \n", "            cumulative_iter += stage_iters\n", "    \n", "    # Set x-axis limits\n", "    if iter_min is not None or iter_max is not None:\n", "        x_min = iter_min if iter_min is not None else min(all_iterations)\n", "        x_max = iter_max if iter_max is not None else max(all_iterations)\n", "        ax2.set_xlim(x_min, x_max)\n", "        \n", "        # Dynamically adjust y-axis based on visible data\n", "        visible_errors = [e for i, e in zip(all_iterations, all_errors) if x_min <= i <= x_max]\n", "        if visible_errors:\n", "            y_min = min(visible_errors)\n", "            y_max = max(visible_errors)\n", "            y_range = y_max - y_min\n", "            margin = y_range * (y_margin_percent / 100)\n", "            ax2.set_ylim(y_min - margin, y_max + margin)\n", "    \n", "    plt.xlabel(\"Iterations\")\n", "    plt.ylabel(\"Relative Error (%)\")\n", "    plt.legend(loc='best')\n", "    plt.title(\"Relative Error During Training Process\")\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Example usage:\n", "# plot_training_history(training_history)  # Show all iterations with auto-scaled y-axis\n", "plot_training_history(training_history, iter_min=200, iter_max=800, line_width=3, y_margin_percent=10)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}