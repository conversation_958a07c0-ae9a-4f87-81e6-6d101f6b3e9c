{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mFailed to connect to the remote Jupyter Server 'http://localhost:8929/'. Verify the server is running and reachable. (Failed to connect to the remote Jupyter Server 'http://localhost:8929/'. Verify the server is running and reachable. (request to http://localhost:8929/api/kernels?1742449624003 failed, reason: read ECONNRESET).)."]}], "source": ["import jax\n", "from jax import tree_util\n", "import pickle\n", "\n", "def inspect_parameters(params):\n", "    # 打印整体参数数量\n", "    print(f\"总参数数量: {jax.tree_util.tree_reduce(lambda x, y: x + y.size, params, 0)}\")\n", "    \n", "    # 打印参数树的结构\n", "    print(\"\\n参数树结构:\")\n", "    jax.tree_util.tree_map(lambda x: print(f\"形状: {x.shape}, 类型: {x.dtype}\"), params)\n", "    \n", "    # 更详细的层次化参数结构\n", "    flat_params = jax.tree_util.tree_flatten(params)[0]\n", "    print(f\"\\n共有 {len(flat_params)} 个参数张量\")\n", "    \n", "    for i, param in enumerate(flat_params):\n", "        print(f\"参数 {i}: 形状 {param.shape}, 大小 {param.size}, 类型 {param.dtype}\")\n", "        # 打印参数统计信息\n", "        print(f\"  - 均值: {param.mean():.6f}, 标准差: {param.std():.6f}\")\n", "        print(f\"  - 最小值: {param.min():.6f}, 最大值: {param.max():.6f}\")\n", "        \n", "# 方法2：从保存的pickle文件加载\n", "with open(\"results.pkl\", \"rb\") as f:\n", "    saved_data = pickle.load(f)\n", "    \n", "# 检查加载的参数\n", "inspect_parameters(saved_data[\"final_parameters\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-20 05:23:34.249852: W external/xla/xla/stream_executor/cuda/cuda_command_buffer.cc:662] Retry CUDA graph instantiation after OOM error\n", "E0320 05:23:34.249983 2698845 pjrt_stream_executor_client.cc:3086] Execution of replica 0 failed: RESOURCE_EXHAUSTED: Underlying backend ran out of memory trying to instantiate graph with 48 nodes and 0 conditionals (total of 0 alive graphs in the process). You can try to (a) Give more memory to the driver by reducing XLA_CLIENT_MEM_FRACTION (b) Disable command buffers with 'XLA_FLAGS=--xla_gpu_enable_command_buffer=' (empty set). Original error: Failed to instantiate CUDA graph: CUDA_ERROR_OUT_OF_MEMORY: out of memory\n"]}, {"ename": "XlaRuntimeError", "evalue": "RESOURCE_EXHAUSTED: Underlying backend ran out of memory trying to instantiate graph with 48 nodes and 0 conditionals (total of 0 alive graphs in the process). You can try to (a) Give more memory to the driver by reducing XLA_CLIENT_MEM_FRACTION (b) Disable command buffers with 'XLA_FLAGS=--xla_gpu_enable_command_buffer=' (empty set). Original error: Failed to instantiate CUDA graph: CUDA_ERROR_OUT_OF_MEMORY: out of memory", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mXlaRuntimeError\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[7], line 33\u001b[0m\n\u001b[1;32m     29\u001b[0m     plt\u001b[38;5;241m.\u001b[39mshow()\n\u001b[1;32m     32\u001b[0m \u001b[38;5;66;03m# 可视化参数分布\u001b[39;00m\n\u001b[0;32m---> 33\u001b[0m \u001b[43mvisualize_parameters\u001b[49m\u001b[43m(\u001b[49m\u001b[43msaved_data\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfinal_parameters\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[7], line 8\u001b[0m, in \u001b[0;36mvisualize_parameters\u001b[0;34m(params)\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mvisualize_parameters\u001b[39m(params):\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;66;03m# 平铺所有参数\u001b[39;00m\n\u001b[1;32m      7\u001b[0m     flat_params \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m----> 8\u001b[0m     \u001b[43mtree_util\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtree_map\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01m<PERSON>bda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mflat_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mextend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mflatten\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m     flat_params \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(flat_params)\n\u001b[1;32m     11\u001b[0m     plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/tree_util.py:358\u001b[0m, in \u001b[0;36mtree_map\u001b[0;34m(f, tree, is_leaf, *rest)\u001b[0m\n\u001b[1;32m    356\u001b[0m leaves, treedef \u001b[38;5;241m=\u001b[39m tree_flatten(tree, is_leaf)\n\u001b[1;32m    357\u001b[0m all_leaves \u001b[38;5;241m=\u001b[39m [leaves] \u001b[38;5;241m+\u001b[39m [treedef\u001b[38;5;241m.\u001b[39mflatten_up_to(r) \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m rest]\n\u001b[0;32m--> 358\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mtreedef\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43munflatten\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mxs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mxs\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mzip\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mall_leaves\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/tree_util.py:358\u001b[0m, in \u001b[0;36m<genexpr>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m    356\u001b[0m leaves, treedef \u001b[38;5;241m=\u001b[39m tree_flatten(tree, is_leaf)\n\u001b[1;32m    357\u001b[0m all_leaves \u001b[38;5;241m=\u001b[39m [leaves] \u001b[38;5;241m+\u001b[39m [treedef\u001b[38;5;241m.\u001b[39mflatten_up_to(r) \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m rest]\n\u001b[0;32m--> 358\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m treedef\u001b[38;5;241m.\u001b[39munflatten(\u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mxs\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m xs \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39mall_leaves))\n", "Cell \u001b[0;32mIn[7], line 8\u001b[0m, in \u001b[0;36mvisualize_parameters.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mvisualize_parameters\u001b[39m(params):\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;66;03m# 平铺所有参数\u001b[39;00m\n\u001b[1;32m      7\u001b[0m     flat_params \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m----> 8\u001b[0m     tree_util\u001b[38;5;241m.\u001b[39mtree_map(\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43mflat_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mextend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mflatten\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m, params)\n\u001b[1;32m      9\u001b[0m     flat_params \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(flat_params)\n\u001b[1;32m     11\u001b[0m     plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/array.py:377\u001b[0m, in \u001b[0;36m<genexpr>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m    375\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mis_fully_replicated \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mis_fully_addressable\n\u001b[1;32m    376\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dispatch\u001b[38;5;241m.\u001b[39mis_single_device_sharding(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msharding) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mis_fully_replicated:\n\u001b[0;32m--> 377\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m (sl \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_chunk_iter(\u001b[38;5;241m100\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m sl \u001b[38;5;129;01min\u001b[39;00m \u001b[43mchunk\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_unstack\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    378\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msharding, PmapSharding):\n\u001b[1;32m    379\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28mself\u001b[39m[i] \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m]))\n", "    \u001b[0;31m[... skipping hidden 5 frame]\u001b[0m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/interpreters/pxla.py:1298\u001b[0m, in \u001b[0;36mExecuteReplicated.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1296\u001b[0m   \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handle_token_bufs(result_token_bufs, sharded_runtime_token)\n\u001b[1;32m   1297\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1298\u001b[0m   results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mxla_executable\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute_sharded\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_bufs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1300\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dispatch\u001b[38;5;241m.\u001b[39mneeds_check_special():\n\u001b[1;32m   1301\u001b[0m   out_arrays \u001b[38;5;241m=\u001b[39m results\u001b[38;5;241m.\u001b[39mdisassemble_into_single_device_arrays()\n", "\u001b[0;31mXlaRuntimeError\u001b[0m: RESOURCE_EXHAUSTED: Underlying backend ran out of memory trying to instantiate graph with 48 nodes and 0 conditionals (total of 0 alive graphs in the process). You can try to (a) Give more memory to the driver by reducing XLA_CLIENT_MEM_FRACTION (b) Disable command buffers with 'XLA_FLAGS=--xla_gpu_enable_command_buffer=' (empty set). Original error: Failed to instantiate CUDA graph: CUDA_ERROR_OUT_OF_MEMORY: out of memory"]}], "source": ["def analyze_vit_attention_weights(params):\n", "    \"\"\"分析ViT模型中的注意力权重\"\"\"\n", "    # 找出所有注意力矩阵J\n", "    attention_weights = {}\n", "    \n", "    def find_attention_matrices(params, path=''):\n", "        if isinstance(params, dict):\n", "            for key, value in params.items():\n", "                # 检查是否是注意力层的J矩阵\n", "                if key == 'J' and path.find('attn') != -1:\n", "                    attention_weights[path] = value\n", "                find_attention_matrices(value, f\"{path}/{key}\" if path else key)\n", "    \n", "    find_attention_matrices(params)\n", "    \n", "    # 可视化几个注意力头的权重\n", "    for i, (path, weights) in enumerate(attention_weights.items()):\n", "        if i >= 3:  # 仅展示前3个注意力层\n", "            break\n", "        \n", "        plt.figure(figsize=(12, 4))\n", "        plt.suptitle(f\"注意力权重: {path}\")\n", "        \n", "        if len(weights.shape) == 3:  # [头数, L_eff, L_eff]\n", "            for h in range(min(3, weights.shape[0])):\n", "                plt.subplot(1, 3, h+1)\n", "                plt.imshow(weights[h], cmap='viridis')\n", "                plt.colorbar()\n", "                plt.title(f'头 {h}')\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(f'attention_weights_{i}.png', dpi=300)\n", "        plt.show()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}