{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\"\n", "# Import netket library\n", "import netket as nk\n", "# Helper libraries\n", "import numpy as np  \n", "import matplotlib.pyplot as plt\n", "from netket.operator.spin import sigmax, sigmay, sigmaz"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The exact ground-state energy is E0 =  -0.4463935225385488\n"]}], "source": ["L = 16\n", "\n", "# 1-d hypercube with periodic boundary \n", "lattice = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)\n", "\n", "# Define the <PERSON>lbert space: spin half, total magnetization 0, same size as g)\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, inverted_ordering=False)\n", "\n", "# <PERSON>\n", "# There is a factor of 2 between Pauli-matrices and spin-1/2 operators (thus a factor of 4 in H)\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice,sign_rule=False, J=1)\n", "\n", "# Compute both eigenvalues and eigenvectors\n", "evals, evecs = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=True)\n", "\n", "# 输出基态能量\n", "print('The exact ground-state energy is E0 = ', evals[0] / (4 * L))  #基态能量除以四是因为netket里用的泡利矩，缺少1/2常数，两个相互作用就是1/4\n", "\n", "# 获取基态波函数\n", "ground_state = evecs[:, 0]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def correlation_function(L, r, op_constructor, hilbert, wavefunction):\n", "    corr_op = nk.operator.LocalOperator(hilbert, 0.0)\n", "    for i in range(L):\n", "        j = (i + r) % L\n", "        corr_op += op_constructor(hilbert, i) * op_constructor(hilbert, j)\n", "    mat = corr_op.to_dense()\n", "    return 0.25*(wavefunction.conjugate().T @ (mat @ wavefunction)) / L\n", "\n", "# x_vals = range(1, L)\n", "x_vals = range(1, (L // 2) + 1)\n", "\n", "corr_vals = []\n", "for r in x_vals:\n", "    corr_x = correlation_function(L, r, sigmax, hilbert, ground_state)\n", "    corr_vals.append(np.real(corr_x))\n", "\n", "plt.plot(x_vals, corr_vals, marker='o')\n", "plt.xlabel('r')\n", "plt.ylabel('Correlation')\n", "plt.title(f'Correlation Function(L={L}, J2=0)')\n", "plt.grid(True)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}