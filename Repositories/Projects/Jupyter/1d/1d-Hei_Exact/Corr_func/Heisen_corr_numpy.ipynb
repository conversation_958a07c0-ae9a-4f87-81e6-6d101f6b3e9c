{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground-State: J1-J2 model\n", "\n", "$$ H = \\sum_{i=1}^{L} J_{1}\\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{i+1} + J_{2} \\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{i+2} $$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Matrix approach"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-3.  1.  1.  1.]\n", "[[ 1.  0.  0.  0.]\n", " [ 0. -1.  2.  0.]\n", " [ 0.  2. -1.  0.]\n", " [ 0.  0.  0.  1.]]\n"]}], "source": ["#2 spin\n", "import numpy as np\n", "\n", "# 定义单自旋算符\n", "Sx = np.array([[0, 1], [1, 0]])\n", "Sy = np.array([[0, -1j], [1j, 0]])\n", "Sz = np.array([[1, 0], [0, -1]])\n", "I = np.eye(2)  # 单位矩阵\n", "\n", "H_2spin_x= np.kron(Sx, Sx)\n", "H_2spin_y= np.kron(Sy, Sy)\n", "H_2spin_z= np.kron(Sz, Sz)\n", "\n", "H_2spin=H_2spin_x+H_2spin_y+H_2spin_z\n", "\n", "E_2spin, V_2spin = np.linalg.eigh(H_2spin)  # 计算哈密顿量的本征值和本征矢量\n", "\n", "print(E_2spin)\n", "print(np.real(H_2spin))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground state energy: -7.999999999999997\n", "Ground state vector:\n", " [ 0.00000000e+00+0.j  0.00000000e+00+0.j  0.00000000e+00+0.j\n", "  2.88675135e-01+0.j  0.00000000e+00+0.j -5.77350269e-01+0.j\n", "  2.88675135e-01+0.j  0.00000000e+00+0.j  0.00000000e+00+0.j\n", "  2.88675135e-01+0.j -5.77350269e-01+0.j -2.48920611e-60+0.j\n", "  2.88675135e-01+0.j -1.38178697e-76+0.j -2.48920611e-60+0.j\n", "  0.00000000e+00+0.j]\n"]}], "source": ["# 4 spin\n", "H1= np.kron(np.kron(np.kron(Sx, Sx), I), I)\n", "H2 = np.kron(np.kron(np.kron(I, Sx), Sx), I)\n", "H3 = np.kron(np.kron(np.kron(I, I), Sx), Sx)\n", "H4 = np.kron(np.kron(np.kron(Sx, I), I), Sx)\n", "H_x=H1+H2+H3+H4\n", "\n", "H5= np.kron(np.kron(np.kron(Sy, Sy), I), I)\n", "H6 = np.kron(np.kron(np.kron(I, Sy), Sy), I)\n", "H7 = np.kron(np.kron(np.kron(<PERSON>, <PERSON>), <PERSON>y), Sy)\n", "H8 = np.kron(np.kron(np.kron(<PERSON>y, <PERSON>), I), Sy)\n", "H_y=H5+H6+H7+H8\n", "\n", "\n", "H9 = np.kron(np.kron(np.kron(Sz, Sz), I), I)\n", "H10 = np.kron(np.kron(np.kron(I, Sz), Sz), I)\n", "H11 = np.kron(np.kron(np.kron(I, I), Sz), Sz)\n", "H12 = np.kron(np.kron(np.kron(Sz, I), I), Sz)\n", "H_z=H9+H10+H11+H12\n", "\n", "# 计算哈密顿量\n", "H = H_x + H_y + H_z\n", "\n", "E, V = np.linalg.eigh(H)  # 计算哈密顿量的本征值和本征矢量\n", "\n", "E, V = np.linalg.eigh(H)\n", "\n", "print(\"Ground state energy:\", E[0])\n", "print(\"Ground state vector:\\n\", V[:,0])\n", "\n", "ground_state = V[:, 0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Exact diag and corr func"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["L=10"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-4.51544635 -4.09220735 -4.09220735 ...  2.5         2.5\n", "  2.5       ]\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 定义单自旋算符\n", "Sx = 0.5*np.array([[0, 1], [1, 0]])\n", "Sy = 0.5*np.array([[0, -1j], [1j, 0]])\n", "Sz = 0.5*np.array([[1, 0], [0, -1]])\n", "I = np.eye(2)  # 单位矩阵\n", "\n", "def H_function(L, pauli_op):\n", "    \n", "    # Initialize H with correct shape and dtype\n", "    H = np.zeros((2**L, 2**L), dtype=np.complex128)\n", "    \n", "    for i in range(L):\n", "        operators = [I] * L # N个单位矩阵\n", "        operators[i] = pauli_op # 第i个变成泡利算符\n", "        operators[(i + 1) % L] = pauli_op  # 使用模运算符处理第一个和最后一个自旋对的相互作用\n", "        \n", "        H_term = operators[0]\n", "        for op in operators[1:]:\n", "            H_term = np.kron(H_term, op)\n", "            \n", "        H += H_term\n", "    \n", "    return H\n", "\n", "def generate_total_H(L):\n", "    H_x = H_function(L, Sx)\n", "    H_y = H_function(L, Sy)\n", "    H_z = H_function(L, Sz)\n", "    return H_x, H_y, H_z\n", "\n", "\n", "H_x, H_y, H_z = generate_total_H(L)\n", "H=H_x+H_y+H_z\n", "E, V = np.linalg.eigh(H)\n", "print(E)\n", "gst = V[:, 0]  # 基态波函数"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniconda/base/envs/playground/lib/python3.12/site-packages/matplotlib/cbook.py:1762: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return math.isfinite(val)\n", "/opt/homebrew/Caskroom/miniconda/base/envs/playground/lib/python3.12/site-packages/matplotlib/cbook.py:1398: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return np.asarray(x, float)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def correlation_function(L, r, pauli_op):\n", "    \n", "    # Initialize H with correct shape and dtype\n", "    corr = np.zeros((2**L, 2**L), dtype=np.complex128)\n", "    \n", "    for i in range(L):\n", "        operators = [I] * L # N个单位矩阵\n", "        operators[i] = pauli_op # 第i个变成泡利算符\n", "        operators[(i + r) % L] = pauli_op  # 使用模运算符处理第一个和最后一个自旋对的相互作用\n", "        \n", "        corr_term = operators[0]\n", "        for op in operators[1:]:\n", "            corr_term = np.kron(corr_term, op)\n", "        \n", "        corr += corr_term\n", "    corr_func =np.dot(gst.conj().T, np.dot(corr, gst))/L\n", "    return corr_func\n", "\n", "def generate_total_H(L,r):\n", "    corr_x = correlation_function(L, r, Sx)\n", "    corr_y = correlation_function(L, r, Sy)\n", "    corr_z = correlation_function(L, r, Sz)\n", "    return corr_x, corr_y, corr_z\n", "\n", "x_values = range(1, L)\n", "y_values = []\n", "\n", "for r in x_values:\n", "    corr_x, _, _ = generate_total_H(L, r)\n", "    y_values.append(corr_x)\n", "\n", "plt.plot(x_values, y_values, marker='o')\n", "plt.xlabel('r')\n", "plt.ylabel('Correlation Function (corr_x)')\n", "plt.title('Correlation Function corr_x from r=1 to r=10')\n", "plt.grid(True)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "playground", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}