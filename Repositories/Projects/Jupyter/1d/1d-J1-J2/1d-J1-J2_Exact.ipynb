{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\"\n", "\n", "import jax  # 导入JAX库，用于加速数值计算\n", "import jax.numpy as jnp  # 导入JAX的NumPy子模块，用于数值计算\n", "from jax._src import dtypes  # 导入JAX的dtypes模块，用于处理数据类型\n", "\n", "import netket as nk  # 导入NetKet库，用于量子多体系统的模拟\n", "from netket.experimental.driver.vmc_srt import VMC_SRt  # 导入VMC_SRt类\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "\n", "import time  # 导入time库，用于计算运行时间\n", "import matplotlib.pyplot as plt  # 导入matplotlib库，用于绘图"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The exact ground-state energy is E0 =  -0.4452193264938236\n"]}], "source": ["# 设置1D晶格的参数\n", "L = 20  # 晶格长度，即晶格中包含的节点数\n", "J2 = 0  # 下一个最近邻相互作用的耦合常数，表示相邻节点之间的相互作用强度\n", "\n", "# 创建1D晶格\n", "lattice = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)  # 创建一个长度为L的1D链式晶格，具有周期性边界条件和最大邻居顺序为2\n", "\n", "# 创建<PERSON><PERSON>空间\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)  # 创建一个Hilbert空间，表示晶格上的自旋系统，具有总自旋为0的约束\n", "\n", "# 创建Heisenberg自旋哈密顿量\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])  # 创建一个Heisenberg自旋哈密顿量，包含最近邻和下一个最近邻的相互作用\n", "\n", "# 使用Lanczos算法计算哈密顿量的本征值和本征向量\n", "evals, evecs = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=True)  \n", "\n", "# 输出基态能量\n", "print('The exact ground-state energy is E0 = ', evals[0] / (4 * L))  #基态能量除以四是因为netket里用的泡利矩，缺少1/2常数，两个相互作用就是1/4\n", "\n", "# 获取基态波函数\n", "ground_state = evecs[:, 0]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def correlation_function(L, r, op_constructor, hilbert, wavefunction):\n", "    corr_op = nk.operator.LocalOperator(hilbert, 0.0)\n", "    for i in range(L):\n", "        j = (i + r) % L\n", "        corr_op += op_constructor(hilbert, i) * op_constructor(hilbert, j)\n", "    mat = corr_op.to_dense()\n", "    return 0.25 * (wavefunction.conjugate().T @ (mat @ wavefunction)) / L\n", "\n", "x_vals = range(1, (L // 2) + 1)\n", "corr_vals = []\n", "for r in x_vals:\n", "    cval = correlation_function(L, r, sigmaz, hilbert, ground_state)\n", "    corr_vals.append(jnp.real(cval))\n", "\n", "plt.plot(x_vals, corr_vals, marker='o')\n", "plt.xlabel('r')\n", "plt.ylabel('Correlation')\n", "plt.title('J1-J2 Correlation Function')\n", "plt.grid(True)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}