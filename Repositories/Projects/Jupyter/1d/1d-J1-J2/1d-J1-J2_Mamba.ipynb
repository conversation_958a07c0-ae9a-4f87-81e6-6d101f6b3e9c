{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# [file name]: mamba1d.py\n", "# [file content begin]\n", "from flax import linen as nn\n", "import jax\n", "import jax.numpy as jnp\n", "from jax.scipy.signal import convolve\n", "import numpy as np\n", "\n", "class SSMLayer(nn.Module):\n", "    d_model: int\n", "    d_state: int = 16\n", "    dt_rank: int = 2\n", "    kernel_init: nn.initializers.Initializer = nn.initializers.normal(0.02)\n", "    \n", "    @nn.compact\n", "    def __call__(self, x):\n", "        batch, seq_len, _ = x.shape\n", "        \n", "        # 参数定义（调整卷积核维度）\n", "        A = self.param(\"A\", self.kernel_init, (self.d_state,))\n", "        D = self.param(\"D\", nn.initializers.ones, (self.d_model,))\n", "        dt = self.param(\"dt\", self.kernel_init, (seq_len, self.dt_rank))\n", "        B = self.param(\"B\", self.kernel_init, (self.dt_rank, self.d_state))\n", "        C = self.param(\"C\", self.kernel_init, (self.d_model, self.d_state))  # 调整为 [d_model, d_state]\n", "        \n", "        # 离散化过程\n", "        delta = jnp.exp(dt @ B)  # [seq_len, d_state]\n", "        A_bar = jnp.exp(-delta * jnp.exp(A))  # [seq_len, d_state]\n", "        B_bar = (delta / jnp.exp(A)) * (1 - A_bar)  # [seq_len, d_state]\n", "        \n", "        # 调整卷积核维度为 [d_model, d_state, seq_len]\n", "        B_bar_exp = B_bar.T[None, :, :]  # [1, d_state, seq_len]\n", "        C_exp = C[:, :, None]            # [d_model, d_state, 1]\n", "        kernel = B_bar_exp * C_exp       # [d_model, d_state, seq_len]\n", "        kernel = jnp.moveaxis(kernel, -1, 0)  # [seq_len, d_state, d_model]\n", "        \n", "        # 执行逐通道一维卷积（调整输入和卷积核维度）\n", "        u = x * D  # [batch, seq_len, d_model]\n", "        # 将输入和卷积核转为 [d_model, batch, seq_len] 和 [d_model, d_state, seq_len]\n", "        u_perm = jnp.moveaxis(u, -1, 0)  # [d_model, batch, seq_len]\n", "        kernel_perm = jnp.moveaxis(kernel, -1, 0)  # [d_model, d_state, seq_len]\n", "        \n", "        # 逐通道卷积（每个通道独立处理）\n", "        y = jax.vmap(lambda u_ch, k_ch: convolve(u_ch, k_ch, mode='same'))(u_perm, kernel_perm)\n", "        y = jnp.moveaxis(y, 0, -1)  # 恢复维度 [batch, seq_len, d_model]\n", "        \n", "        # 残差连接\n", "        y = y + u * A_bar[None, :, None]\n", "        return y\n", "\n", "class MambaBlock(nn.Module):\n", "    d_model: int\n", "    b: int = 4\n", "    expand: int = 2\n", "    \n", "    @nn.compact\n", "    def __call__(self, x):\n", "        x = x[..., None]  # [batch, L, 1]\n", "        x = x.reshape(x.shape[0], -1, self.b)  # [batch, L//b, b]\n", "        x = jnp.tile(x, (1, 1, self.d_model))  # [batch, L//b, b*d_model]\n", "        x = x.reshape(x.shape[0], -1, self.d_model)  # [batch, seq_len, d_model]\n", "        \n", "        x = nn.Dense(self.d_model * self.expand)(x)\n", "        x = SSMLayer(d_model=self.d_model * self.expand)(x)\n", "        x = nn.silu(x)\n", "        x = nn.<PERSON><PERSON>(self.d_model)(x)\n", "        return x.reshape(x.shape[0], -1)  # [batch, seq_len]\n", "\n", "class Mamba_Enc(nn.Mo<PERSON>le):\n", "    d_model: int\n", "    b: int = 4\n", "    n_layers: int = 2\n", "    \n", "    @nn.compact\n", "    def __call__(self, x):\n", "        x = x.reshape(x.shape[0], -1)  # [batch, L]\n", "        for _ in range(self.n_layers):\n", "            x = MambaBlock(d_model=self.d_model, b=self.b)(x)\n", "        return jnp.sum(x, axis=1)  # [batch, d_model]\n", "# [file content end]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "One input must be smaller than the other in every dimension.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 42\u001b[0m\n\u001b[1;32m     40\u001b[0m key \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39mPR<PERSON><PERSON><PERSON>(seed)\n\u001b[1;32m     41\u001b[0m key, subkey \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39msplit(key)\n\u001b[0;32m---> 42\u001b[0m params \u001b[38;5;241m=\u001b[39m \u001b[43mwf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minit\u001b[49m\u001b[43m(\u001b[49m\u001b[43msubkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mzeros\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlattice\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mn_nodes\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     44\u001b[0m \u001b[38;5;66;03m# 采样器与优化器配置\u001b[39;00m\n\u001b[1;32m     45\u001b[0m sampler \u001b[38;5;241m=\u001b[39m nk\u001b[38;5;241m.\u001b[39msampler\u001b[38;5;241m.\u001b[39mMetropolisExchange(\n\u001b[1;32m     46\u001b[0m     hilbert\u001b[38;5;241m=\u001b[39mhilbert, graph\u001b[38;5;241m=\u001b[39mlattice, d_max\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m, n_chains\u001b[38;5;241m=\u001b[39mN_samples, n_sweeps\u001b[38;5;241m=\u001b[39mlattice\u001b[38;5;241m.\u001b[39mn_nodes\n\u001b[1;32m     47\u001b[0m )\n", "    \u001b[0;31m[... skipping hidden 9 frame]\u001b[0m\n", "Cell \u001b[0;32mIn[1], line 78\u001b[0m, in \u001b[0;36mMamba_Enc.__call__\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     76\u001b[0m x \u001b[38;5;241m=\u001b[39m x\u001b[38;5;241m.\u001b[39mreshape(x\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m], \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)  \u001b[38;5;66;03m# [batch, L]\u001b[39;00m\n\u001b[1;32m     77\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_layers):\n\u001b[0;32m---> 78\u001b[0m     x \u001b[38;5;241m=\u001b[39m \u001b[43mMambaBlock\u001b[49m\u001b[43m(\u001b[49m\u001b[43md_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43md_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mb\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     79\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m jnp\u001b[38;5;241m.\u001b[39msum(x, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n", "    \u001b[0;31m[... skipping hidden 2 frame]\u001b[0m\n", "Cell \u001b[0;32mIn[1], line 64\u001b[0m, in \u001b[0;36mMambaBlock.__call__\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     61\u001b[0m x \u001b[38;5;241m=\u001b[39m x\u001b[38;5;241m.\u001b[39mreshape(x\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m], \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39md_model)  \u001b[38;5;66;03m# [batch, seq_len, d_model]\u001b[39;00m\n\u001b[1;32m     63\u001b[0m x \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mDense(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39md_model \u001b[38;5;241m*\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexpand)(x)\n\u001b[0;32m---> 64\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[43mSS<PERSON><PERSON>er\u001b[49m\u001b[43m(\u001b[49m\u001b[43md_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43md_model\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     65\u001b[0m x \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39msilu(x)\n\u001b[1;32m     66\u001b[0m x \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mDense(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39md_model)(x)\n", "    \u001b[0;31m[... skipping hidden 2 frame]\u001b[0m\n", "Cell \u001b[0;32mIn[1], line 44\u001b[0m, in \u001b[0;36mSS<PERSON>ayer.__call__\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     41\u001b[0m kernel_perm \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mmoveaxis(kernel, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m0\u001b[39m)  \u001b[38;5;66;03m# [d_model, d_state, seq_len]\u001b[39;00m\n\u001b[1;32m     43\u001b[0m \u001b[38;5;66;03m# 逐通道卷积（每个通道独立处理）\u001b[39;00m\n\u001b[0;32m---> 44\u001b[0m y \u001b[38;5;241m=\u001b[39m \u001b[43mjax\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvmap\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mu_ch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mk_ch\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvolve\u001b[49m\u001b[43m(\u001b[49m\u001b[43mu_ch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mk_ch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msame\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mu_perm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkernel_perm\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     45\u001b[0m y \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mmoveaxis(y, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)  \u001b[38;5;66;03m# 恢复维度 [batch, seq_len, d_model]\u001b[39;00m\n\u001b[1;32m     47\u001b[0m \u001b[38;5;66;03m# 残差连接\u001b[39;00m\n", "    \u001b[0;31m[... skipping hidden 6 frame]\u001b[0m\n", "Cell \u001b[0;32mIn[1], line 44\u001b[0m, in \u001b[0;36mSS<PERSON>ayer.__call__.<locals>.<lambda>\u001b[0;34m(u_ch, k_ch)\u001b[0m\n\u001b[1;32m     41\u001b[0m kernel_perm \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mmoveaxis(kernel, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m0\u001b[39m)  \u001b[38;5;66;03m# [d_model, d_state, seq_len]\u001b[39;00m\n\u001b[1;32m     43\u001b[0m \u001b[38;5;66;03m# 逐通道卷积（每个通道独立处理）\u001b[39;00m\n\u001b[0;32m---> 44\u001b[0m y \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mvmap(\u001b[38;5;28;01mlambda\u001b[39;00m u_ch, k_ch: \u001b[43mconvolve\u001b[49m\u001b[43m(\u001b[49m\u001b[43mu_ch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mk_ch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msame\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m)(u_perm, kernel_perm)\n\u001b[1;32m     45\u001b[0m y \u001b[38;5;241m=\u001b[39m jnp\u001b[38;5;241m.\u001b[39mmoveaxis(y, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)  \u001b[38;5;66;03m# 恢复维度 [batch, seq_len, d_model]\u001b[39;00m\n\u001b[1;32m     47\u001b[0m \u001b[38;5;66;03m# 残差连接\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/scipy/signal.py:248\u001b[0m, in \u001b[0;36mconvolve\u001b[0;34m(in1, in2, mode, method, precision)\u001b[0m\n\u001b[1;32m    246\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m fftconvolve(in1, in2, mode\u001b[38;5;241m=\u001b[39mmode)\n\u001b[1;32m    247\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m method \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdirect\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m'\u001b[39m]:\n\u001b[0;32m--> 248\u001b[0m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_convolve_nd\u001b[49m\u001b[43m(\u001b[49m\u001b[43min1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprecision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprecision\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    250\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGot \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmethod\u001b[38;5;132;01m=}\u001b[39;00m\u001b[38;5;124m; expected \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfft\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, or \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdirect\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/jax/_src/scipy/signal.py:166\u001b[0m, in \u001b[0;36m_convolve_nd\u001b[0;34m(in1, in2, mode, precision)\u001b[0m\n\u001b[1;32m    164\u001b[0m swap \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mall\u001b[39m(s1 \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m s2 \u001b[38;5;28;01mfor\u001b[39;00m s1, s2 \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(in1\u001b[38;5;241m.\u001b[39mshape, in2\u001b[38;5;241m.\u001b[39mshape))\n\u001b[1;32m    165\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (no_swap \u001b[38;5;129;01mor\u001b[39;00m swap):\n\u001b[0;32m--> 166\u001b[0m   \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOne input must be smaller than the other in every dimension.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    168\u001b[0m shape_o \u001b[38;5;241m=\u001b[39m in2\u001b[38;5;241m.\u001b[39mshape\n\u001b[1;32m    169\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m swap:\n", "\u001b[0;31mValueError\u001b[0m: One input must be smaller than the other in every dimension."]}], "source": ["# [file name]: main_gs.py\n", "# [file content begin]\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import time\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "seed = 0\n", "\n", "# 1<PERSON>\n", "L = 100\n", "J2 = 0.8\n", "\n", "# Settings optimization\n", "diag_shift = 1e-3\n", "eta = 0.01\n", "N_opt = 10000\n", "N_samples = 3000\n", "N_discard = 0\n", "\n", "# Settings wave function\n", "f = 1\n", "d_model = 8  # 直接定义d_model，无需与heads关联\n", "b = 4        # 确保 L % b == 0\n", "\n", "#! End input\n", "\n", "lattice = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])\n", "\n", "if L <= 16:\n", "    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)\n", "    print(f'Exact E0: {evals[0]/(4*L):.5f}')\n", "\n", "# 初始化Mamba波函数\n", "wf = Mamba_Enc(d_model=d_model, b=b, n_layers=2)  # 注意移除h参数\n", "\n", "key = jax.random.<PERSON><PERSON><PERSON><PERSON>(seed)\n", "key, subkey = jax.random.split(key)\n", "params = wf.init(subkey, jnp.zeros((1, lattice.n_nodes)))\n", "\n", "# 采样器与优化器配置\n", "sampler = nk.sampler.MetropolisExchange(\n", "    hilbert=hilbert, graph=lattice, d_max=2, n_chains=N_samples, n_sweeps=lattice.n_nodes\n", ")\n", "vstate = nk.vqs.MCState(\n", "    sampler=sampler, \n", "    model=wf, \n", "    sampler_seed=key, \n", "    n_samples=N_samples, \n", "    n_discard_per_chain=N_discard,\n", "    variables=params\n", ")\n", "print(f'Parameters: {nk.jax.tree_size(vstate.parameters)}')\n", "\n", "optimizer = nk.optimizer.Sgd(learning_rate=eta)\n", "vmc = VMC_SRt(hamiltonian=hamiltonian, optimizer=optimizer, variational_state=vstate, diag_shift=diag_shift)\n", "\n", "# 运行优化\n", "start = time.time()\n", "vmc.run(out='Mamba_GS', n_iter=N_opt)\n", "print(f'Time: {time.time()-start:.1f}s')\n", "# [file content end]"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}