{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mon Feb  3 11:29:26 2025       \n", "+---------------------------------------------------------------------------------------+\n", "| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |\n", "|-----------------------------------------+----------------------+----------------------+\n", "| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |\n", "|                                         |                      |               MIG M. |\n", "|=========================================+======================+======================|\n", "|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:03:00.0 Off |                    0 |\n", "| N/A   44C    P0              54W / 400W |      0MiB / 40960MiB |      0%      Default |\n", "|                                         |                      |             Disabled |\n", "+-----------------------------------------+----------------------+----------------------+\n", "                                                                                         \n", "+---------------------------------------------------------------------------------------+\n", "| Processes:                                                                            |\n", "|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |\n", "|        ID   ID                                                             Usage      |\n", "|=======================================================================================|\n", "|  No running processes found                                                           |\n", "+---------------------------------------------------------------------------------------+\n"]}], "source": ["# 查看GPU信息\n", "!nvidia-smi"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from flax import linen as nn\n", "\n", "from typing import Sequence\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "from jax._src import dtypes\n", "\n", "# 正负对称的初始化\n", "def custom_uniform(scale=1e-2, dtype=jnp.float_):\n", "    def init(key, shape, dtype=dtype):\n", "        dtype = dtypes.canonicalize_dtype(dtype)\n", "        return jax.random.uniform(key, shape, dtype, minval=-scale, maxval=scale)\n", "    return init #返回的init输入符合jax接口规范\n", "\n", "@jax.jit\n", "def log_cosh(x):\n", "    sgn_x = -2 * jnp.signbit(x.real) + 1\n", "    x = x * sgn_x\n", "    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)\n", "\n", "@jax.jit\n", "def attention(J, values, shift):\n", "    values = jnp.roll(values, shift, axis=0)\n", "    return jnp.sum(J * values, axis=0)\n", "\n", "class EncoderBlock(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L: int\n", "    b: int\n", "\n", "    def setup(self):\n", "        scale = (3.0 * 0.7 / self.L) ** 0.5\n", "        self.v_projR = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.3, \"fan_in\", \"uniform\"),\n", "                                bias_init=nn.initializers.zeros)\n", "        self.v_projI = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.3, \"fan_in\", \"uniform\"),\n", "                                bias_init=nn.initializers.zeros)\n", "        self.JR = self.param(\"JR\", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)\n", "        self.JI = self.param(\"JI\", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)\n", "        self.W0R = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "        self.W0I = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "\n", "    def __call__(self, x):\n", "        J = self.JR + 1j * self.JI\n", "        x = self.v_projR(x).reshape(self.L, self.h, -1) + 1j * self.v_projI(x).reshape(self.L, self.h, -1)\n", "        x = jax.vmap(attention, (None, None, 0))(J, x, jnp.arange(self.L))\n", "        x = x.reshape(self.L, -1)\n", "        x = self.W0R(x) + 1j * self.W0I(x)\n", "        return log_cosh(x)\n", "\n", "class Transformer_Enc(nn.Module):\n", "    d_model: int\n", "    h: int\n", "    L: int\n", "    b: int\n", "\n", "    def setup(self):\n", "        self.encoder = EncoderBlock(self.d_model, self.h, self.L, self.b)\n", "\n", "    def __call__(self, x):\n", "        x = x.reshape(x.shape[0], -1, self.b)\n", "        x = jax.vmap(self.encoder)(x)\n", "        return jnp.sum(x, axis=(1, 2))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> 28 08:18:35 2025       \n", "+---------------------------------------------------------------------------------------+\n", "| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |\n", "|-----------------------------------------+----------------------+----------------------+\n", "| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |\n", "|                                         |                      |               MIG M. |\n", "|=========================================+======================+======================|\n", "|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:03:00.0 Off |                    0 |\n", "| N/A   45C    P0              57W / 400W |  30814MiB / 40960MiB |      0%      Default |\n", "|                                         |                      |             Disabled |\n", "+-----------------------------------------+----------------------+----------------------+\n", "                                                                                         \n", "+---------------------------------------------------------------------------------------+\n", "| Processes:                                                                            |\n", "|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |\n", "|        ID   ID                                                             Usage      |\n", "|=======================================================================================|\n", "|    0   N/A  N/A   1767340      C   /usr/bin/python                           30806MiB |\n", "+---------------------------------------------------------------------------------------+\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/lib/python3.10/pty.py:89: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  pid, fd = os.forkpty()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of parameters =  288\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/tmp/pbs.9329445.pbs101/ipykernel_1767340/3386191990.py:54: UnoptimalSRtWarning: \n", "You are in the case n_samples > num_params (3200 > 288),\n", "for which the `VMC_SRt` is not optimal. Consider using `netket.driver.VMC`\n", "with the preconditioner `nk.optimizer.SR` to achieve the same parameter dynamics,\n", "but with improved speed.\n", "\n", "\n", "-------------------------------------------------------\n", "For more detailed informations, visit the following link:\n", "\t https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.UnoptimalSRtWarning.html\n", "or the list of all common errors and warnings at\n", "\t https://netket.readthedocs.io/en/latest/api/errors.html\n", "-------------------------------------------------------\n", "\n", "  vmc = VMC_SRt(hamiltonian=hamiltonian, optimizer=optimizer, diag_shift=diag_shift, variational_state=vstate)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e1501168d29f4d1fa857ec129878557b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The calculation took  22.672118186950684  seconds\n", "The optimized energy is E0 =  (-0.*****************-0.0002436897174283934j)\n"]}], "source": ["import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import time\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "# 设置JAX使用GPU\n", "jax.config.update(\"jax_platform_name\", \"cpu\")\n", "\n", "\n", "\n", "# 设置随机种子和参数\n", "seed = 0\n", "L = 16\n", "J2 = 0\n", "diag_shift = 1e-3\n", "eta = 0.005\n", "N_opt = 500\n", "N_samples = 3200\n", "N_discard = 0\n", "f = 1\n", "heads = 8\n", "d_model = f * heads\n", "b = 4\n", "\n", "\n", "# 创建晶格和Hilbert空间\n", "lattice = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)\n", "hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)\n", "hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J=[1.0, J2])\n", "\n", "\n", "\n", "# 初始化变分波函数和采样器\n", "wf = Transformer_Enc(d_model=d_model, h=heads, L=L // b, b=b)\n", "key = jax.random.<PERSON><PERSON><PERSON><PERSON>(seed)\n", "key, subkey = jax.random.split(key, num=2)\n", "params = wf.init(subkey, jnp.zeros((1, lattice.n_nodes)))\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hilbert, graph=lattice, d_max=2, \n", "                                       n_chains=N_samples, sweep_size=lattice.n_nodes)\n", "key, subkey = jax.random.split(key, 2)\n", "vstate = nk.vqs.MCState(sampler=sampler, model=wf, sampler_seed=subkey, \n", "                        n_samples=N_samples, n_discard_per_chain=N_discard, variables=params)\n", "print('Number of parameters = ', nk.jax.tree_size(vstate.parameters))\n", "\n", "\n", "\n", "optimizer = nk.optimizer.Sgd(learning_rate=eta)\n", "# optimizer = nk.optimizer.<PERSON>(learning_rate=eta)\n", "\n", "# 创建VMC驱动器\n", "vmc = VMC_SRt(hamiltonian=hamiltonian, optimizer=optimizer, diag_shift=diag_shift, variational_state=vstate)\n", "\n", "\n", "\n", "# 优化过程\n", "start = time.time()\n", "vmc.run(out='ViT', n_iter=N_opt)\n", "end = time.time()\n", "print('The calculation took ', end - start, ' seconds')\n", "\n", "# 获取并打印优化后的能量\n", "energy = vstate.expect(hamiltonian).mean\n", "print('The optimized energy is E0 = ', energy / (4 * L))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-28 08:19:10.689844: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:10.689892: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:10.707679: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:10.707707: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The exact ground-state energy is E0 =  -0.4463935225385482\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 加载和处理优化日志数据\n", "data_ViT = json.load(open('ViT.log'))\n", "min_iter = 0\n", "max_iter = 400\n", "iters_ViT = data_ViT['Energy']['iters'][min_iter:max_iter]\n", "energy_ViT = [e / (4 * L) for e in data_ViT['Energy']['Mean']['real'][min_iter:max_iter]]\n", "\n", "# 计算基态能量（适用于L ≤ 16）\n", "if L <= 16:\n", "    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)\n", "    exact_gs_energy = evals[0] / (4 * L)\n", "    print('The exact ground-state energy is E0 = ', exact_gs_energy)\n", "    error_ViT = [np.abs((e - exact_gs_energy) / exact_gs_energy) for e in energy_ViT]\n", "    fig, ax2 = plt.subplots()\n", "    ax2.plot(iters_ViT, error_ViT, color='C1', label='Error (ViT)')\n", "    ax2.set_ylabel('Relative Error')\n", "    ax2.set_xlabel('Iteration')\n", "    ax2.set_title(f'Relative Error Iteration (L={L})')\n", "    ax2.set_xlim([0, max_iter])\n", "    ax2.legend()\n", "\n", "\n", "# 绘制能量迭代图\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_ViT, energy_ViT, color='C8', label='ViT')\n", "ax1.set_ylabel('E/N')\n", "ax1.set_xlabel('Iteration')\n", "ax1.set_title(f'Energy iteration (L={L})')\n", "\n", "if 'exact_gs_energy' in locals():\n", "    plt.axis([0, max_iter, exact_gs_energy - 0.1, exact_gs_energy + 0.4])\n", "    plt.axhline(y=exact_gs_energy, xmin=0, xmax=max_iter, linewidth=2, color='k', label='Exact')\n", "ax1.legend()\n", "\n", "# 绘制相对误差迭代图\n", "\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-28 08:19:11.576240: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:11.576275: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:13.406176: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:13.406214: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:14.098913: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n", "2025-01-28 08:19:14.098950: W external/xla/xla/service/gpu/ir_emitter_unnested.cc:1171] Unable to parse backend config for custom call: Could not convert JSON string to proto: : Root element must be a message.\n", "Fall back to parse the raw backend config str.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 定义二点和四点算符\n", "def build_2site_operator(hilbert, graph):\n", "    op = nk.operator.LocalOperator(hilbert)\n", "    L = graph.n_nodes\n", "    for R in range(L):\n", "        R1 = (R + 1) % L\n", "        Sz_R = nk.operator.spin.sigmaz(hi<PERSON>, <PERSON>)\n", "        Sz_R1 = nk.operator.spin.sigmaz(hilbert, R1)\n", "        op += (Sz_R @ Sz_R1)\n", "    return op\n", "\n", "def build_4site_operator(hilbert, graph, r):\n", "    op = nk.operator.LocalOperator(hilbert)\n", "    L = graph.n_nodes\n", "    for R in range(L):\n", "        R1 = (R + 1) % L\n", "        Rr = (R + r) % L\n", "        Rr1 = (R + r + 1) % L\n", "        Sz_R = nk.operator.spin.sigmaz(hi<PERSON>, <PERSON>)\n", "        Sz_R1 = nk.operator.spin.sigmaz(hilbert, R1)\n", "        Sz_Rr = nk.operator.spin.sigmaz(hilbert, Rr)\n", "        Sz_Rr1 = nk.operator.spin.sigmaz(hilbert, Rr1)\n", "        op += (Sz_R @ Sz_R1 @ Sz_Rr @ Sz_Rr1)\n", "    return op\n", "\n", "# 计算相关函数\n", "two_site_op = build_2site_operator(hilbert, lattice)\n", "Czz1_val = (vstate.expect(two_site_op).mean / L) * 0.25\n", "corr_vals = []\n", "max_r = L // 2\n", "for r in range(1, max_r + 1):\n", "    val_r = (vstate.expect(build_4site_operator(hilbert, lattice, r)).mean / L) * 0.0625\n", "    Dr = val_r - Czz1_val**2\n", "    corr_vals.append(Dr.<PERSON>)\n", "\n", "# 绘制D(r)图\n", "plt.plot(range(1, max_r + 1), corr_vals, marker='o')\n", "plt.xlabel(\"r\")\n", "plt.ylabel(\"D(r)\")\n", "plt.title(f\"J2/J1={J2}\")\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 2}