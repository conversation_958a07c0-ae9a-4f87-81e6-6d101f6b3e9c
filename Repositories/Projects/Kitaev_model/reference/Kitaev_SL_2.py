import os
import logging
import sys

os.environ["CUDA_VISIBLE_DEVICES"]="0"
os.environ["JAX_PLATFORM_NAME"] = "gpu"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"]="false"
#os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"]="platform"

import jax
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import eigsh
from matplotlib.colors import LogNorm
import time
import sys 
from matplotlib.colors import LogNorm
import json
import netket.nn as nknn
import flax
import flax.linen as nn
import jax.numpy as jnp
import math
from math import pi
from netket.nn import log_cosh, reim_selu, reim_relu
from netket.utils.group.planar import rotation, reflection_group, D, C
from netket.utils.group import PointGroup, Identity
from netket.operator.spin import sigmax, sigmay, sigmaz
from netket.utils.group import PermutationGroup,Permutation
from netket.graph import Kagome
from netket.optimizer.qgt import (
QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly
)
from netket.operator import AbstractOperator
from netket.vqs import VariationalState
from netket.graph import Honeycomb, KitaevHoneycomb
from netket.hilbert.random import flip_state
from netket.sampler import MetropolisRule
from netket.utils.struct import dataclass
from typing import Union, Any
import numpy as np
import jax
from jax import numpy as jnp
from flax import linen as nn
from jax.nn.initializers import normal
from netket.utils import HashableArray
from netket.utils.types import NNInitFunc
from netket.utils.group import PermutationGroup
from netket import nn as nknn

default_kernel_init = normal(stddev=0.01)


Lx = 4
Ly = 4
  

N_features = 6
N_layers = 6


# Lagrange multiplier for sum of all plaquette operators 
Lambda=0.0

n_iter=20000

# Kitaev-Heisenberg model in a [111] magnetic field

Kz=1.0
Kx=1.0
Ky=1.0

hx=0.1
hy=0.1
hz=0.1

J=0.0


custom_edges = [
    
    
    (0, 1, [0.5,1.0/(2.0*jnp.sqrt(3.0))], 0),
    (0, 1, [-0.5,1.0/(2.0*jnp.sqrt(3.0))],1),
    (0, 1, [0.0,-1.0/(jnp.sqrt(3.0))],2),
  
]

# Honeycomb lattice 

lattice = nk.graph.Lattice(
basis_vectors = [[1.0,0.0], [0.5,jnp.sqrt(3.0)/2.0]], 
extent = (Lx,Ly),
site_offsets = [[0.5,1.0/(2.0*jnp.sqrt(3.0))],[1.0,(1.0/jnp.sqrt(3.0))]], custom_edges=custom_edges,pbc=[True,True])


lattice.draw(edge_color='red')

plt.savefig("Honeycomb_lattice.png")

#correlated RBM, similar to cRBM in Phys. Rev. Research 4, L012010 (2022) ( arXiv:2103.05017)

class cRBM(nn.Module):
    param_dtype: Any = np.float64
    activation: Any = nknn.log_cosh
    alpha: Union[float, int] = 1
    use_hidden_bias: bool = True
    use_visible_bias: bool = True
    precision: Any = None
    kernel_init: NNInitFunc = default_kernel_init
    hidden_bias_init: NNInitFunc = default_kernel_init
    visible_bias_init: NNInitFunc = default_kernel_init

    @nn.compact
    def __call__(self, input):
        input_b=input
        l_b=jnp.size(input_b,axis=0)# n_samples
        input_c=jnp.reshape(input_b,(l_b,Lx,2*Ly))
        input_d=jnp.transpose(input_c,(0,2,1))
        l_d=jnp.size(input_d,axis=0)
        input_e=jnp.reshape(input_d,(l_d,Ly,2*Lx))
        input_f=jnp.sort(input_e,axis=-1)

        input_c_roll=jnp.roll(input_c,-1,axis=-1)
        input_cc_roll=input_c*input_c_roll
        input_cc_roll_final=jnp.reshape(input_cc_roll,(l_b,2*Lx*Ly))

        input_f_roll=jnp.roll(input_f,-1,axis=-1)
        input_ff_roll=input_f*input_f_roll
        input_ff_roll_final=jnp.reshape(input_ff_roll,(l_b,2*Lx*Ly))

        input_ff_roll_final_2=jnp.delete(input_ff_roll_final,slice(None,None,2),axis=1)

        input_final_nn_correlators=jnp.concatenate((input_cc_roll_final,input_ff_roll_final_2),axis=-1)

        input_final=jnp.concatenate((input_b,input_final_nn_correlators),axis=-1)

        x = nn.Dense(
            name="Dense",
            features=int(self.alpha * input_final.shape[-1]),
            param_dtype=self.param_dtype,
            precision=self.precision,
            use_bias=self.use_hidden_bias,
            kernel_init=self.kernel_init,
            bias_init=self.hidden_bias_init,
        )(input_final)
        x = self.activation(x)
        x = jnp.sum(x, axis=-1)

        if self.use_visible_bias:
            v_bias = self.param(
                "visible_bias",
                self.visible_bias_init,
                (input_final.shape[-1],),
                self.param_dtype,
            )
            out_bias = jnp.dot(input_final, v_bias)
            return x + out_bias
        else:
            return x

# Hilbert-space, spin 1/2

hi= nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)

sigmax = [[0, 0.5], [0.5, 0]]
sigmay = [[0, -0.5j], [0.5j, 0]] 
sigmaz = [[0.5, 0], [0, -0.5]]

sigmax_1 = [[0, 1.0], [1.0, 0]]
sigmay_1 = [[0, -1.0j], [1.0j, 0]] 
sigmaz_1 = [[1.0, 0], [0, -1.0]]


sigmaxi_sigmaxj = np.kron(sigmax,sigmax)
sigmayi_sigmayj = np.kron(sigmay,sigmay)
sigmazi_sigmazj= np.kron(sigmaz,sigmaz)


SiSj = sigmaxi_sigmaxj+sigmayi_sigmayj+sigmazi_sigmazj


sampler=nk.sampler.MetropolisLocal(hilbert=hi,n_chains=2**13)

def T_logp2(params,inputs,temperature):
    variables={"params":params}
    preds=model.apply(variables,inputs)
    return 2.0*temperature*jnp.mean(jnp.real(preds)*jnp.real(preds))

def T_logp_2(params,inputs,temperature):
    variables={"params":params}
    preds=model.apply(variables,inputs)
    return 2.0*temperature*jnp.mean(jnp.real(preds))*jnp.mean(jnp.real(preds))


symmetries=lattice.space_group(C(2))

momentum=[0.0,0.0]
sgb=lattice.space_group_builder(point_group=C(2))

print(sgb.little_group(momentum).character_table_readable())
chi = sgb.space_group_irreps(momentum)[0] 

print(chi)

# NQS

model_no_symm=cRBM(alpha=4,param_dtype = np.complex128)
model=nk.nn.blocks.SymmExpSum(module=model_no_symm,symm_group=symmetries,character_id=0)

print(symmetries.character_table()[0])


bond_operator = [
    (Kx*sigmaxi_sigmaxj-J*SiSj).tolist(),
    (Ky*sigmayi_sigmayj-J*SiSj).tolist(),
    (Kz*sigmazi_sigmazj-J*SiSj).tolist(),
 ]   

bond_color = [0,1,2]


site_operator = [ (-hx*np.array(sigmax)-hy*np.array(sigmay)-hz*np.array(sigmaz)).tolist()]


# Hamiltonian for Kitaev-Heisenberg model in a [111] magnetic field

H0 = nk.operator.GraphOperator(hi, graph=lattice, bond_ops=bond_operator,site_ops=site_operator,bond_ops_colors=bond_color)


# constraint for sum of all plaquette operators (Lagrange multiplier, Lambda)

prodxyz=np.kron(np.kron(sigmay_1,sigmaz_1),sigmax_1)
prodxyz2=np.kron(prodxyz,prodxyz)

Hp=nk.operator.LocalOperator(hi,dtype=jnp.complex128)


for x in range(Lx):
    for y in range(Ly):
        Wp=[]
        sites=[]
        n1 = y+Ly*x
        n2 = y+Ly*((x+1)%Lx)
        n3 = y-1+Ly*int(y==0)+Ly*((x+1)%Lx)
        n4 =y-1+Ly*int(y==0)+Ly*x

        in1=2*n1
        jn1=2*n1+1
        in2=2*n2
        jn2=2*n2+1
        in3=2*n3
        jn3=2*n3+1
        in4=2*n4
        jn4=2*n4+1

        Wp.append(prodxyz2)
        sites.append((in1,jn1,in2,jn3,in3,jn4))

        Hp = Hp+nk.operator.LocalOperator(hi,Wp,sites,dtype=jnp.complex128)

# Hp : sum of all plaquette operators

H=H0-Lambda*Hp


start = time.time()

#  Variational state 

vqs = nk.vqs.MCState(
sampler=sampler,
model=model,
n_samples=2**13,
n_discard_per_chain=0,
chunk_size=2**10,
)

with open("cRBM_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"cRBM_Lambda.mpack", 'rb') as file:
    vqs = flax.serialization.from_bytes(vqs, file.read())


from tqdm import tqdm

n_ann = n_iter
n_train=1


if os.path.exists("E_real_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("E_real_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file E_real does not exist") 
  
if os.path.exists("E_imag_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("E_imag_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file E_imag does not exist")
  
if os.path.exists("Iters_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("Iters_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file Iters does not exist")  
  
  
if os.path.exists("E_error_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("E_error_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file E_error does not exist") 
  
if os.path.exists("E_variance_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("E_variance_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file E_variance does not exist") 
  
  
if os.path.exists("E_ED_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat"):
  os.remove("E_ED_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat")
else:
  print("The file E_ED does not exist")   
  

def custom_sr_free_energy(
      hamiltonian: AbstractOperator,    
      vstate: VariationalState,                                            
      lr: float,                                         
      temperature: float,
      n_ann: int,
      n_train:int,
):
      
      for i in range(n_ann):
          temperature_i=temperature*(jnp.exp(-i/50.0)/2.0)
          for j in tqdm(range(n_train)):
            with open("cRBM_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(round(Kz,1))+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"cRBM.mpack", 'wb') as file:
                file.write(flax.serialization.to_bytes(vstate))
            
            energy, f = vstate.expect_and_grad(hamiltonian)
            variables=vstate.variables 
            inputs0=vstate.samples
            inputs1=jnp.reshape(inputs0,(1,sampler.n_chains,lattice.n_nodes))
            inputs=inputs1[0]
            
                
            with open("E_real_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat","a+") as file:
                file.write(str(energy.mean.real)+" ")
            with open("E_imag_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat","a+") as file:
                file.write(str(energy.mean.imag)+" ")
            with open("Iters_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat","a+") as file:
                file.write(str(i)+" ")
                
            with open("E_error_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat","a+") as file:
                file.write(str(energy.error_of_mean)+" ")
            with open("E_variance_Kitaev_SL_Lx="+str(Lx)+"_Ly="+str(Ly)+"_Kx="+str(Kx)+"_Ky="+str(Ky)+"_Kz="+str(Kz)+"J="+str(J)+"hx="+str(hx)+"hy="+str(hy)+"hz="+str(hz)+"_cRBM.dat","a+") as file:
                file.write(str(energy.variance)+" ")    

            
            print("iteration:",i)                           
            print("E:", energy.mean,"+-",energy.error_of_mean)  
            
            G = vstate.quantum_geometric_tensor(QGTJacobianDense(diag_shift=0.001,diag_scale=0.001))
            
            mT_grad_S_1 = jax.grad(T_logp2,argnums=0)(variables["params"],inputs,temperature_i)
            
            mT_grad_S_2 = jax.grad(T_logp_2,argnums=0)(variables["params"],inputs,temperature_i)
            
            mT_grad_S = jax.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)            
            
            gamma_S=jax.tree_map(lambda x: -1.0 *jnp.conj(x), mT_grad_S)
            
            gamma_f = jax.tree_map(lambda x: -1.0 * x, f)
            
            gamma_tot= jax.tree_map(lambda x,y: x+y, gamma_f,gamma_S)
            
            dtheta, _ = G.solve(jax.scipy.sparse.linalg.cg, gamma_tot)
            
            vstate.parameters = jax.tree_map(
            lambda x, y: x + lr * y, vstate.parameters, dtheta
            
            )            
           
custom_sr_free_energy(hamiltonian=H,vstate=vqs,lr = 0.1,n_ann=n_ann, n_train=n_train, temperature=0.0)

end = time.time()


print('The cRBM calculation took',end-start,'seconds')

