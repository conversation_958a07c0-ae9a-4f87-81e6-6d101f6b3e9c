# Kitaev模型神经量子态模型比较

本项目实现了多种神经量子态模型用于求解蜂窝晶格上的Kitaev模型。下面是各模型的特点和适用场景的详细比较。

## 模型架构总览

### 1. cRBM (相关受限玻尔兹曼机)
- **核心思想**: 基于受限玻尔兹曼机，加入最近邻关联子
- **优势**: 
  - 实现简单，计算效率高
  - 在小系统上效果良好
  - 参数量相对较少
- **局限性**:
  - 表达能力有限，难以捕捉复杂的长程关联
  - 在大系统或强关联系统中精度下降明显

### 2. ViT (Vision Transformer)
- **核心思想**: 将自旋构型映射为2D网格，用ViT处理
- **优势**:
  - 强大的长程关联捕捉能力
  - 自注意力机制天然适合量子纠缠
- **局限性**:
  - **缺乏归纳偏置**: 抛弃了局域性假设，需要从数据中学习物理规律
  - **方格映射问题**: 蜂窝晶格强制映射到方格破坏了原有的几何结构
  - 计算开销大，参数量多

### 3. CTWF (Convolutional Transformer Wavefunction)
- **核心思想**: 结合CNN的局域特征提取和Transformer的长程关联
- **优势**:
  - 平衡了局域和长程特征提取
  - 卷积层提供物理归纳偏置
  - 在多种系统上表现优秀
- **局限性**:
  - **仍然存在方格映射问题**: 卷积层假设规则网格结构
  - 蜂窝晶格的非规则连接在方格上难以准确表示

### 4. **GNNViT (Graph Neural Network + Vision Transformer) - 本项目新贡献**

## GNNViT: 面向蜂窝晶格的最优解决方案

### 核心设计理念

GNNViT直接在蜂窝晶格的**真实拓扑结构**上操作，彻底解决了以往模型的方格映射问题：

```
蜂窝晶格 → 图表示 → GNN特征提取 → 图注意力长程关联 → 输出
    ↑                ↑                  ↑
保持原始几何    尊重局域相互作用    捕捉量子纠缠
```

### 架构创新点

#### 1. **真实拓扑操作**
- 直接从NetKet晶格对象提取图结构
- 边索引`[2, n_edges]`精确描述蜂窝晶格连接
- 保持原始的三重对称性和几何特性

#### 2. **物理感知的边特征**
```python
edge_attr: [n_edges, 3]  # Kitaev键类型: [X键, Y键, Z键]
```
- 每条边编码了其Kitaev相互作用类型（X/Y/Z键）
- 图卷积层能够区分不同类型的相互作用
- 比方格卷积更准确地反映物理哈密顿量

#### 3. **分层特征提取**
```
GraphConvUnit (局域) → GraphAttentionUnit (长程) → FFN
     ↓                        ↓                  ↓
  提取局域关联            建立长程纠缠         非线性变换
```

#### 4. **图感知的位置编码**
- 基于最短路径距离的位置编码
- 比2D网格的行列索引更适合非规则图结构
- 注意力机制能感知节点间的拓扑距离

### 相比其他模型的优势

#### vs. CTWF
| 特性 | CTWF | GNNViT |
|------|------|--------|
| 拓扑保持 | ❌ 方格映射 | ✅ 真实蜂窝晶格 |
| 局域特征 | 🟡 2D卷积 | ✅ 图卷积 |
| 边类型感知 | ❌ | ✅ X/Y/Z键编码 |
| 几何对称性 | 🟡 部分保持 | ✅ 完全保持 |

#### vs. ViT
| 特性 | ViT | GNNViT |
|------|-----|--------|
| 归纳偏置 | ❌ 缺乏 | ✅ 图结构偏置 |
| 计算效率 | 🟡 O(N²) | ✅ O(E) (E≪N²) |
| 物理解释性 | 🟡 较弱 | ✅ 强 |

### 预期性能优势

1. **精度提升**: 直接在真实拓扑上操作，避免方格映射引入的误差
2. **效率提升**: 图卷积只在相邻节点间传播，计算复杂度更低
3. **泛化能力**: 图结构归纳偏置提高对不同参数区域的泛化
4. **物理一致性**: 模型架构与物理哈密顿量结构高度匹配

### 配置示例
```yaml
# configs/model/GNNViT.yaml
_target_: src.models.GNNViT.GNNViTNQS
num_layers: 4              # 比CTWF更少的层数即可达到相似效果
d_model: 32                # 更紧凑的特征维度
heads: 4                   # 多头注意力
max_path_length: 8         # 蜂窝晶格的特征路径长度
```

## 使用建议

### 何时选择GNNViT?
- ✅ **蜂窝晶格系统** (如Kitaev模型)
- ✅ **需要高精度** (目标0.1%误差)
- ✅ **重视物理解释性**
- ✅ **计算资源有限**

### 何时选择其他模型?
- **cRBM**: 快速原型验证、小系统
- **ViT**: 通用性要求高、不太关心物理解释性
- **CTWF**: 规则格点系统、已有良好的方格表示

## 实验建议

基于GNNViT的设计优势，推荐的实验策略：

1. **从小系统开始**: 4×4蜂窝晶格验证基本功能
2. **逐步扩展**: 对比不同系统尺寸下的精度表现
3. **消融实验**: 
   - 关闭边特征编码
   - 使用不同的聚合函数
   - 调整注意力范围 (`max_path_length`)
4. **与CTWF对比**: 在相同参数区域比较收敛速度和最终精度

## 总结

GNNViT代表了神经量子态模型设计的一个重要进步：**从通用架构适配转向物理系统定制**。通过直接在真实拓扑上操作，它有望在Kitaev模型上实现前所未有的精度和效率平衡。

这种设计思路也为其他非规则格点系统（如三角格、Kagome格）的神经量子态研究提供了重要参考。 