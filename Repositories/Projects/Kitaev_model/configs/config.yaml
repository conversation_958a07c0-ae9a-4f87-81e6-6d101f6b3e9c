# @package _global_

# 默认配置组, Hydra将从这些子目录中加载默认值
defaults:
  - system: kitaev
  - training: default
  - model: ViT  # 默认模型，可以通过命令行覆盖
  - _self_  # 允许从此文件覆盖以上默认值

# Hydra作业配置
hydra:
  run:
    # 采用与sweep一致的目录结构, 以免用户忘记--multirun时路径混乱
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}/Job_0
  sweep:
    # 多重运行的根目录
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}
    # 子任务目录名
    subdir: Job_${hydra.job.num} 