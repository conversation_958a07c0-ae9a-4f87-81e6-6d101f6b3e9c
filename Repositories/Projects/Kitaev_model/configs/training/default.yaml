# GPU分布式计算配置
gpu_mesh_shape: [1, 2] # 格式: [model_axis, batch_axis], 使用列表
use_model_sharding: true

# 实验名称，会作为输出目录的一部分
experiment_name: "pretrain"

# Checkpoint 配置
checkpoint:
  # 是否启用checkpoint功能
  enable: false
  # checkpoint保存间隔（迭代次数）
  save_interval: 500
  # 要从中恢复训练的checkpoint路径（相对于工作目录或绝对路径）
  # 支持以下格式：
  # 1. 具体的checkpoint文件路径：results/.../checkpoints/checkpoint_iter_001000.pkl
  # 2. checkpoint目录路径：results/.../checkpoints （自动选择最新的checkpoint）
  # 3. null 或留空：从头开始训练
  resume_from: null
  # 是否保留历史checkpoint（如果为false，只保留最新的checkpoint）
  keep_history: true

# 这里可以放置从`src/models`中各TrainingConfig导入的共享参数
# 例如，如果所有模型都使用相同的迭代次数和学习率
# iterations: 10000
# learning_rate: 0.01 