_target_: src.models.GNNViT.GNNViTNQS

# 模型标识
name: GNNViT

# 模型参数
model:
  num_layers: 4              # GNN-Transformer层数（比CTWF稍少，因为GNN能更高效地提取特征）
  d_model: 32                # 模型维度（从较小的值开始）
  heads: 4                   # 注意力头数
  max_path_length: 10        # 图中考虑的最大路径长度（蜂窝晶格通常不需要太大）
  edge_types: 3              # Kitaev键类型数量（X/Y/Z键）
  attention_cutoff: 4        # 注意力截断距离（多少跳内的节点参与注意力计算）
  param_dtype: "float32"     # 数据类型
  use_symmetries: true      # 是否使用对称性

# 训练参数
training:
  use_model_sharding: true   # GNNViT计算效率高，先不用模型分片

  seed: 0
  learning_rate: 0.05       # 稍微降低学习率，因为边敏感机制增加了模型复杂性
  n_train: 1                 # 每次退火的训练步数
  n_samples: 4096            # 样本数量，比ViT少一些（GNN效率更高）
  n_discard_per_chain: 50    # 每条链丢弃的样本数
  chunk_size: 1024           # 批处理大小
  diag_shift: 0.05           # 相对较小的对角线位移
  grad_clip: 0.75            # 梯度裁剪阈值

  # 热重启余弦退火参数
  n_cycles: 7                # 热重启周期数
  initial_period: 150        # 初始周期长度，稍长以让GNN充分学习
  period_mult: 2.0           # 周期倍数
  max_temperature: 1.0       # 最大温度
  min_temperature: 0.0       # 最小温度

# 注释：重要设计特点（边敏感版本）
# 1. 相比CTWF，GNNViT直接在蜂窝晶格的真实拓扑上操作
# 2. 图卷积层处理局域相互作用，边敏感注意力层处理长程纠缠
# 3. 边特征编码了Kitaev模型的X/Y/Z键类型信息，直接融入注意力计算
# 4. 基于最短路径距离的位置编码比2D网格更适合蜂窝晶格
# 5. 边敏感注意力让模型能区分不同类型的相互作用路径，对Kitaev模型至关重要
# 6. 每种键类型（X/Y/Z）都有专门的注意力偏置，提高了物理建模精度 