# cRBM 模型配置
name: cRBM

# 模型参数
model:
  alpha: 4                     # RBM隐藏单元比例
  param_dtype: "complex128"    # 参数数据类型
  use_hidden_bias: true        # 是否使用隐藏偏置
  use_visible_bias: true       # 是否使用可见偏置
  use_symmetries: true         # 是否使用对称性

# 训练参数
training:
  seed: 0
  learning_rate: 0.1           # 学习率
  n_cycles: 8                  # 热重启周期数（总迭代次数 = 25500）
  n_train: 1                   # 每次退火的训练步数
  n_samples: 8192              # 样本数量 (2**13)
  n_discard_per_chain: 0       # 每条链丢弃的样本数
  chunk_size: 1024             # 批处理大小 (2**10)
  diag_shift: 0.01             # 对角线位移
  clip_norm: 1.0               # 梯度裁剪阈值
  use_model_sharding: true     # 默认启用模型分片
  
  # 热重启余弦退火参数
  initial_period: 100          # 初始周期长度
  period_mult: 2.0             # 周期倍数
  max_temperature: 1.0         # 最大温度
  min_temperature: 0.0         # 最小温度 