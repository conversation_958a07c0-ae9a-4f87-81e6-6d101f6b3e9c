#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=2 
#PBS -l walltime=23:00:00
#PBS -P 12004256
#PBS -N kitaev-gnnvit-train
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

echo "==================== 预训练阶段 (无对称性) ===================="
echo "Starting pretraining without symmetries..."

# ==================== Checkpoint 配置 ====================
# 是否启用checkpoint保存 (true/false)
ENABLE_CHECKPOINT=true

# checkpoint保存间隔（迭代次数）
CHECKPOINT_INTERVAL=3000

# 从checkpoint恢复训练的路径（可选）
# 示例：results/L4x4_K1.01.01.0_J0.0_h0.10.10.1_L0.0/2025-06-17/04-12_pretrain_no_symm/Job_0/checkpoints
# 留空表示从头开始训练
RESUME_FROM_CHECKPOINT=""

# 是否保留历史checkpoint (true/false)
KEEP_CHECKPOINT_HISTORY=true

# 构建checkpoint相关的命令行参数
CHECKPOINT_ARGS=""
if [ "$ENABLE_CHECKPOINT" = "true" ]; then
    CHECKPOINT_ARGS="training.checkpoint.enable=true training.checkpoint.save_interval=$CHECKPOINT_INTERVAL training.checkpoint.keep_history=$KEEP_CHECKPOINT_HISTORY"
    
    if [ -n "$RESUME_FROM_CHECKPOINT" ]; then
        echo "Resuming from checkpoint: $RESUME_FROM_CHECKPOINT"
        CHECKPOINT_ARGS="$CHECKPOINT_ARGS training.checkpoint.resume_from=$RESUME_FROM_CHECKPOINT"
    fi
    
    echo "Checkpoint enabled with interval: $CHECKPOINT_INTERVAL"
else
    CHECKPOINT_ARGS="training.checkpoint.enable=false"
    echo "Checkpoint disabled"
fi

# ==================== 模型配置 ====================
# 可选择的模型类型: cRBM, ViT, CTWF, GNNViT
MODEL_TYPE=GNNViT

# 根据模型类型设置实验名称
EXPERIMENT_NAME="gnnvit_pretrain"

echo "Using model: $MODEL_TYPE"
echo "Experiment name: $EXPERIMENT_NAME"

# 使用简化的命令行参数，系统配置和模型配置均使用yaml文件
singularity exec --nv -B /scratch,/app \
    /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
    python -m src.train --multirun \
    model=$MODEL_TYPE \
    system=kitaev \
    training.experiment_name=$EXPERIMENT_NAME \
    training.gpu_mesh_shape=[1,2] \
    $CHECKPOINT_ARGS

echo "预训练完成！模型已保存，可用于后续微调。"
echo "Job finished at: $(date)" 