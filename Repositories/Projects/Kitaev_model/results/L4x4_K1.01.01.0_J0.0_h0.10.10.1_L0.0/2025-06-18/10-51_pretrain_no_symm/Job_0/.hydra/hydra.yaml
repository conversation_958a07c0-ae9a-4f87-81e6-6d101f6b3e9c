hydra:
  run:
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}/Job_0
  sweep:
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}
    subdir: Job_${hydra.job.num}
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: MULTIRUN
  searchpath: []
  callbacks: {}
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=MULTIRUN
    task:
    - model=ViT
    - system=kitaev
    - training.experiment_name=pretrain_no_symm
    - training.gpu_mesh_shape=[1,2]
    - training.checkpoint.enable=True
    - training.checkpoint.save_interval=3000
    - training.checkpoint.keep_history=True
  job:
    name: train
    chdir: null
    override_dirname: model=ViT,system=kitaev,training.checkpoint.enable=True,training.checkpoint.keep_history=True,training.checkpoint.save_interval=3000,training.experiment_name=pretrain_no_symm,training.gpu_mesh_shape=[1,2]
    id: '0'
    num: 0
    config_name: config
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys: []
  runtime:
    version: 1.3.2
    version_base: '1.3'
    cwd: /home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: /home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/configs
      schema: file
      provider: main
    - path: ''
      schema: structured
      provider: schema
    output_dir: /home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/results/L4x4_K1.01.01.0_J0.0_h0.10.10.1_L0.0/2025-06-18/10-51_pretrain_no_symm/Job_0
    choices:
      model: ViT
      training: default
      system: kitaev
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false
