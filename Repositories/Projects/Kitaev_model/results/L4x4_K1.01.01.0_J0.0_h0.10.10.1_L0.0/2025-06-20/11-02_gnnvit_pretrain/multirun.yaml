hydra:
  run:
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}/Job_0
  sweep:
    dir: ${system.output_dir}/${now:%Y-%m-%d}/${now:%H-%M}_${training.experiment_name}
    subdir: Job_${hydra.job.num}
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: MULTIRUN
  searchpath: []
  callbacks: {}
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=MULTIRUN
    task:
    - model=GNNViT
    - system=kitaev
    - training.experiment_name=gnnvit_pretrain
    - training.gpu_mesh_shape=[1,2]
    - training.checkpoint.enable=true
    - training.checkpoint.save_interval=3000
    - training.checkpoint.keep_history=true
  job:
    name: train
    chdir: null
    override_dirname: model=GNNViT,system=kitaev,training.checkpoint.enable=true,training.checkpoint.keep_history=true,training.checkpoint.save_interval=3000,training.experiment_name=gnnvit_pretrain,training.gpu_mesh_shape=[1,2]
    id: ???
    num: ???
    config_name: config
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys: []
  runtime:
    version: 1.3.2
    version_base: '1.3'
    cwd: /home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: /home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/configs
      schema: file
      provider: main
    - path: ''
      schema: structured
      provider: schema
    output_dir: ???
    choices:
      model: GNNViT
      training: default
      system: kitaev
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false
system:
  Lx: 4
  Ly: 4
  Kx: 1.0
  Ky: 1.0
  Kz: 1.0
  J: 0.0
  h:
    x: 0.1
    'y': 0.1
    z: 0.1
  Lambda: 0.0
  spin: 0.5
  reference_energy: -6.396
  output_dir: results/L${.Lx}x${.Ly}_K${.Kx}${.Ky}${.Kz}_J${.J}_h${.h.x}${.h.y}${.h.z}_L${.Lambda}
training:
  gpu_mesh_shape:
  - 1
  - 2
  use_model_sharding: true
  experiment_name: gnnvit_pretrain
  checkpoint:
    enable: true
    save_interval: 3000
    resume_from: null
    keep_history: true
model:
  _target_: src.models.GNNViT.GNNViTNQS
  name: GNNViT
  model:
    num_layers: 4
    d_model: 32
    heads: 4
    max_path_length: 10
    edge_types: 3
    attention_cutoff: 4
    param_dtype: float32
    use_symmetries: true
  training:
    use_model_sharding: true
    seed: 0
    learning_rate: 0.05
    n_train: 1
    n_samples: 4096
    n_discard_per_chain: 50
    chunk_size: 1024
    diag_shift: 0.05
    grad_clip: 0.75
    n_cycles: 7
    initial_period: 150
    period_mult: 2.0
    max_temperature: 1.0
    min_temperature: 0.0
