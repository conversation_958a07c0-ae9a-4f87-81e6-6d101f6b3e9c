system:
  Lx: 4
  Ly: 4
  Kx: 1.0
  Ky: 1.0
  Kz: 1.0
  J: 0.0
  h:
    x: 0.1
    'y': 0.1
    z: 0.1
  Lambda: 0.0
  spin: 0.5
  reference_energy: -6.396
  output_dir: results/L${.Lx}x${.Ly}_K${.Kx}${.Ky}${.Kz}_J${.J}_h${.h.x}${.h.y}${.h.z}_L${.Lambda}
training:
  gpu_mesh_shape:
  - 1
  - 2
  use_model_sharding: true
  experiment_name: gnnvit_pretrain
  checkpoint:
    enable: true
    save_interval: 3000
    resume_from: null
    keep_history: true
model:
  _target_: src.models.GNNViT.GNNViTNQS
  name: GNNViT
  model:
    num_layers: 4
    d_model: 32
    heads: 4
    max_path_length: 10
    edge_types: 3
    attention_cutoff: 4
    param_dtype: float32
    use_symmetries: true
  training:
    use_model_sharding: true
    seed: 0
    learning_rate: 0.05
    n_train: 1
    n_samples: 4096
    n_discard_per_chain: 50
    chunk_size: 1024
    diag_shift: 0.05
    grad_clip: 0.75
    n_cycles: 7
    initial_period: 150
    period_mult: 2.0
    max_temperature: 1.0
    min_temperature: 0.0
