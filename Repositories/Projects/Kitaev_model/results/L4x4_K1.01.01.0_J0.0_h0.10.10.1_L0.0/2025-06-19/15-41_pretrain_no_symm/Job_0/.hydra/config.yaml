system:
  Lx: 4
  Ly: 4
  Kx: 1.0
  Ky: 1.0
  Kz: 1.0
  J: 0.0
  h:
    x: 0.1
    'y': 0.1
    z: 0.1
  Lambda: 0.0
  spin: 0.5
  reference_energy: -6.396
  output_dir: results/L${.Lx}x${.Ly}_K${.Kx}${.Ky}${.Kz}_J${.J}_h${.h.x}${.h.y}${.h.z}_L${.Lambda}
training:
  gpu_mesh_shape:
  - 1
  - 2
  use_model_sharding: true
  experiment_name: pretrain_no_symm
  checkpoint:
    enable: true
    save_interval: 3000
    resume_from: null
    keep_history: true
model:
  name: ViT
  model:
    num_layers: 8
    d_model: 48
    heads: 8
    patch_size: 2
    mlp_hidden_dim: 4
    param_dtype: float64
    use_symmetries: false
  training:
    use_model_sharding: true
    seed: 0
    learning_rate: 0.03
    n_train: 1
    n_samples: 4096
    n_discard_per_chain: 0
    chunk_size: 1024
    diag_shift: 0.1
    grad_clip: 0.75
    n_cycles: 8
    initial_period: 100
    period_mult: 2.0
    max_temperature: 1.0
    min_temperature: 0.0
