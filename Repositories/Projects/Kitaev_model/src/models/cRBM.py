import numpy as np
import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Union, Any
from netket.utils.types import NNInitFunc
from netket.nn import log_cosh
from jax.nn.initializers import normal

default_kernel_init = normal(stddev=0.01)

# 注意：硬编码的配置类已被移除
# 现在所有配置都从YAML文件读取 (configs/model/cRBM.yaml)
# 这确保了配置的一致性和可维护性

class cRBM(nn.Module):
    """
    相关受限玻尔兹曼机(correlated RBM)
    基于 Phys. Rev. Research 4, L012010 (2022) (arXiv:2103.05017)
    
    包含最近邻关联子的RBM，专门为Kitaev模型设计
    """
    Lx: int                                      # x方向格点数
    Ly: int                                      # y方向格点数
    param_dtype: Any = np.complex128             # 参数数据类型
    activation: Any = log_cosh                   # 激活函数
    alpha: Union[float, int] = 4                 # 隐藏单元比例
    use_hidden_bias: bool = True                 # 是否使用隐藏偏置
    use_visible_bias: bool = True                # 是否使用可见偏置
    precision: Any = None                        # 精度
    kernel_init: NNInitFunc = default_kernel_init
    hidden_bias_init: NNInitFunc = default_kernel_init
    visible_bias_init: NNInitFunc = default_kernel_init

    @nn.compact
    def __call__(self, input):
        """
        前向传播
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            对数波函数幅值
        """
        input_b = input
        l_b = jnp.size(input_b, axis=0)  # batch_size
        
        # 重塑为二维格点阵列 [batch_size, Lx, 2*Ly]
        # 这里2*Ly是因为蜂窝晶格每个单元格有2个格点
        input_c = jnp.reshape(input_b, (l_b, self.Lx, 2 * self.Ly))
        
        # 转置为 [batch_size, 2*Ly, Lx]
        input_d = jnp.transpose(input_c, (0, 2, 1))
        l_d = jnp.size(input_d, axis=0)
        
        # 重塑为 [batch_size, Ly, 2*Lx]
        input_e = jnp.reshape(input_d, (l_d, self.Ly, 2 * self.Lx))
        
        # 对最后一个维度排序
        input_f = jnp.sort(input_e, axis=-1)

        # 计算x方向的最近邻关联子
        input_c_roll = jnp.roll(input_c, -1, axis=-1)
        input_cc_roll = input_c * input_c_roll
        input_cc_roll_final = jnp.reshape(input_cc_roll, (l_b, 2 * self.Lx * self.Ly))

        # 计算y方向的最近邻关联子
        input_f_roll = jnp.roll(input_f, -1, axis=-1)
        input_ff_roll = input_f * input_f_roll
        input_ff_roll_final = jnp.reshape(input_ff_roll, (l_b, 2 * self.Lx * self.Ly))

        # 删除部分关联子以避免重复计数
        input_ff_roll_final_2 = jnp.delete(input_ff_roll_final, slice(None, None, 2), axis=1)

        # 合并所有关联子
        input_final_nn_correlators = jnp.concatenate((input_cc_roll_final, input_ff_roll_final_2), axis=-1)

        # 合并原始自旋和关联子
        input_final = jnp.concatenate((input_b, input_final_nn_correlators), axis=-1)

        # 隐藏层
        x = nn.Dense(
            name="Dense",
            features=int(self.alpha * input_final.shape[-1]),
            param_dtype=self.param_dtype,
            precision=self.precision,
            use_bias=self.use_hidden_bias,
            kernel_init=self.kernel_init,
            bias_init=self.hidden_bias_init,
        )(input_final)
        
        x = self.activation(x)
        x = jnp.sum(x, axis=-1)

        # 可见偏置
        if self.use_visible_bias:
            v_bias = self.param(
                "visible_bias",
                self.visible_bias_init,
                (input_final.shape[-1],),
                self.param_dtype,
            )
            out_bias = jnp.dot(input_final, v_bias)
            return x + out_bias
        else:
            return x 