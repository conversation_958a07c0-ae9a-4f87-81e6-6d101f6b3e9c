import numpy as np
import jax
import jax.numpy as jnp
import flax.linen as nn
from netket.nn import log_cosh

# 注意：硬编码的配置类已被移除
# 现在所有配置都从YAML文件读取 (configs/model/CTWF.yaml)
# 这确保了配置的一致性和可维护性

# ----------------------------------------------
# 1. Convolutional Embedding：将2D自旋构型嵌入为 Token 序列
# ----------------------------------------------
class ConvEmbedding(nn.Module):
    d_model: int      # 输出通道数（嵌入维度）
    patch_size: int   # patch 尺寸

    def setup(self):
        self.conv = nn.Conv(
            features=self.d_model,
            kernel_size=(self.patch_size, self.patch_size),
            strides=(self.patch_size, self.patch_size),
            padding="VALID",
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=jnp.float32,
            dtype=jnp.float32
        )

    def __call__(self, x):
        # x shape: [batch, L, L, channels]，这里 channels=1
        x = self.conv(x)  # 输出 shape: [batch, L//patch, L//patch, d_model]
        batch, H, W, _ = x.shape
        tokens = x.reshape(batch, H * W, self.d_model)
        return tokens

# ----------------------------------------------
# 2. ConvUnit：利用卷积捕获局部特征
# ----------------------------------------------
class ConvUnit(nn.Module):
    d_model: int

    @nn.compact
    def __call__(self, x):
        # 输入 x shape: [batch, n_tokens, d_model]
        batch, n_tokens, d_model = x.shape
        grid = int(np.sqrt(n_tokens))
        x_grid = x.reshape(batch, grid, grid, d_model)
        conv_out = nn.Conv(
            features=self.d_model,
            kernel_size=(3, 3),
            padding="SAME",
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=jnp.float32,
            dtype=jnp.float32
        )(x_grid)
        conv_out = nn.gelu(conv_out)
        out = conv_out.reshape(batch, n_tokens, self.d_model)
        return out

# ----------------------------------------------
# 3. CT_MHSA：带相对位置编码的多头自注意力模块
# ----------------------------------------------
class CT_MHSA(nn.Module):
    d_model: int
    h: int         # 注意力头数
    n_tokens: int  # Token 数（应为完全平方数）
    
    def setup(self):
        self.d_head = self.d_model // self.h
        self.WQ = nn.Dense(self.d_model,
                           kernel_init=nn.initializers.xavier_uniform(),
                           param_dtype=jnp.float32,
                           dtype=jnp.float32)
        self.WK = nn.Dense(self.d_model,
                           kernel_init=nn.initializers.xavier_uniform(),
                           param_dtype=jnp.float32,
                           dtype=jnp.float32)
        self.WV = nn.Dense(self.d_model,
                           kernel_init=nn.initializers.xavier_uniform(),
                           param_dtype=jnp.float32,
                           dtype=jnp.float32)
        # Relative positional encoding, shape: [h, n_tokens, n_tokens]
        self.P = self.param("RPE", nn.initializers.xavier_uniform(),
                            (self.h, self.n_tokens, self.n_tokens), jnp.float32)
        self.WO = nn.Dense(self.d_model,
                           kernel_init=nn.initializers.xavier_uniform(),
                           param_dtype=jnp.float32,
                           dtype=jnp.float32)

    def __call__(self, x):
        batch, n, _ = x.shape
        Q = self.WQ(x)
        K = self.WK(x)
        V = self.WV(x)
        Q = Q.reshape(batch, n, self.h, self.d_head).transpose(0, 2, 1, 3)
        K = K.reshape(batch, n, self.h, self.d_head).transpose(0, 2, 1, 3)
        V = V.reshape(batch, n, self.h, self.d_head).transpose(0, 2, 1, 3)
        scale = np.sqrt(self.d_head)
        attn_scores = jnp.einsum('bhid,bhjd->bhij', Q, K) / scale
        attn_scores = attn_scores + self.P
        attn = nn.softmax(attn_scores, axis=-1)
        attn_out = jnp.einsum('bhij,bhjd->bhid', attn, V)
        attn_out = attn_out.transpose(0, 2, 1, 3).reshape(batch, n, self.d_model)
        out = self.WO(attn_out)
        return out

# ----------------------------------------------
# 4. IRFFN：Inverted Residual Feed-Forward Network
# ----------------------------------------------
class IRFFN(nn.Module):
    d_model: int
    expansion_factor: int = 2

    @nn.compact
    def __call__(self, x):
        batch, n_tokens, _ = x.shape
        expanded_dim = self.expansion_factor * self.d_model
        hidden = nn.Dense(expanded_dim,
                          kernel_init=nn.initializers.xavier_uniform(),
                          param_dtype=jnp.float32,
                          dtype=jnp.float32)(x)
        hidden = nn.gelu(hidden)
        grid = int(np.sqrt(n_tokens))
        hidden = hidden.reshape(batch, grid, grid, expanded_dim)
        hidden = nn.Conv(
            features=expanded_dim,
            kernel_size=(3, 3),
            padding="SAME",
            feature_group_count=expanded_dim,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=jnp.float32,
            dtype=jnp.float32
        )(hidden)
        hidden = nn.gelu(hidden)
        hidden = hidden.reshape(batch, n_tokens, expanded_dim)
        out = nn.Dense(self.d_model,
                       kernel_init=nn.initializers.xavier_uniform(),
                       param_dtype=jnp.float32,
                       dtype=jnp.float32)(hidden)
        return out

# ----------------------------------------------
# 5. TransformerBlock：标准Transformer块（MHSA + IRFFN）
# ----------------------------------------------
class TransformerBlock(nn.Module):
    d_model: int    # 模型维度
    h: int          # 注意力头数
    n_tokens: int   # Token数量

    @nn.compact
    def __call__(self, x):
        # 第一个子层：卷积单元 + 多头自注意力
        h1 = nn.LayerNorm(dtype=jnp.float32, param_dtype=jnp.float32)(x)
        h1 = ConvUnit(self.d_model)(h1)
        h1 = nn.LayerNorm(dtype=jnp.float32, param_dtype=jnp.float32)(h1)
        h1 = CT_MHSA(self.d_model, self.h, self.n_tokens)(h1)
        x = x + h1  # 残差连接
        
        # 第二个子层：前馈网络
        h2 = nn.LayerNorm(dtype=jnp.float32, param_dtype=jnp.float32)(x)
        h2 = IRFFN(self.d_model)(h2)
        x = x + h2  # 残差连接
        
        return x

# ----------------------------------------------
# 8. OutputHead：汇聚 Token 后输出复数波函数振幅
# ----------------------------------------------
class OutputHead(nn.Module):
    d_model: int

    def setup(self):
        self.out_layer_norm = nn.LayerNorm(dtype=jnp.float32, param_dtype=jnp.float32)
        self.norm0 = nn.LayerNorm(use_scale=True, use_bias=True,
                                  dtype=jnp.float32, param_dtype=jnp.float32)
        self.norm1 = nn.LayerNorm(use_scale=True, use_bias=True,
                                  dtype=jnp.float32, param_dtype=jnp.float32)
        self.output_layer0 = nn.Dense(self.d_model,
                                      kernel_init=nn.initializers.xavier_uniform(),
                                      bias_init=nn.initializers.zeros,
                                      param_dtype=jnp.float32,
                                      dtype=jnp.float32)
        self.output_layer1 = nn.Dense(self.d_model,
                                      kernel_init=nn.initializers.xavier_uniform(),
                                      bias_init=nn.initializers.zeros,
                                      param_dtype=jnp.float32,
                                      dtype=jnp.float32)

    def __call__(self, x):
        z = self.out_layer_norm(x.sum(axis=1))
        amp = self.norm0(self.output_layer0(z))
        sign = self.norm1(self.output_layer1(z))
        out = amp + 1j * sign
        return jnp.sum(log_cosh(out), axis=-1)

# ----------------------------------------------
# 9. CTWFNQS：针对Kitaev模型优化的主要架构
# ----------------------------------------------
class CTWFNQS(nn.Module):
    """
    Convolutional Transformer Wavefunctions for Neural Quantum States
    
    专为2D量子多体系统设计的神经量子态模型，特别适用于Kitaev模型
    
    Architecture flow:
    Input → 2D reshape → Patch Embedding → TransformerBlocks → Output Head → log ψ
    
    Args:
        num_layers: Transformer层数
        d_model: 模型维度
        heads: 注意力头数
        n_sites: 系统中的格点数
        patch_size: patch 尺寸
        param_dtype: 参数数据类型
    """
    num_layers: int     # Transformer层数（例如4～8层）
    d_model: int        # 模型维度（建议32～48）
    heads: int          # 注意力头数（例如4～8）
    n_sites: int        # 总格点数，例如 L x L，n_sites=L^2
    patch_size: int     # patch 尺寸
    param_dtype: jnp.dtype = jnp.float32

    def setup(self):
        # 对于蜂窝晶格，需要特殊处理
        # n_sites 对应蜂窝晶格的总格点数（通常为 2*Lx*Ly）
        # 我们需要将其重塑为正方形网格进行处理
        
        # 计算最接近正方形的边长
        self.L = int(np.sqrt(self.n_sites))
        if self.L * self.L != self.n_sites:
            # 如果不是完全平方数，向上取整并用零填充
            self.L = int(np.ceil(np.sqrt(self.n_sites)))
        
        # 每边 token 数 = L // patch_size，token 总数
        self.n_tokens_side = self.L // self.patch_size
        self.n_tokens = self.n_tokens_side * self.n_tokens_side
        
        # 确保 heads 能整除 d_model
        if self.d_model % self.heads != 0:
            raise ValueError(f"d_model ({self.d_model}) 必须能被 heads ({self.heads}) 整除")
        
        self.embedding = ConvEmbedding(d_model=self.d_model, patch_size=self.patch_size)
        # 堆叠多个Transformer块
        self.transformer_blocks = [TransformerBlock(d_model=self.d_model, h=self.heads, n_tokens=self.n_tokens)
                                   for _ in range(self.num_layers)]
        self.output = OutputHead(d_model=self.d_model)

    def __call__(self, spins):
        """
        前向传播
        
        Args:
            spins: [batch, n_sites] 输入自旋构型
        Returns:
            对数波函数值
        """
        # spins: [batch, n_sites]，一维数组后 reshape 成 [batch, L, L, 1]
        x = jnp.atleast_2d(spins)
        batch = x.shape[0]
        
        # 如果 n_sites 不是完全平方数，需要填充到正方形
        if x.shape[1] != self.L * self.L:
            # 用零填充到 L*L
            padding_size = self.L * self.L - x.shape[1]
            x = jnp.pad(x, ((0, 0), (0, padding_size)), mode='constant', constant_values=0)
        
        x = x.reshape(batch, self.L, self.L, 1)
        x = self.embedding(x)  # [batch, n_tokens, d_model]
        
        for block in self.transformer_blocks:
            x = block(x)
        
        out = self.output(x)
        return out 