import numpy as np
import jax
import jax.numpy as jnp
import flax.linen as nn
from netket.nn import log_cosh
from typing import Optional, Tuple, Sequence
import netket as nk

# 图神经网络核心模块
class GraphConvUnit(nn.Module):
    """
    图卷积单元 - 基于消息传递的图卷积层
    专门为蜂窝晶格的邻接关系设计
    """
    d_model: int
    use_edge_features: bool = True
    
    @nn.compact
    def __call__(self, node_feats, edge_index, edge_attr=None):
        """
        Args:
            node_feats: [batch, n_nodes, d_model] 节点特征
            edge_index: [2, n_edges] 边索引，每列表示一条边 [src_node, dst_node]
            edge_attr: [batch, n_edges, edge_dim] 边特征（Kitaev键类型：X/Y/Z）
        """
        batch_size, n_nodes, _ = node_feats.shape
        n_edges = edge_index.shape[1]
        
        # 节点特征变换
        h_nodes = nn.Dense(self.d_model, name='node_transform')(node_feats)
        
        # 边特征处理（对于Kitaev模型，边有不同类型：X, Y, Z键）
        if self.use_edge_features and edge_attr is not None:
            h_edges = nn.Dense(self.d_model, name='edge_transform')(edge_attr)
        else:
            h_edges = jnp.zeros((batch_size, n_edges, self.d_model))
        
        # 消息传递：从源节点收集消息
        src_nodes, dst_nodes = edge_index
        
        # 获取源节点和目标节点的特征
        # src_feats: [batch, n_edges, d_model]
        src_feats = h_nodes[:, src_nodes, :]
        
        # 消息计算：节点特征 + 边特征
        messages = src_feats + h_edges
        messages = nn.gelu(messages)
        
        # 聚合消息到目标节点
        # 使用segment_sum在目标节点上聚合消息
        # messages: [batch, n_edges, d_model], dst_nodes: [n_edges]
        # 需要为每个batch单独处理
        def aggregate_batch(batch_messages):
            return jax.ops.segment_sum(
                batch_messages,  # [n_edges, d_model]
                dst_nodes,       # [n_edges]
                num_segments=n_nodes,
                indices_are_sorted=False
            )  # -> [n_nodes, d_model]
        
        aggregated = jax.vmap(aggregate_batch)(messages)  # [batch, n_nodes, d_model]
        
        # 更新节点特征：原始特征 + 聚合消息
        updated_feats = h_nodes + aggregated
        updated_feats = nn.gelu(updated_feats)
        
        return updated_feats

class GraphAttentionUnit(nn.Module):
    """
    边敏感的图注意力单元 - 结合图结构和边特征的注意力机制
    将Kitaev键类型（X/Y/Z键）直接融入注意力分数计算
    """
    d_model: int
    heads: int
    max_path_length: int = 10  # 图中最大路径长度
    edge_types: int = 3  # Kitaev键类型数量（X, Y, Z）
    attention_cutoff: int = 4  # 注意力截断距离（默认4跳）
    
    def setup(self):
        self.d_head = self.d_model // self.heads
        assert self.d_model % self.heads == 0
        
        # Q, K, V变换
        self.WQ = nn.Dense(self.d_model, name='query_proj')
        self.WK = nn.Dense(self.d_model, name='key_proj') 
        self.WV = nn.Dense(self.d_model, name='value_proj')
        
        # 基于最短路径距离的位置编码
        self.distance_embedding = nn.Embed(
            num_embeddings=self.max_path_length + 1,
            features=self.heads,
            name='distance_embed'
        )
        
        # 边类型嵌入 - 为每种Kitaev键类型学习不同的注意力偏置
        self.edge_type_embedding = nn.Embed(
            num_embeddings=self.edge_types,
            features=self.heads,
            name='edge_type_embed'
        )
        
        # 边感知的注意力投影层
        # 用于将边特征直接融入注意力计算
        self.edge_attention_proj = nn.Dense(
            self.heads, 
            name='edge_attention_proj'
        )
        
        self.output_proj = nn.Dense(self.d_model, name='output_proj')
    
    def compute_edge_aware_attention_bias(self, edge_index, edge_attr, n_nodes):
        """
        计算边敏感的注意力偏置矩阵
        
        Args:
            edge_index: [2, n_edges] 边索引
            edge_attr: [batch, n_edges, 3] 边特征（one-hot编码的键类型）
            n_nodes: 节点数量
            
        Returns:
            edge_bias: [batch, heads, n_nodes, n_nodes] 边敏感的注意力偏置
        """
        batch_size = edge_attr.shape[0]
        src_nodes, dst_nodes = edge_index
        
        # 将one-hot编码的边特征转换为键类型索引
        edge_types = jnp.argmax(edge_attr, axis=-1)  # [batch, n_edges]
        
        # 获取边类型的注意力偏置
        # edge_type_bias: [batch, n_edges, heads]
        edge_type_bias = jax.vmap(lambda types: self.edge_type_embedding(types))(edge_types)
        
        # 初始化偏置矩阵
        edge_bias = jnp.zeros((batch_size, self.heads, n_nodes, n_nodes))
        
        # 为每个batch填充边偏置
        def fill_edge_bias_batch(bias, types_bias):
            # 使用at[].set()来设置边的偏置值
            # 同时处理双向边（无向图）
            bias = bias.at[:, src_nodes, dst_nodes].set(types_bias.T)
            bias = bias.at[:, dst_nodes, src_nodes].set(types_bias.T)
            return bias
        
        edge_bias = jax.vmap(fill_edge_bias_batch)(edge_bias, edge_type_bias)
        
        return edge_bias
    
    def __call__(self, node_feats, edge_index, edge_attr, distance_matrix, adjacency_mask):
        """
        Args:
            node_feats: [batch, n_nodes, d_model]
            edge_index: [2, n_edges] 边索引
            edge_attr: [batch, n_edges, 3] 边特征（Kitaev键类型的one-hot编码）
            distance_matrix: [n_nodes, n_nodes] 最短路径距离矩阵
            adjacency_mask: [n_nodes, n_nodes] 邻接掩码
        """
        batch_size, n_nodes, _ = node_feats.shape
        
        # 计算Q, K, V
        Q = self.WQ(node_feats).reshape(batch_size, n_nodes, self.heads, self.d_head)
        K = self.WK(node_feats).reshape(batch_size, n_nodes, self.heads, self.d_head)
        V = self.WV(node_feats).reshape(batch_size, n_nodes, self.heads, self.d_head)
        
        # 转置以便计算注意力
        Q = Q.transpose(0, 2, 1, 3)  # [batch, heads, n_nodes, d_head]
        K = K.transpose(0, 2, 1, 3)
        V = V.transpose(0, 2, 1, 3)
        
        # 标准注意力分数计算
        attn_scores = jnp.einsum('bhid,bhjd->bhij', Q, K) / jnp.sqrt(self.d_head)
        
        # 添加基于距离的位置偏置
        distance_bias = self.distance_embedding(distance_matrix).transpose(2, 0, 1)
        attn_scores = attn_scores + distance_bias[None, :, :, :]
        
        # 添加边敏感的注意力偏置 - 核心创新
        edge_bias = self.compute_edge_aware_attention_bias(edge_index, edge_attr, n_nodes)
        attn_scores = attn_scores + edge_bias
        
        # 应用邻接掩码 - 扩展到多跳邻居
        # 考虑更长的路径以捕捉Kitaev模型的长程关联
        extended_mask = (distance_matrix <= self.attention_cutoff).astype(jnp.float32)
        attn_scores = jnp.where(
            extended_mask[None, None, :, :] == 1.0,
            attn_scores,
            jnp.full_like(attn_scores, -1e9)
        )
        
        # Softmax注意力权重
        attn_weights = nn.softmax(attn_scores, axis=-1)
        
        # 加权聚合
        attn_out = jnp.einsum('bhij,bhjd->bhid', attn_weights, V)
        attn_out = attn_out.transpose(0, 2, 1, 3).reshape(batch_size, n_nodes, self.d_model)
        
        # 输出投影
        output = self.output_proj(attn_out)
        return output

class GNNTransformerBlock(nn.Module):
    """
    GNN-Transformer混合块 - 边敏感版本
    先用图卷积提取局域特征，再用边敏感的图注意力建立长程关联
    """
    d_model: int
    heads: int
    max_path_length: int = 10
    edge_types: int = 3  # Kitaev键类型数量
    attention_cutoff: int = 4  # 注意力截断距离
    
    @nn.compact
    def __call__(self, node_feats, edge_index, edge_attr, distance_matrix, adjacency_mask):
        # 第一个子层：图卷积 + LayerNorm + 残差连接
        h1 = nn.LayerNorm()(node_feats)
        h1 = GraphConvUnit(d_model=self.d_model)(h1, edge_index, edge_attr)
        node_feats = node_feats + h1
        
        # 第二个子层：边敏感的图注意力 + LayerNorm + 残差连接  
        h2 = nn.LayerNorm()(node_feats)
        h2 = GraphAttentionUnit(
            d_model=self.d_model, 
            heads=self.heads,
            max_path_length=self.max_path_length,
            edge_types=self.edge_types,
            attention_cutoff=self.attention_cutoff
        )(h2, edge_index, edge_attr, distance_matrix, adjacency_mask)
        node_feats = node_feats + h2
        
        # 第三个子层：前馈网络 + LayerNorm + 残差连接
        h3 = nn.LayerNorm()(node_feats)
        h3 = nn.Dense(4 * self.d_model)(h3)
        h3 = nn.gelu(h3)
        h3 = nn.Dense(self.d_model)(h3)
        node_feats = node_feats + h3
        
        return node_feats

class GraphEmbedding(nn.Module):
    """
    图嵌入层 - 将原始自旋值嵌入到高维特征空间
    """
    d_model: int
    
    @nn.compact
    def __call__(self, spins):
        """
        Args:
            spins: [batch, n_sites] 原始自旋构型 (+1/-1)
        Returns:
            node_feats: [batch, n_sites, d_model] 嵌入后的节点特征
        """
        # 简单的线性嵌入 + 位置编码
        spins_expanded = jnp.expand_dims(spins, -1)  # [batch, n_sites, 1]
        
        # 可学习的嵌入
        node_feats = nn.Dense(self.d_model)(spins_expanded.astype(jnp.float32))
        
        # 可选：添加可学习的节点位置编码
        n_sites = spins.shape[-1]
        pos_encoding = self.param(
            'pos_encoding',
            nn.initializers.normal(stddev=0.02),
            (n_sites, self.d_model)
        )
        node_feats = node_feats + pos_encoding[None, :, :]
        
        return node_feats

class OutputHead(nn.Module):
    """
    输出头 - 将节点特征聚合为最终的对数波函数值
    """
    d_model: int
    
    def setup(self):
        self.norm = nn.LayerNorm()
        self.output_layers = [
            nn.Dense(self.d_model),
            nn.Dense(self.d_model)
        ]
    
    def __call__(self, node_feats):
        """
        Args:
            node_feats: [batch, n_sites, d_model]
        Returns:
            log_psi: [batch,] 对数波函数值
        """
        # 全局平均池化
        global_feat = jnp.mean(node_feats, axis=1)  # [batch, d_model]
        
        # 归一化
        global_feat = self.norm(global_feat)
        
        # 分别计算振幅和相位
        amp = self.output_layers[0](global_feat)
        phase = self.output_layers[1](global_feat)
        
        # 组合为复数输出
        complex_out = amp + 1j * phase
        
        # 应用log_cosh激活函数并求和
        log_psi = jnp.sum(log_cosh(complex_out), axis=-1)
        
        return log_psi

def extract_graph_structure(lattice):
    """
    从NetKet晶格对象中提取图结构信息
    专门为Kitaev蜂窝晶格优化，正确处理X/Y/Z键类型
    
    Args:
        lattice: NetKet晶格对象
        
    Returns:
        edge_index: [2, n_edges] 边索引
        edge_attr: [n_edges, 3] 边属性（Kitaev键类型的one-hot编码）
        distance_matrix: [n_nodes, n_nodes] 最短路径距离矩阵
        adjacency_mask: [n_nodes, n_nodes] 邻接掩码
    """
    # 获取边列表
    edges = list(lattice.edges())
    n_nodes = lattice.n_nodes
    n_edges = len(edges)
    
    # 构建边索引矩阵
    edge_index = np.zeros((2, n_edges), dtype=np.int32)
    edge_attr = np.zeros((n_edges, 3), dtype=np.float32)  # 3种Kitaev键类型
    
    # 邻接矩阵（用于计算最短路径）
    adjacency_matrix = np.zeros((n_nodes, n_nodes), dtype=np.int32)
    
    for i, edge in enumerate(edges):
        src, dst = edge[0], edge[1]
        edge_index[0, i] = src
        edge_index[1, i] = dst
        
        # 添加对称边（无向图）
        adjacency_matrix[src, dst] = 1
        adjacency_matrix[dst, src] = 1
        
        # 正确提取Kitaev键类型
        # NetKet的边格式通常是 (src, dst, displacement_vector, color)
        if len(edge) >= 4:
            # 直接从边的颜色信息获取键类型
            bond_type = int(edge[3]) % 3  # 确保在0-2范围内
        elif hasattr(lattice, 'edge_colors'):
            # 从晶格的边颜色属性获取
            try:
                colors = lattice.edge_colors()
                bond_type = int(colors[i]) % 3
            except:
                bond_type = i % 3  # 备用方案
        else:
            # 基于边的位移向量推断键类型（几何方法）
            if len(edge) >= 3:
                displacement = edge[2]
                # 根据位移向量的方向确定键类型
                # 这是基于蜂窝晶格几何结构的启发式方法
                if abs(displacement[0]) > abs(displacement[1]):
                    # 主要沿x方向 → X键
                    bond_type = 0
                elif displacement[1] > 0:
                    # 向上倾斜 → Y键  
                    bond_type = 1
                else:
                    # 向下倾斜 → Z键
                    bond_type = 2
            else:
                # 最后的备用方案：循环分配
                bond_type = i % 3
        
        edge_attr[i, bond_type] = 1.0
    
    # 计算最短路径距离矩阵（Floyd-Warshall算法）
    distance_matrix = np.full((n_nodes, n_nodes), np.inf, dtype=np.float32)
    np.fill_diagonal(distance_matrix, 0)
    
    # 初始化直接相邻的距离为1
    distance_matrix[adjacency_matrix == 1] = 1
    
    # Floyd-Warshall算法
    for k in range(n_nodes):
        for i in range(n_nodes):
            for j in range(n_nodes):
                distance_matrix[i, j] = min(
                    distance_matrix[i, j],
                    distance_matrix[i, k] + distance_matrix[k, j]
                )
    
    # 将无穷大替换为一个大的有限值
    distance_matrix = np.where(
        distance_matrix == np.inf,
        10,  # max_path_length
        distance_matrix
    ).astype(np.int32)
    
    # 转换为JAX数组
    edge_index = jnp.array(edge_index)
    edge_attr = jnp.array(edge_attr)
    distance_matrix = jnp.array(distance_matrix)
    adjacency_mask = jnp.array(adjacency_matrix, dtype=jnp.float32)
    
    return edge_index, edge_attr, distance_matrix, adjacency_mask

class GNNViTNQS(nn.Module):
    """
    Graph Neural Network + Vision Transformer 混合神经量子态模型 - 边敏感版本
    
    专门为蜂窝晶格上的Kitaev模型设计，直接在图拓扑上操作，
    避免了CTWF中的方格映射问题。现在支持边敏感的注意力机制。
    
    Architecture:
    Input Spins → Graph Embedding → Edge-Aware GNN-Transformer Blocks → Output Head → log ψ
    
    Args:
        num_layers: GNN-Transformer层数
        d_model: 模型维度
        heads: 注意力头数  
        lattice: NetKet晶格对象（用于提取图结构）
        max_path_length: 图中考虑的最大路径长度
        edge_types: Kitaev键类型数量（默认3：X/Y/Z键）
        attention_cutoff: 注意力截断距离（多少跳内的节点参与注意力计算）
        param_dtype: 参数数据类型
    """
    num_layers: int
    d_model: int  
    heads: int
    lattice: Optional[object] = None  # NetKet晶格对象
    max_path_length: int = 10
    edge_types: int = 3  # Kitaev键类型数量（X, Y, Z）
    attention_cutoff: int = 4  # 注意力截断距离
    param_dtype: jnp.dtype = jnp.float32
    
    def setup(self):
        if self.lattice is None:
            raise ValueError("必须提供NetKet晶格对象来提取图结构")
            
        # 提取图结构信息
        self.edge_index, self.edge_attr, self.distance_matrix, self.adjacency_mask = \
            extract_graph_structure(self.lattice)
        
        # 模型组件
        self.embedding = GraphEmbedding(d_model=self.d_model)
        
        self.gnn_blocks = [
            GNNTransformerBlock(
                d_model=self.d_model,
                heads=self.heads,
                max_path_length=self.max_path_length,
                edge_types=self.edge_types,
                attention_cutoff=self.attention_cutoff
            ) for _ in range(self.num_layers)
        ]
        
        self.output_head = OutputHead(d_model=self.d_model)
    
    def __call__(self, spins):
        """
        前向传播
        
        Args:
            spins: [batch, n_sites] 输入自旋构型
            
        Returns:
            log_psi: [batch,] 对数波函数值
        """
        # 确保输入是二维的
        spins = jnp.atleast_2d(spins)
        batch_size = spins.shape[0]
        
        # 图嵌入
        node_feats = self.embedding(spins)  # [batch, n_sites, d_model]
        
        # 扩展边属性到batch维度
        batch_edge_attr = jnp.broadcast_to(
            self.edge_attr[None, :, :],
            (batch_size, self.edge_attr.shape[0], self.edge_attr.shape[1])
        )
        
        # 通过GNN-Transformer块
        for block in self.gnn_blocks:
            node_feats = block(
                node_feats, 
                self.edge_index, 
                batch_edge_attr,
                self.distance_matrix, 
                self.adjacency_mask
            )
        
        # 输出头
        log_psi = self.output_head(node_feats)
        
        return log_psi 