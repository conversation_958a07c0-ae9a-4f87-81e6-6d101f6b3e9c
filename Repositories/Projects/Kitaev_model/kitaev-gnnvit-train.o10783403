Job start at: Fri Jun 20 19:01:32 +08 2025
Running on node: x1000c1s6b0n1
GPU Information:
Fri Jun 20 19:01:32 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:41:00.0 Off |                    0 |
| N/A   43C    P0              56W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-40GB          On  | 00000000:C1:00.0 Off |                    0 |
| N/A   41C    P0              52W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
==================== 预训练阶段 (无对称性) ====================
Starting pretraining without symmetries...
Checkpoint enabled with interval: 3000
Using model: GNNViT
Experiment name: gnnvit_pretrain
13:4: not a valid test operator: (
13:4: not a valid test operator: 535.154.05
[2025-06-20 11:02:02,798][HYDRA] Launching 1 jobs locally
[2025-06-20 11:02:02,798][HYDRA] 	#0 : model=GNNViT system=kitaev training.experiment_name=gnnvit_pretrain training.gpu_mesh_shape=[1,2] training.checkpoint.enable=True training.checkpoint.save_interval=3000 training.checkpoint.keep_history=True
/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/src/physics/kitaev.py:52: UndeclaredSpinOderingWarning: 
You have not explicitly specified the spin ordering for the Hilbert space.
The default behaviour is currently `-1=↑, 1=↓`, but it will be changed 1st january 2025 to `1=↑, -1=↓`.

- To maintain the current behaviour in the future, specify `inverted_ordering=True` (this
    allows you to load NN parameters you have saved in the past)
- To opt-in today in the future default, specify `inverted_ordering=False` (so your code will
    work without changes in the future)

If you do not care about this warning, you can silence it by setting the environment variable
`NETKET_SPIN_ORDERING_WARNING=0` or by executing `nk.config.netket_spin_ordering_warning = False`

This warning will be shown once per day during interactive sessions, and always in scripts and MPI/SLURM jobs unless silenced.


-------------------------------------------------------
For more detailed informations, visit the following link:
	 https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.UndeclaredSpinOderingWarning.html
or the list of all common errors and warnings at
	 https://netket.readthedocs.io/en/latest/api/errors.html
-------------------------------------------------------

  hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-06-20 20:12:52.191299:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 10783403.pbs101
	Project: 12004256
	Exit Status: 271
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(32), Used(32)
	CPU Time Used: 02:20:01
	Memory: Requested(220gb), Used(2118836kb)
	Vmem Used: 340580340kb
	Walltime: Requested(23:00:00), Used(01:11:28)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (x1000c1s6b0n1:ngpus=2:ncpus=32:mem=230686720kb)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	GPU Duration: 1.19hrs
	GPU Power Consumed: 359.59W
	GPU Max GPU Memory Used: 62.73GB
	Memory Throughput Rate (Average): x1000c1s6b0n1:(gpu1:97%+gpu3:97%)
	Memory Throughput Rate (Max): x1000c1s6b0n1:(gpu1:100%+gpu3:100%)
	Memory Throughput Rate (Min): x1000c1s6b0n1:(gpu1:0%+gpu3:0%)
	GPU SM Utilization (Average): x1000c1s6b0n1:(gpu1:97%+gpu3:97%)
	GPU SM Utilization (Max): x1000c1s6b0n1:(gpu1:100%+gpu3:100%)
	GPU SM Utilization (Min): x1000c1s6b0n1:(gpu1:0%+gpu3:0%)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Warning: None
GPU application profile: High
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

