#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试改进后的Shastry-Sutherland训练系统
"""

import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
# 不强制使用GPU，让JAX自动选择可用的后端

def test_basic_functionality():
    """测试基本功能"""
    print("="*50)
    print("测试1: 基本SSRunner功能")
    print("="*50)
    
    try:
        from src.runner import SSRunner
        
        # 创建一个简单的配置
        model_config = {
            'num_features': 4,
            'num_layers': 4
        }
        
        training_config = {
            'learning_rate': 0.015,
            'n_cycles': 1,
            'initial_period': 10,  # 使用较小的周期进行测试
            'period_mult': 2.0,
            'max_temperature': 1.0,
            'min_temperature': 0.01,
            'n_samples': 256,      # 使用较小的样本数进行测试
            'n_discard_per_chain': 0,
            'chunk_size': 128,
            'diag_shift': 0.20,
            'grad_clip': 1.0,
            'n_iters': 20          # 使用较少的迭代次数进行测试
        }
        
        checkpoint_config = {
            'enable': False,       # 暂时禁用checkpoint进行基本测试
            'save_interval': 10,
            'resume_from': None,
            'keep_history': True
        }
        
        # 创建运行器
        runner = SSRunner(
            L=2,                   # 使用较小的晶格进行测试
            J1=1.0,
            J2=0.5,
            model_class="GCNN",
            model_config=model_config,
            training_config=training_config,
            reference_energy=None,
            checkpoint_config=checkpoint_config
        )
        
        print("✓ SSRunner创建成功")
        
        # 设置模型
        runner.setup_model()
        print("✓ 模型设置成功")
        
        # 运行训练（短时间）
        runner.run()
        print("✓ 训练运行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_checkpoint_functionality():
    """测试checkpoint功能"""
    print("\n" + "="*50)
    print("测试2: Checkpoint功能")
    print("="*50)
    
    try:
        from src.runner import SSRunner
        import tempfile
        import shutil
        
        # 创建临时目录用于测试
        temp_dir = tempfile.mkdtemp()
        print(f"使用临时目录: {temp_dir}")
        
        # 创建配置，启用checkpoint
        model_config = {
            'num_features': 4,
            'num_layers': 4
        }
        
        training_config = {
            'learning_rate': 0.015,
            'n_cycles': 1,
            'initial_period': 5,
            'period_mult': 2.0,
            'max_temperature': 1.0,
            'min_temperature': 0.01,
            'n_samples': 256,
            'n_discard_per_chain': 0,
            'chunk_size': 128,
            'diag_shift': 0.20,
            'grad_clip': 1.0,
            'n_iters': 10
        }
        
        checkpoint_config = {
            'enable': True,
            'save_interval': 5,    # 每5步保存一次
            'resume_from': None,
            'keep_history': True
        }
        
        # 第一次训练
        runner1 = SSRunner(
            L=2,
            J1=1.0,
            J2=0.5,
            model_class="GCNN",
            model_config=model_config,
            training_config=training_config,
            reference_energy=None,
            output_dir=temp_dir,
            checkpoint_config=checkpoint_config
        )
        
        runner1.setup_model()
        runner1.run()
        print("✓ 第一次训练完成")
        
        # 检查checkpoint文件是否存在
        checkpoint_dir = os.path.join(temp_dir, "checkpoints")
        if os.path.exists(checkpoint_dir):
            checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pkl')]
            if checkpoint_files:
                print(f"✓ Checkpoint文件已创建: {checkpoint_files}")
            else:
                print("⚠️  未找到checkpoint文件")
        else:
            print("⚠️  Checkpoint目录不存在")
        
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print("✓ 临时目录已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ Checkpoint功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试改进后的Shastry-Sutherland训练系统")
    
    # 检查JAX和NetKet是否可用
    try:
        import jax
        import netket as nk
        print(f"✓ JAX版本: {jax.__version__}")
        print(f"✓ NetKet版本: {nk.__version__}")
        print(f"✓ 可用设备: {jax.devices()}")
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {e}")
        return
    
    # 运行测试
    test_results = []
    
    # 测试1: 基本功能
    test_results.append(test_basic_functionality())
    
    # 测试2: Checkpoint功能
    test_results.append(test_checkpoint_functionality())
    
    # 总结
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！新的训练系统工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
