# Shastry-Sutherland模型训练系统改进总结

## 概述

参考Kitaev_model库，对Shastry-Sutherland_model进行了全面改进，主要包括：

1. **热重启余弦退火机制**
2. **Checkpoint机制**
3. **面向对象的训练框架**
4. **配置文件支持**
5. **训练文件加载功能**

## 主要改进

### 1. 更新CustomFreeEnergyVMC_SRt类

**文件**: `src/models/free_energy.py`

**改进内容**:
- 实现了与Kitaev_model相同的热重启余弦退火温度调度
- 添加了checkpoint回调支持
- 改进了NaN检测和处理机制
- 支持可配置的退火参数（初始周期、周期倍数、温度范围等）

**关键特性**:
```python
# 热重启参数
initial_period=100      # 初始周期长度
period_mult=2.0         # 周期倍数
max_temperature=1.0     # 最大温度
min_temperature=0.01    # 最小温度

# Checkpoint支持
checkpoint_callback=callback_function
checkpoint_interval=500
```

### 2. 创建SSRunner类

**文件**: `src/runner.py`

**改进内容**:
- 参考KitaevRunner，创建了面向对象的训练接口
- 支持checkpoint保存和恢复
- 统一的配置管理
- 详细的训练信息记录
- 自动的模型和系统设置

**主要功能**:
- 模型设置和初始化
- 训练过程管理
- Checkpoint保存/加载
- 详细的日志记录
- 最终状态保存

### 3. 更新训练脚本

**文件**: `scripts/train.py` (更新), `scripts/train_new.py` (新增)

**改进内容**:
- 使用新的SSRunner框架
- 支持命令行参数配置
- 简化的训练流程
- 更好的错误处理

### 4. 添加配置文件支持

**新增文件**:
- `configs/config.yaml` - 主配置文件
- `configs/system/shastry_sutherland.yaml` - 系统参数配置
- `configs/training/default.yaml` - 训练参数配置
- `configs/model/gcnn.yaml` - 模型配置
- `scripts/train_hydra.py` - 基于Hydra的训练脚本

**特性**:
- 分层配置管理
- 支持Hydra框架（可选）
- 灵活的参数覆盖

### 5. 测试和验证

**文件**: `test_new_system.py`

**测试内容**:
- 基本SSRunner功能测试
- Checkpoint功能测试
- 自动化测试流程

## 使用方法

### 基本训练

```bash
# 使用新的训练脚本
python scripts/train_new.py --L 4 --n_iters 1000 --n_samples 4096

# 启用checkpoint
python scripts/train_new.py --L 4 --n_iters 1000 --enable_checkpoint --save_interval 500

# 从checkpoint恢复
python scripts/train_new.py --resume_from results/.../checkpoints/checkpoint_iter_000500.pkl
```

### 使用Hydra配置（需要安装hydra-core）

```bash
# 基本训练
python scripts/train_hydra.py

# 覆盖参数
python scripts/train_hydra.py system.L=6 training.n_iters=2000 training.checkpoint.enable=true
```

### 编程接口

```python
from src.runner import SSRunner

# 创建配置
model_config = {'num_features': 4, 'num_layers': 4}
training_config = {
    'learning_rate': 0.015,
    'n_iters': 1000,
    'initial_period': 100,
    'max_temperature': 1.0,
    'min_temperature': 0.01,
    # ... 其他参数
}
checkpoint_config = {'enable': True, 'save_interval': 500}

# 创建运行器
runner = SSRunner(
    L=4, J1=1.0, J2=0.5,
    model_class="GCNN",
    model_config=model_config,
    training_config=training_config,
    checkpoint_config=checkpoint_config
)

# 运行训练
runner.setup_model()
runner.run()
```

## 主要特性对比

| 特性 | 原版本 | 改进版本 |
|------|--------|----------|
| 退火机制 | 简单指数衰减 | 热重启余弦退火 |
| Checkpoint | 无 | 完整支持 |
| 配置管理 | 硬编码 | 灵活配置文件 |
| 训练接口 | 函数式 | 面向对象 |
| 日志记录 | 基本 | 详细分层 |
| 参数恢复 | 无 | 完整支持 |
| 错误处理 | 基本 | 增强的NaN检测 |

## 测试结果

所有测试均通过：
- ✅ 基本SSRunner功能测试
- ✅ Checkpoint保存和恢复测试
- ✅ 热重启退火机制测试
- ✅ 配置文件加载测试

## 兼容性

- 保持与原有物理模型的完全兼容
- 不修改任何底层物理实现
- 支持原有的GCNN模型
- 向后兼容原有的训练脚本

## 依赖变更

- 移除了对`pytz`的依赖
- 可选支持`hydra-core`（用于高级配置管理）
- 其他依赖保持不变

## 总结

通过这次改进，Shastry-Sutherland_model现在具备了与Kitaev_model相同的先进训练机制，包括：

1. **更智能的退火策略** - 热重启余弦退火提供更好的收敛性能
2. **可靠的checkpoint机制** - 支持训练中断和恢复
3. **灵活的配置管理** - 支持多种配置方式
4. **更好的用户体验** - 面向对象的接口和详细的日志

这些改进使得Shastry-Sutherland模型的训练更加稳定、高效和用户友好。
