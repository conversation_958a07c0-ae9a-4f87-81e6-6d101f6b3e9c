#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=4
#PBS -l walltime=24:00:00
#PBS -P 12004256
#PBS -N GCNN-Shastry-New
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

# 设置参数值
L_VALUES="4 6"
J2_VALUES="0.5"
J1_VALUES="1.0"

echo "参数设置:"
echo "L_VALUES = $L_VALUES"
echo "J2_VALUES = $J2_VALUES"
echo "J1_VALUES = $J1_VALUES"

# 并行任务最大数量
max_tasks=2
current_tasks=0

for L in $L_VALUES; do
    for J2 in $J2_VALUES; do
        for J1 in $J1_VALUES; do
            echo "开始训练 L=$L, J2=$J2, J1=$J1 at: $(date)"

            # 使用新的训练脚本，启用checkpoint
            singularity exec --nv -B /scratch,/app \
                /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
                python scripts/train_new.py \
                --L $L \
                --J1 $J1 \
                --J2 $J2 \
                --n_iters 2000 \
                --n_samples 4096 \
                --enable_checkpoint \
                --save_interval 500 \
                --learning_rate 0.015 \
                --n_cycles 2 \
                --initial_period 100 \
                --max_temperature 1.0 \
                --min_temperature 0.01 &

            current_tasks=$((current_tasks + 1))

            # 如果达到最大并行任务数，则等待这批任务全部结束
            if [ $current_tasks -ge $max_tasks ]; then
                wait
                current_tasks=0
            fi

            echo "已提交任务 L=$L, J2=$J2, J1=$J1 at: $(date)"
        done
    done
done

# 等待剩余任务
wait

echo "所有任务完成 at: $(date)" 