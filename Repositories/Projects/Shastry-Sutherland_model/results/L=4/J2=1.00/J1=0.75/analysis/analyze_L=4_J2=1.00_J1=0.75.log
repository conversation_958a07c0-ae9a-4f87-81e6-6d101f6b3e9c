[2025-07-25 00:32:00] ================================================================================
[2025-07-25 00:32:00] 加载量子态: L=4, J2=1.00, J1=0.75
[2025-07-25 00:32:00] 设置样本数为: 1048576
[2025-07-25 00:32:00] 开始生成共享样本集...
[2025-07-25 00:33:10] 样本生成完成,耗时: 69.945 秒
[2025-07-25 00:33:10] ================================================================================
[2025-07-25 00:33:10] 开始计算自旋结构因子...
[2025-07-25 00:33:10] 初始化操作符缓存...
[2025-07-25 00:33:10] 预构建所有自旋相关操作符...
[2025-07-25 00:33:10] 开始计算自旋相关函数...
[2025-07-25 00:33:18] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.991s
[2025-07-25 00:33:26] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.484s
[2025-07-25 00:33:30] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.592s
[2025-07-25 00:33:34] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.592s
[2025-07-25 00:33:37] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.591s
[2025-07-25 00:33:41] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.589s
[2025-07-25 00:33:44] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.590s
[2025-07-25 00:33:48] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.581s
[2025-07-25 00:33:52] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.576s
[2025-07-25 00:33:55] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.590s
[2025-07-25 00:33:59] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.590s
[2025-07-25 00:34:02] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.591s
[2025-07-25 00:34:06] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.582s
[2025-07-25 00:34:09] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.571s
[2025-07-25 00:34:13] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.582s
[2025-07-25 00:34:17] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.591s
[2025-07-25 00:34:20] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.591s
[2025-07-25 00:34:24] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.571s
[2025-07-25 00:34:27] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.582s
[2025-07-25 00:34:31] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.582s
[2025-07-25 00:34:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.591s
[2025-07-25 00:34:38] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.591s
[2025-07-25 00:34:42] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.590s
[2025-07-25 00:34:45] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.570s
[2025-07-25 00:34:49] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.582s
[2025-07-25 00:34:52] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.593s
[2025-07-25 00:34:56] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.592s
[2025-07-25 00:35:00] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.591s
[2025-07-25 00:35:03] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.574s
[2025-07-25 00:35:07] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.572s
[2025-07-25 00:35:10] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.582s
[2025-07-25 00:35:14] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.592s
[2025-07-25 00:35:18] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.594s
[2025-07-25 00:35:21] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.582s
[2025-07-25 00:35:25] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.573s
[2025-07-25 00:35:28] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.581s
[2025-07-25 00:35:32] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.592s
[2025-07-25 00:35:36] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.591s
[2025-07-25 00:35:39] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.592s
[2025-07-25 00:35:43] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.573s
[2025-07-25 00:35:46] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.582s
[2025-07-25 00:35:50] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.592s
[2025-07-25 00:35:53] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.589s
[2025-07-25 00:35:57] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.591s
[2025-07-25 00:36:01] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.582s
[2025-07-25 00:36:04] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.581s
[2025-07-25 00:36:08] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.571s
[2025-07-25 00:36:11] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.582s
[2025-07-25 00:36:15] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.573s
[2025-07-25 00:36:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.593s
[2025-07-25 00:36:22] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.592s
[2025-07-25 00:36:26] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.591s
[2025-07-25 00:36:29] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.581s
[2025-07-25 00:36:33] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.580s
[2025-07-25 00:36:36] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.572s
[2025-07-25 00:36:40] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.593s
[2025-07-25 00:36:44] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.593s
[2025-07-25 00:36:47] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.578s
[2025-07-25 00:36:51] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.581s
[2025-07-25 00:36:54] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.572s
[2025-07-25 00:36:58] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.592s
[2025-07-25 00:37:02] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.594s
[2025-07-25 00:37:05] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.594s
[2025-07-25 00:37:09] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.572s
[2025-07-25 00:37:09] 自旋相关函数计算完成,总耗时 238.82 秒
[2025-07-25 00:37:09] 计算傅里叶变换...
[2025-07-25 00:37:09] 自旋结构因子计算完成
[2025-07-25 00:37:10] 自旋相关函数平均误差: 0.000562
[2025-07-25 00:37:10] ================================================================================
[2025-07-25 00:37:10] 开始计算对角二聚体结构因子...
[2025-07-25 00:37:10] 识别所有对角二聚体...
[2025-07-25 00:37:10] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 00:37:10] 预计算对角二聚体操作符...
[2025-07-25 00:37:12] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 00:37:16] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.612s
[2025-07-25 00:37:27] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 11.043s
[2025-07-25 00:37:33] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.981s
[2025-07-25 00:37:39] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.981s
[2025-07-25 00:37:45] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.989s
[2025-07-25 00:37:51] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.994s
[2025-07-25 00:37:57] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.964s
[2025-07-25 00:38:03] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.982s
[2025-07-25 00:38:09] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.993s
[2025-07-25 00:38:15] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.988s
[2025-07-25 00:38:21] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.990s
[2025-07-25 00:38:27] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.991s
[2025-07-25 00:38:33] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.982s
[2025-07-25 00:38:39] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.965s
[2025-07-25 00:38:52] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.499s
[2025-07-25 00:38:58] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.014s
[2025-07-25 00:39:04] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.005s
[2025-07-25 00:39:10] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.975s
[2025-07-25 00:39:16] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.988s
[2025-07-25 00:39:22] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.973s
[2025-07-25 00:39:28] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.009s
[2025-07-25 00:39:34] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.014s
[2025-07-25 00:39:40] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.984s
[2025-07-25 00:39:46] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.983s
[2025-07-25 00:39:52] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.987s
[2025-07-25 00:39:58] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.008s
[2025-07-25 00:40:04] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.013s
[2025-07-25 00:40:10] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.008s
[2025-07-25 00:40:16] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.988s
[2025-07-25 00:40:22] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.971s
[2025-07-25 00:40:28] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.015s
[2025-07-25 00:40:34] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.013s
[2025-07-25 00:40:40] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.009s
[2025-07-25 00:40:46] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.988s
[2025-07-25 00:40:52] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.987s
[2025-07-25 00:40:58] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.980s
[2025-07-25 00:41:04] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.015s
[2025-07-25 00:41:10] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.017s
[2025-07-25 00:41:16] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.988s
[2025-07-25 00:41:22] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.984s
[2025-07-25 00:41:28] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.985s
[2025-07-25 00:41:34] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.015s
[2025-07-25 00:41:40] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.012s
[2025-07-25 00:41:46] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.012s
[2025-07-25 00:41:52] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.986s
[2025-07-25 00:41:58] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.972s
[2025-07-25 00:42:04] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.986s
[2025-07-25 00:42:10] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.969s
[2025-07-25 00:42:16] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.983s
[2025-07-25 00:42:22] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.006s
[2025-07-25 00:42:27] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.711s
[2025-07-25 00:42:33] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.014s
[2025-07-25 00:42:39] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.987s
[2025-07-25 00:42:45] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.972s
[2025-07-25 00:42:51] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.012s
[2025-07-25 00:42:57] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.015s
[2025-07-25 00:43:03] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.016s
[2025-07-25 00:43:09] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.984s
[2025-07-25 00:43:15] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.988s
[2025-07-25 00:43:21] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.968s
[2025-07-25 00:43:27] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.014s
[2025-07-25 00:43:33] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.007s
[2025-07-25 00:43:39] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.974s
[2025-07-25 00:43:45] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.987s
[2025-07-25 00:43:45] 西北-东南方向对角二聚体相关函数计算完成,耗时: 392.53 秒
[2025-07-25 00:43:45] ================================================================================
[2025-07-25 00:43:45] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 00:43:49] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.582s
[2025-07-25 00:43:55] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.012s
[2025-07-25 00:43:59] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.705s
[2025-07-25 00:44:05] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.012s
[2025-07-25 00:44:11] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.987s
[2025-07-25 00:44:17] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.968s
[2025-07-25 00:44:23] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.010s
[2025-07-25 00:44:29] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.013s
[2025-07-25 00:44:35] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.014s
[2025-07-25 00:44:41] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.984s
[2025-07-25 00:44:47] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.973s
[2025-07-25 00:44:53] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.983s
[2025-07-25 00:44:59] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.013s
[2025-07-25 00:45:05] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.012s
[2025-07-25 00:45:11] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.979s
[2025-07-25 00:45:17] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.988s
[2025-07-25 00:45:23] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.972s
[2025-07-25 00:45:29] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.015s
[2025-07-25 00:45:35] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.011s
[2025-07-25 00:45:41] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.015s
[2025-07-25 00:45:47] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.967s
[2025-07-25 00:45:53] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.979s
[2025-07-25 00:45:59] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.005s
[2025-07-25 00:46:05] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.019s
[2025-07-25 00:46:11] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.015s
[2025-07-25 00:46:17] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.013s
[2025-07-25 00:46:23] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.016s
[2025-07-25 00:46:29] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.016s
[2025-07-25 00:46:35] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.987s
[2025-07-25 00:46:41] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.985s
[2025-07-25 00:46:47] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.017s
[2025-07-25 00:46:53] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.019s
[2025-07-25 00:46:59] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.019s
[2025-07-25 00:47:05] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.987s
[2025-07-25 00:47:11] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.976s
[2025-07-25 00:47:17] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.986s
[2025-07-25 00:47:23] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.013s
[2025-07-25 00:47:29] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.010s
[2025-07-25 00:47:35] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.984s
[2025-07-25 00:47:41] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.980s
[2025-07-25 00:47:47] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.967s
[2025-07-25 00:47:53] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.015s
[2025-07-25 00:47:59] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.002s
[2025-07-25 00:48:05] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.013s
[2025-07-25 00:48:11] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.970s
[2025-07-25 00:48:17] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.982s
[2025-07-25 00:48:23] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.012s
[2025-07-25 00:48:29] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.015s
[2025-07-25 00:48:35] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.019s
[2025-07-25 00:48:41] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.986s
[2025-07-25 00:48:47] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.968s
[2025-07-25 00:48:53] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.986s
[2025-07-25 00:48:59] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.011s
[2025-07-25 00:49:05] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.003s
[2025-07-25 00:49:11] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.973s
[2025-07-25 00:49:17] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.967s
[2025-07-25 00:49:23] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.967s
[2025-07-25 00:49:29] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.014s
[2025-07-25 00:49:35] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.006s
[2025-07-25 00:49:41] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.014s
[2025-07-25 00:49:47] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.972s
[2025-07-25 00:49:53] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.987s
[2025-07-25 00:49:58] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.708s
[2025-07-25 00:50:04] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.017s
[2025-07-25 00:50:04] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.89 秒
[2025-07-25 00:50:04] 计算傅里叶变换...
[2025-07-25 00:50:04] 对角二聚体结构因子计算完成
[2025-07-25 00:50:05] 对角二聚体相关函数平均误差: 0.000128
