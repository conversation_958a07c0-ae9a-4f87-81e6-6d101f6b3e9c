[2025-07-25 02:03:32] ================================================================================
[2025-07-25 02:03:32] 加载量子态: L=4, J2=1.00, J1=0.80
[2025-07-25 02:03:32] 设置样本数为: 1048576
[2025-07-25 02:03:32] 开始生成共享样本集...
[2025-07-25 02:04:42] 样本生成完成,耗时: 69.386 秒
[2025-07-25 02:04:42] ================================================================================
[2025-07-25 02:04:42] 开始计算自旋结构因子...
[2025-07-25 02:04:42] 初始化操作符缓存...
[2025-07-25 02:04:42] 预构建所有自旋相关操作符...
[2025-07-25 02:04:42] 开始计算自旋相关函数...
[2025-07-25 02:04:50] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.782s
[2025-07-25 02:04:58] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.343s
[2025-07-25 02:05:02] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.570s
[2025-07-25 02:05:05] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.567s
[2025-07-25 02:05:09] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.571s
[2025-07-25 02:05:12] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.565s
[2025-07-25 02:05:16] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.570s
[2025-07-25 02:05:19] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.564s
[2025-07-25 02:05:23] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.555s
[2025-07-25 02:05:27] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.570s
[2025-07-25 02:05:30] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.569s
[2025-07-25 02:05:34] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.566s
[2025-07-25 02:05:37] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.563s
[2025-07-25 02:05:41] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.577s
[2025-07-25 02:05:44] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.564s
[2025-07-25 02:05:48] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.570s
[2025-07-25 02:05:51] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.571s
[2025-07-25 02:05:55] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.555s
[2025-07-25 02:05:59] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.563s
[2025-07-25 02:06:02] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.564s
[2025-07-25 02:06:06] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.570s
[2025-07-25 02:06:09] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.570s
[2025-07-25 02:06:13] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.568s
[2025-07-25 02:06:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.555s
[2025-07-25 02:06:20] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.562s
[2025-07-25 02:06:24] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.570s
[2025-07-25 02:06:27] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.569s
[2025-07-25 02:06:31] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.566s
[2025-07-25 02:06:34] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.554s
[2025-07-25 02:06:38] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.554s
[2025-07-25 02:06:41] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.564s
[2025-07-25 02:06:45] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.566s
[2025-07-25 02:06:49] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.571s
[2025-07-25 02:06:52] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.607s
[2025-07-25 02:06:56] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.556s
[2025-07-25 02:06:59] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.564s
[2025-07-25 02:07:03] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.568s
[2025-07-25 02:07:06] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.572s
[2025-07-25 02:07:10] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.570s
[2025-07-25 02:07:14] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.558s
[2025-07-25 02:07:17] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.563s
[2025-07-25 02:07:21] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.571s
[2025-07-25 02:07:24] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.568s
[2025-07-25 02:07:28] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.567s
[2025-07-25 02:07:31] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.563s
[2025-07-25 02:07:35] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.561s
[2025-07-25 02:07:39] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.557s
[2025-07-25 02:07:42] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.563s
[2025-07-25 02:07:46] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.556s
[2025-07-25 02:07:49] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.568s
[2025-07-25 02:07:53] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.571s
[2025-07-25 02:07:56] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.570s
[2025-07-25 02:08:00] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.563s
[2025-07-25 02:08:03] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.560s
[2025-07-25 02:08:07] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.555s
[2025-07-25 02:08:11] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.568s
[2025-07-25 02:08:14] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.567s
[2025-07-25 02:08:18] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.555s
[2025-07-25 02:08:21] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.563s
[2025-07-25 02:08:25] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.557s
[2025-07-25 02:08:28] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.571s
[2025-07-25 02:08:32] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.568s
[2025-07-25 02:08:36] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.570s
[2025-07-25 02:08:39] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.555s
[2025-07-25 02:08:39] 自旋相关函数计算完成,总耗时 237.26 秒
[2025-07-25 02:08:39] 计算傅里叶变换...
[2025-07-25 02:08:40] 自旋结构因子计算完成
[2025-07-25 02:08:40] 自旋相关函数平均误差: 0.000536
[2025-07-25 02:08:40] ================================================================================
[2025-07-25 02:08:40] 开始计算对角二聚体结构因子...
[2025-07-25 02:08:40] 识别所有对角二聚体...
[2025-07-25 02:08:41] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 02:08:41] 预计算对角二聚体操作符...
[2025-07-25 02:08:42] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:08:46] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.591s
[2025-07-25 02:08:57] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.866s
[2025-07-25 02:09:03] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.945s
[2025-07-25 02:09:09] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.946s
[2025-07-25 02:09:15] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.944s
[2025-07-25 02:09:21] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.952s
[2025-07-25 02:09:27] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.916s
[2025-07-25 02:09:33] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.946s
[2025-07-25 02:09:39] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.955s
[2025-07-25 02:09:44] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.945s
[2025-07-25 02:09:50] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.951s
[2025-07-25 02:09:56] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.953s
[2025-07-25 02:10:02] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.946s
[2025-07-25 02:10:08] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.916s
[2025-07-25 02:10:21] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.126s
[2025-07-25 02:10:27] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 5.975s
[2025-07-25 02:10:33] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.960s
[2025-07-25 02:10:39] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.940s
[2025-07-25 02:10:45] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.954s
[2025-07-25 02:10:51] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.940s
[2025-07-25 02:10:57] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 5.965s
[2025-07-25 02:11:03] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.977s
[2025-07-25 02:11:09] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.953s
[2025-07-25 02:11:15] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.941s
[2025-07-25 02:11:21] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.954s
[2025-07-25 02:11:27] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.963s
[2025-07-25 02:11:33] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 5.970s
[2025-07-25 02:11:39] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.960s
[2025-07-25 02:11:45] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.954s
[2025-07-25 02:11:51] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.941s
[2025-07-25 02:11:57] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 5.978s
[2025-07-25 02:12:03] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.973s
[2025-07-25 02:12:09] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 5.964s
[2025-07-25 02:12:15] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.955s
[2025-07-25 02:12:21] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.954s
[2025-07-25 02:12:27] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.937s
[2025-07-25 02:12:32] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 5.969s
[2025-07-25 02:12:38] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.974s
[2025-07-25 02:12:44] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.954s
[2025-07-25 02:12:50] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.937s
[2025-07-25 02:12:56] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.954s
[2025-07-25 02:13:02] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.979s
[2025-07-25 02:13:08] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 5.971s
[2025-07-25 02:13:14] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.967s
[2025-07-25 02:13:20] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.955s
[2025-07-25 02:13:26] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.939s
[2025-07-25 02:13:32] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.954s
[2025-07-25 02:13:38] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.942s
[2025-07-25 02:13:44] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.951s
[2025-07-25 02:13:50] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.957s
[2025-07-25 02:13:55] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.673s
[2025-07-25 02:14:01] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.971s
[2025-07-25 02:14:07] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.954s
[2025-07-25 02:14:12] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.942s
[2025-07-25 02:14:18] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 5.973s
[2025-07-25 02:14:24] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.977s
[2025-07-25 02:14:30] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 5.979s
[2025-07-25 02:14:36] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.940s
[2025-07-25 02:14:42] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.954s
[2025-07-25 02:14:48] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.941s
[2025-07-25 02:14:54] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 5.971s
[2025-07-25 02:15:00] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.963s
[2025-07-25 02:15:06] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.942s
[2025-07-25 02:15:12] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.954s
[2025-07-25 02:15:12] 西北-东南方向对角二聚体相关函数计算完成,耗时: 389.63 秒
[2025-07-25 02:15:12] ================================================================================
[2025-07-25 02:15:12] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:15:16] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.564s
[2025-07-25 02:15:22] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.975s
[2025-07-25 02:15:26] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.775s
[2025-07-25 02:15:32] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 5.974s
[2025-07-25 02:15:38] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.952s
[2025-07-25 02:15:44] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.964s
[2025-07-25 02:15:50] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 5.966s
[2025-07-25 02:15:56] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.980s
[2025-07-25 02:16:02] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.983s
[2025-07-25 02:16:08] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.951s
[2025-07-25 02:16:14] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.943s
[2025-07-25 02:16:20] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.951s
[2025-07-25 02:16:26] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.977s
[2025-07-25 02:16:32] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 5.975s
[2025-07-25 02:16:38] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.939s
[2025-07-25 02:16:44] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.953s
[2025-07-25 02:16:50] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.939s
[2025-07-25 02:16:56] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 5.970s
[2025-07-25 02:17:02] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.968s
[2025-07-25 02:17:08] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 5.976s
[2025-07-25 02:17:14] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.939s
[2025-07-25 02:17:20] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.939s
[2025-07-25 02:17:26] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 5.959s
[2025-07-25 02:17:32] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.977s
[2025-07-25 02:17:38] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.975s
[2025-07-25 02:17:44] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 5.973s
[2025-07-25 02:17:50] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 5.977s
[2025-07-25 02:17:56] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 5.981s
[2025-07-25 02:18:01] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.940s
[2025-07-25 02:18:07] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.953s
[2025-07-25 02:18:13] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.977s
[2025-07-25 02:18:19] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 5.979s
[2025-07-25 02:18:25] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.977s
[2025-07-25 02:18:31] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.954s
[2025-07-25 02:18:37] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.939s
[2025-07-25 02:18:43] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.955s
[2025-07-25 02:18:49] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.976s
[2025-07-25 02:18:55] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 5.967s
[2025-07-25 02:19:01] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.952s
[2025-07-25 02:19:07] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.943s
[2025-07-25 02:19:13] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.943s
[2025-07-25 02:19:19] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 5.981s
[2025-07-25 02:19:25] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 5.957s
[2025-07-25 02:19:31] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 5.978s
[2025-07-25 02:19:37] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.942s
[2025-07-25 02:19:43] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.949s
[2025-07-25 02:19:49] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 5.974s
[2025-07-25 02:19:55] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.978s
[2025-07-25 02:20:01] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.981s
[2025-07-25 02:20:07] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.954s
[2025-07-25 02:20:13] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.945s
[2025-07-25 02:20:19] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.954s
[2025-07-25 02:20:25] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.975s
[2025-07-25 02:20:31] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 5.965s
[2025-07-25 02:20:36] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.946s
[2025-07-25 02:20:42] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.947s
[2025-07-25 02:20:48] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.947s
[2025-07-25 02:20:54] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 5.985s
[2025-07-25 02:21:00] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.964s
[2025-07-25 02:21:06] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 5.973s
[2025-07-25 02:21:12] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.943s
[2025-07-25 02:21:18] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.954s
[2025-07-25 02:21:23] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.674s
[2025-07-25 02:21:29] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 5.977s
[2025-07-25 02:21:29] 西南-东北方向对角二聚体相关函数计算完成,耗时: 376.77 秒
[2025-07-25 02:21:29] 计算傅里叶变换...
[2025-07-25 02:21:29] 对角二聚体结构因子计算完成
[2025-07-25 02:21:30] 对角二聚体相关函数平均误差: 0.000118
