[2025-07-25 00:50:24] ================================================================================
[2025-07-25 00:50:24] 加载量子态: L=4, J2=1.00, J1=0.76
[2025-07-25 00:50:24] 设置样本数为: 1048576
[2025-07-25 00:50:24] 开始生成共享样本集...
[2025-07-25 00:51:35] 样本生成完成,耗时: 70.793 秒
[2025-07-25 00:51:35] ================================================================================
[2025-07-25 00:51:35] 开始计算自旋结构因子...
[2025-07-25 00:51:35] 初始化操作符缓存...
[2025-07-25 00:51:35] 预构建所有自旋相关操作符...
[2025-07-25 00:51:35] 开始计算自旋相关函数...
[2025-07-25 00:51:43] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.268s
[2025-07-25 00:51:52] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.429s
[2025-07-25 00:51:55] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.575s
[2025-07-25 00:51:59] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.595s
[2025-07-25 00:52:03] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.596s
[2025-07-25 00:52:06] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.577s
[2025-07-25 00:52:10] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.596s
[2025-07-25 00:52:13] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.596s
[2025-07-25 00:52:17] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.577s
[2025-07-25 00:52:21] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.596s
[2025-07-25 00:52:24] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.576s
[2025-07-25 00:52:28] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.576s
[2025-07-25 00:52:31] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.598s
[2025-07-25 00:52:35] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.575s
[2025-07-25 00:52:39] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.596s
[2025-07-25 00:52:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.595s
[2025-07-25 00:52:46] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.576s
[2025-07-25 00:52:49] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.576s
[2025-07-25 00:52:53] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.597s
[2025-07-25 00:52:56] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.595s
[2025-07-25 00:53:00] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.575s
[2025-07-25 00:53:04] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.576s
[2025-07-25 00:53:07] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.595s
[2025-07-25 00:53:11] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.577s
[2025-07-25 00:53:14] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.596s
[2025-07-25 00:53:18] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.596s
[2025-07-25 00:53:22] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.576s
[2025-07-25 00:53:25] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.596s
[2025-07-25 00:53:29] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.576s
[2025-07-25 00:53:32] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.576s
[2025-07-25 00:53:36] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.595s
[2025-07-25 00:53:40] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.577s
[2025-07-25 00:53:43] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.595s
[2025-07-25 00:53:47] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.597s
[2025-07-25 00:53:50] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.577s
[2025-07-25 00:53:54] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.597s
[2025-07-25 00:53:57] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.575s
[2025-07-25 00:54:01] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.576s
[2025-07-25 00:54:05] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.596s
[2025-07-25 00:54:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.576s
[2025-07-25 00:54:12] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.598s
[2025-07-25 00:54:15] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.596s
[2025-07-25 00:54:19] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.595s
[2025-07-25 00:54:23] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.576s
[2025-07-25 00:54:26] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.596s
[2025-07-25 00:54:30] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.595s
[2025-07-25 00:54:33] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.575s
[2025-07-25 00:54:37] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.596s
[2025-07-25 00:54:41] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.576s
[2025-07-25 00:54:44] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.576s
[2025-07-25 00:54:48] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.596s
[2025-07-25 00:54:51] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.577s
[2025-07-25 00:54:55] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.596s
[2025-07-25 00:54:58] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.595s
[2025-07-25 00:55:02] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.576s
[2025-07-25 00:55:06] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.596s
[2025-07-25 00:55:09] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.576s
[2025-07-25 00:55:13] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.577s
[2025-07-25 00:55:16] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.595s
[2025-07-25 00:55:20] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.576s
[2025-07-25 00:55:24] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.595s
[2025-07-25 00:55:27] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.595s
[2025-07-25 00:55:31] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.576s
[2025-07-25 00:55:34] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.575s
[2025-07-25 00:55:34] 自旋相关函数计算完成,总耗时 239.12 秒
[2025-07-25 00:55:34] 计算傅里叶变换...
[2025-07-25 00:55:35] 自旋结构因子计算完成
[2025-07-25 00:55:36] 自旋相关函数平均误差: 0.000534
[2025-07-25 00:55:36] ================================================================================
[2025-07-25 00:55:36] 开始计算对角二聚体结构因子...
[2025-07-25 00:55:36] 识别所有对角二聚体...
[2025-07-25 00:55:36] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 00:55:36] 预计算对角二聚体操作符...
[2025-07-25 00:55:38] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 00:55:42] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.640s
[2025-07-25 00:55:53] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 11.047s
[2025-07-25 00:55:59] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 6.019s
[2025-07-25 00:56:05] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 6.017s
[2025-07-25 00:56:11] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.978s
[2025-07-25 00:56:17] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 6.018s
[2025-07-25 00:56:23] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.978s
[2025-07-25 00:56:29] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 6.021s
[2025-07-25 00:56:35] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.977s
[2025-07-25 00:56:41] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 6.019s
[2025-07-25 00:56:47] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.977s
[2025-07-25 00:56:53] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.986s
[2025-07-25 00:56:59] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 6.019s
[2025-07-25 00:57:05] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.978s
[2025-07-25 00:57:18] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.256s
[2025-07-25 00:57:24] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.024s
[2025-07-25 00:57:30] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.983s
[2025-07-25 00:57:36] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.983s
[2025-07-25 00:57:42] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.025s
[2025-07-25 00:57:48] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.985s
[2025-07-25 00:57:54] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.023s
[2025-07-25 00:58:00] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.983s
[2025-07-25 00:58:06] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 6.022s
[2025-07-25 00:58:12] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.983s
[2025-07-25 00:58:18] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.023s
[2025-07-25 00:58:24] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.984s
[2025-07-25 00:58:30] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.023s
[2025-07-25 00:58:36] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.984s
[2025-07-25 00:58:42] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.024s
[2025-07-25 00:58:48] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.982s
[2025-07-25 00:58:54] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.022s
[2025-07-25 00:59:00] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.983s
[2025-07-25 00:59:06] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.025s
[2025-07-25 00:59:12] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.024s
[2025-07-25 00:59:18] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.022s
[2025-07-25 00:59:24] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.985s
[2025-07-25 00:59:30] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.130s
[2025-07-25 00:59:36] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.983s
[2025-07-25 00:59:42] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.025s
[2025-07-25 00:59:48] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.983s
[2025-07-25 00:59:54] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 6.023s
[2025-07-25 01:00:00] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.983s
[2025-07-25 01:00:06] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.025s
[2025-07-25 01:00:12] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.983s
[2025-07-25 01:00:18] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.023s
[2025-07-25 01:00:24] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.983s
[2025-07-25 01:00:30] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.023s
[2025-07-25 01:00:36] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.984s
[2025-07-25 01:00:42] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 6.022s
[2025-07-25 01:00:48] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.022s
[2025-07-25 01:00:53] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.697s
[2025-07-25 01:00:59] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.984s
[2025-07-25 01:01:05] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.023s
[2025-07-25 01:01:11] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.984s
[2025-07-25 01:01:17] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.025s
[2025-07-25 01:01:23] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.983s
[2025-07-25 01:01:29] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.024s
[2025-07-25 01:01:35] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.983s
[2025-07-25 01:01:41] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.025s
[2025-07-25 01:01:47] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.983s
[2025-07-25 01:01:53] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.022s
[2025-07-25 01:01:59] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.982s
[2025-07-25 01:02:05] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.985s
[2025-07-25 01:02:11] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.023s
[2025-07-25 01:02:11] 西北-东南方向对角二聚体相关函数计算完成,耗时: 393.01 秒
[2025-07-25 01:02:11] ================================================================================
[2025-07-25 01:02:11] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:02:15] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.582s
[2025-07-25 01:02:21] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.023s
[2025-07-25 01:02:25] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.697s
[2025-07-25 01:02:31] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.024s
[2025-07-25 01:02:37] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.024s
[2025-07-25 01:02:43] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.984s
[2025-07-25 01:02:49] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.023s
[2025-07-25 01:02:55] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.983s
[2025-07-25 01:03:01] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.983s
[2025-07-25 01:03:07] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 6.023s
[2025-07-25 01:03:13] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.983s
[2025-07-25 01:03:19] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 6.021s
[2025-07-25 01:03:25] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.985s
[2025-07-25 01:03:31] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.024s
[2025-07-25 01:03:37] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.984s
[2025-07-25 01:03:43] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.024s
[2025-07-25 01:03:49] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.984s
[2025-07-25 01:03:55] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.022s
[2025-07-25 01:04:01] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.983s
[2025-07-25 01:04:07] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.022s
[2025-07-25 01:04:13] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.983s
[2025-07-25 01:04:19] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.983s
[2025-07-25 01:04:25] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.023s
[2025-07-25 01:04:31] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.984s
[2025-07-25 01:04:37] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.986s
[2025-07-25 01:04:43] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.023s
[2025-07-25 01:04:49] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 5.984s
[2025-07-25 01:04:55] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.024s
[2025-07-25 01:05:01] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.983s
[2025-07-25 01:05:07] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.022s
[2025-07-25 01:05:13] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.982s
[2025-07-25 01:05:19] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.022s
[2025-07-25 01:05:25] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.982s
[2025-07-25 01:05:31] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.026s
[2025-07-25 01:05:37] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.982s
[2025-07-25 01:05:43] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.023s
[2025-07-25 01:05:49] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.983s
[2025-07-25 01:05:55] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.023s
[2025-07-25 01:06:01] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 6.022s
[2025-07-25 01:06:07] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.982s
[2025-07-25 01:06:13] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.983s
[2025-07-25 01:06:19] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.023s
[2025-07-25 01:06:25] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 5.985s
[2025-07-25 01:06:31] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.023s
[2025-07-25 01:06:37] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.985s
[2025-07-25 01:06:43] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 6.023s
[2025-07-25 01:06:49] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 5.982s
[2025-07-25 01:06:55] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.023s
[2025-07-25 01:07:01] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.984s
[2025-07-25 01:07:08] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.023s
[2025-07-25 01:07:13] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.983s
[2025-07-25 01:07:20] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.023s
[2025-07-25 01:07:25] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.983s
[2025-07-25 01:07:32] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.023s
[2025-07-25 01:07:37] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.982s
[2025-07-25 01:07:43] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.984s
[2025-07-25 01:07:49] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.982s
[2025-07-25 01:07:55] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.023s
[2025-07-25 01:08:01] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.983s
[2025-07-25 01:08:07] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.023s
[2025-07-25 01:08:13] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.982s
[2025-07-25 01:08:20] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.022s
[2025-07-25 01:08:24] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.697s
[2025-07-25 01:08:30] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.023s
[2025-07-25 01:08:30] 西南-东北方向对角二聚体相关函数计算完成,耗时: 379.20 秒
[2025-07-25 01:08:30] 计算傅里叶变换...
[2025-07-25 01:08:31] 对角二聚体结构因子计算完成
[2025-07-25 01:08:32] 对角二聚体相关函数平均误差: 0.000119
