[2025-07-25 03:16:40] ================================================================================
[2025-07-25 03:16:40] 加载量子态: L=4, J2=1.00, J1=0.84
[2025-07-25 03:16:40] 设置样本数为: 1048576
[2025-07-25 03:16:40] 开始生成共享样本集...
[2025-07-25 03:17:50] 样本生成完成,耗时: 70.034 秒
[2025-07-25 03:17:50] ================================================================================
[2025-07-25 03:17:50] 开始计算自旋结构因子...
[2025-07-25 03:17:50] 初始化操作符缓存...
[2025-07-25 03:17:50] 预构建所有自旋相关操作符...
[2025-07-25 03:17:50] 开始计算自旋相关函数...
[2025-07-25 03:17:58] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.767s
[2025-07-25 03:18:06] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.383s
[2025-07-25 03:18:10] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.596s
[2025-07-25 03:18:13] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.596s
[2025-07-25 03:18:17] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.597s
[2025-07-25 03:18:21] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.596s
[2025-07-25 03:18:24] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.654s
[2025-07-25 03:18:28] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.592s
[2025-07-25 03:18:31] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.580s
[2025-07-25 03:18:35] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.597s
[2025-07-25 03:18:39] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.596s
[2025-07-25 03:18:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.596s
[2025-07-25 03:18:46] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.602s
[2025-07-25 03:18:49] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.586s
[2025-07-25 03:18:53] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.599s
[2025-07-25 03:18:57] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.596s
[2025-07-25 03:19:00] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.597s
[2025-07-25 03:19:04] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.584s
[2025-07-25 03:19:07] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.601s
[2025-07-25 03:19:11] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.596s
[2025-07-25 03:19:15] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.595s
[2025-07-25 03:19:18] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.595s
[2025-07-25 03:19:22] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.610s
[2025-07-25 03:19:25] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.583s
[2025-07-25 03:19:29] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.599s
[2025-07-25 03:19:33] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.600s
[2025-07-25 03:19:36] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.596s
[2025-07-25 03:19:40] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.598s
[2025-07-25 03:19:43] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.583s
[2025-07-25 03:19:47] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.585s
[2025-07-25 03:19:51] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.596s
[2025-07-25 03:19:54] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.595s
[2025-07-25 03:19:58] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.597s
[2025-07-25 03:20:01] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.600s
[2025-07-25 03:20:05] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.587s
[2025-07-25 03:20:08] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.601s
[2025-07-25 03:20:12] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.597s
[2025-07-25 03:20:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.596s
[2025-07-25 03:20:19] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.595s
[2025-07-25 03:20:23] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.586s
[2025-07-25 03:20:26] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.603s
[2025-07-25 03:20:30] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.599s
[2025-07-25 03:20:34] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.598s
[2025-07-25 03:20:37] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.595s
[2025-07-25 03:20:41] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.596s
[2025-07-25 03:20:44] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.591s
[2025-07-25 03:20:48] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.582s
[2025-07-25 03:20:52] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.714s
[2025-07-25 03:20:55] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.586s
[2025-07-25 03:20:59] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.596s
[2025-07-25 03:21:03] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.596s
[2025-07-25 03:21:06] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.596s
[2025-07-25 03:21:10] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.599s
[2025-07-25 03:21:13] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.591s
[2025-07-25 03:21:17] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.581s
[2025-07-25 03:21:21] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.596s
[2025-07-25 03:21:24] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.595s
[2025-07-25 03:21:28] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.696s
[2025-07-25 03:21:31] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.596s
[2025-07-25 03:21:35] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.581s
[2025-07-25 03:21:39] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.596s
[2025-07-25 03:21:42] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.598s
[2025-07-25 03:21:46] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.596s
[2025-07-25 03:21:49] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.586s
[2025-07-25 03:21:49] 自旋相关函数计算完成,总耗时 239.35 秒
[2025-07-25 03:21:49] 计算傅里叶变换...
[2025-07-25 03:21:50] 自旋结构因子计算完成
[2025-07-25 03:21:51] 自旋相关函数平均误差: 0.000525
[2025-07-25 03:21:51] ================================================================================
[2025-07-25 03:21:51] 开始计算对角二聚体结构因子...
[2025-07-25 03:21:51] 识别所有对角二聚体...
[2025-07-25 03:21:51] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 03:21:51] 预计算对角二聚体操作符...
[2025-07-25 03:21:53] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:21:56] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.633s
[2025-07-25 03:22:07] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.889s
[2025-07-25 03:22:13] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 6.013s
[2025-07-25 03:22:19] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 6.011s
[2025-07-25 03:22:25] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 6.001s
[2025-07-25 03:22:31] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 6.007s
[2025-07-25 03:22:37] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.989s
[2025-07-25 03:22:43] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 6.016s
[2025-07-25 03:22:49] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 6.001s
[2025-07-25 03:22:55] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 6.011s
[2025-07-25 03:23:01] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 6.000s
[2025-07-25 03:23:07] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 6.000s
[2025-07-25 03:23:13] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 6.012s
[2025-07-25 03:23:19] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.989s
[2025-07-25 03:23:33] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.160s
[2025-07-25 03:23:39] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.027s
[2025-07-25 03:23:45] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.019s
[2025-07-25 03:23:51] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.998s
[2025-07-25 03:23:57] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.030s
[2025-07-25 03:24:03] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.995s
[2025-07-25 03:24:09] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.027s
[2025-07-25 03:24:15] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.020s
[2025-07-25 03:24:21] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 6.014s
[2025-07-25 03:24:27] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 6.004s
[2025-07-25 03:24:33] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.020s
[2025-07-25 03:24:39] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.020s
[2025-07-25 03:24:45] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.025s
[2025-07-25 03:24:51] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.019s
[2025-07-25 03:24:57] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.030s
[2025-07-25 03:25:03] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 6.003s
[2025-07-25 03:25:09] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.020s
[2025-07-25 03:25:15] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.020s
[2025-07-25 03:25:21] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.032s
[2025-07-25 03:25:27] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.028s
[2025-07-25 03:25:33] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.023s
[2025-07-25 03:25:39] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 6.004s
[2025-07-25 03:25:45] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.021s
[2025-07-25 03:25:51] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.020s
[2025-07-25 03:25:57] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.028s
[2025-07-25 03:26:03] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.998s
[2025-07-25 03:26:09] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 6.018s
[2025-07-25 03:26:15] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.020s
[2025-07-25 03:26:21] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.030s
[2025-07-25 03:26:27] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.020s
[2025-07-25 03:26:33] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.020s
[2025-07-25 03:26:39] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 6.001s
[2025-07-25 03:26:45] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.021s
[2025-07-25 03:26:51] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 6.004s
[2025-07-25 03:26:57] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 6.013s
[2025-07-25 03:27:03] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.019s
[2025-07-25 03:27:08] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.715s
[2025-07-25 03:27:14] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.020s
[2025-07-25 03:27:20] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.025s
[2025-07-25 03:27:26] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 6.004s
[2025-07-25 03:27:32] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.031s
[2025-07-25 03:27:38] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.019s
[2025-07-25 03:27:44] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.026s
[2025-07-25 03:27:50] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 6.000s
[2025-07-25 03:27:56] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.030s
[2025-07-25 03:28:02] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 6.004s
[2025-07-25 03:28:08] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.025s
[2025-07-25 03:28:14] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.021s
[2025-07-25 03:28:20] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.993s
[2025-07-25 03:28:26] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.025s
[2025-07-25 03:28:26] 西北-东南方向对角二聚体相关函数计算完成,耗时: 393.36 秒
[2025-07-25 03:28:26] ================================================================================
[2025-07-25 03:28:26] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:28:30] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.589s
[2025-07-25 03:28:36] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.019s
[2025-07-25 03:28:40] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.712s
[2025-07-25 03:28:47] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.028s
[2025-07-25 03:28:53] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.028s
[2025-07-25 03:28:59] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.994s
[2025-07-25 03:29:05] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.022s
[2025-07-25 03:29:11] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.020s
[2025-07-25 03:29:17] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.020s
[2025-07-25 03:29:23] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 6.113s
[2025-07-25 03:29:29] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 6.002s
[2025-07-25 03:29:35] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 6.016s
[2025-07-25 03:29:41] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.020s
[2025-07-25 03:29:47] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.029s
[2025-07-25 03:29:53] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.994s
[2025-07-25 03:29:59] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.028s
[2025-07-25 03:30:05] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 6.003s
[2025-07-25 03:30:11] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.024s
[2025-07-25 03:30:17] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.020s
[2025-07-25 03:30:23] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.020s
[2025-07-25 03:30:29] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.996s
[2025-07-25 03:30:35] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 6.004s
[2025-07-25 03:30:41] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.021s
[2025-07-25 03:30:47] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.020s
[2025-07-25 03:30:53] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.020s
[2025-07-25 03:30:59] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.027s
[2025-07-25 03:31:05] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.020s
[2025-07-25 03:31:11] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.030s
[2025-07-25 03:31:17] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 6.000s
[2025-07-25 03:31:23] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.024s
[2025-07-25 03:31:29] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.020s
[2025-07-25 03:31:35] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.020s
[2025-07-25 03:31:41] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.019s
[2025-07-25 03:31:47] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.032s
[2025-07-25 03:31:53] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 6.002s
[2025-07-25 03:31:59] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.023s
[2025-07-25 03:32:05] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.019s
[2025-07-25 03:32:11] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.020s
[2025-07-25 03:32:17] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 6.016s
[2025-07-25 03:32:23] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 6.003s
[2025-07-25 03:32:29] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.999s
[2025-07-25 03:32:35] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.025s
[2025-07-25 03:32:41] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.018s
[2025-07-25 03:32:47] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.027s
[2025-07-25 03:32:53] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 6.005s
[2025-07-25 03:32:59] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 6.012s
[2025-07-25 03:33:05] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.018s
[2025-07-25 03:33:11] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.021s
[2025-07-25 03:33:17] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.019s
[2025-07-25 03:33:23] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.025s
[2025-07-25 03:33:29] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 6.000s
[2025-07-25 03:33:35] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.020s
[2025-07-25 03:33:41] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.021s
[2025-07-25 03:33:47] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.024s
[2025-07-25 03:33:53] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 6.000s
[2025-07-25 03:33:59] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 6.004s
[2025-07-25 03:34:06] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 6.002s
[2025-07-25 03:34:12] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.019s
[2025-07-25 03:34:18] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.019s
[2025-07-25 03:34:24] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.024s
[2025-07-25 03:34:30] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 6.001s
[2025-07-25 03:34:36] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.024s
[2025-07-25 03:34:40] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.715s
[2025-07-25 03:34:46] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.022s
[2025-07-25 03:34:46] 西南-东北方向对角二聚体相关函数计算完成,耗时: 380.18 秒
[2025-07-25 03:34:46] 计算傅里叶变换...
[2025-07-25 03:34:47] 对角二聚体结构因子计算完成
[2025-07-25 03:34:48] 对角二聚体相关函数平均误差: 0.000115
