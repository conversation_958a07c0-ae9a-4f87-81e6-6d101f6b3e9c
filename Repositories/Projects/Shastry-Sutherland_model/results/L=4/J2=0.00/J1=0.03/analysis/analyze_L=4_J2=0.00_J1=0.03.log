[2025-07-23 18:57:11] ================================================================================
[2025-07-23 18:57:11] 加载量子态: L=4, J2=0.00, J1=0.03
[2025-07-23 18:57:11] 设置样本数为: 1048576
[2025-07-23 18:57:11] 开始生成共享样本集...
[2025-07-23 18:58:21] 样本生成完成,耗时: 70.159 秒
[2025-07-23 18:58:21] ================================================================================
[2025-07-23 18:58:21] 开始计算自旋结构因子...
[2025-07-23 18:58:21] 初始化操作符缓存...
[2025-07-23 18:58:21] 预构建所有自旋相关操作符...
[2025-07-23 18:58:22] 开始计算自旋相关函数...
[2025-07-23 18:58:30] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.637s
[2025-07-23 18:58:39] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.133s
[2025-07-23 18:58:43] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.604s
[2025-07-23 18:58:47] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.604s
[2025-07-23 18:58:50] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.605s
[2025-07-23 18:58:54] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.600s
[2025-07-23 18:58:57] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.685s
[2025-07-23 18:59:01] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.578s
[2025-07-23 18:59:05] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.580s
[2025-07-23 18:59:08] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.601s
[2025-07-23 18:59:12] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.604s
[2025-07-23 18:59:15] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.600s
[2025-07-23 18:59:19] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.603s
[2025-07-23 18:59:23] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.577s
[2025-07-23 18:59:26] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.579s
[2025-07-23 18:59:30] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.600s
[2025-07-23 18:59:33] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.601s
[2025-07-23 18:59:37] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.588s
[2025-07-23 18:59:41] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.581s
[2025-07-23 18:59:44] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.580s
[2025-07-23 18:59:48] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.600s
[2025-07-23 18:59:51] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.600s
[2025-07-23 18:59:55] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.600s
[2025-07-23 18:59:58] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.579s
[2025-07-23 19:00:02] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.579s
[2025-07-23 19:00:06] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.601s
[2025-07-23 19:00:09] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.601s
[2025-07-23 19:00:13] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.599s
[2025-07-23 19:00:16] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.580s
[2025-07-23 19:00:20] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.580s
[2025-07-23 19:00:24] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.580s
[2025-07-23 19:00:27] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.599s
[2025-07-23 19:00:31] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.601s
[2025-07-23 19:00:34] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.580s
[2025-07-23 19:00:38] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.580s
[2025-07-23 19:00:42] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.581s
[2025-07-23 19:00:45] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.600s
[2025-07-23 19:00:49] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.600s
[2025-07-23 19:00:52] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.600s
[2025-07-23 19:00:56] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.579s
[2025-07-23 19:01:00] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.582s
[2025-07-23 19:01:03] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.612s
[2025-07-23 19:01:07] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.598s
[2025-07-23 19:01:10] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.598s
[2025-07-23 19:01:14] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.578s
[2025-07-23 19:01:17] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.579s
[2025-07-23 19:01:21] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.582s
[2025-07-23 19:01:25] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.589s
[2025-07-23 19:01:28] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.581s
[2025-07-23 19:01:32] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.601s
[2025-07-23 19:01:35] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.603s
[2025-07-23 19:01:39] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.601s
[2025-07-23 19:01:43] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.581s
[2025-07-23 19:01:46] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.581s
[2025-07-23 19:01:50] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.578s
[2025-07-23 19:01:53] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.600s
[2025-07-23 19:01:57] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.600s
[2025-07-23 19:02:01] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.581s
[2025-07-23 19:02:04] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.580s
[2025-07-23 19:02:08] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.579s
[2025-07-23 19:02:11] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.600s
[2025-07-23 19:02:15] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.599s
[2025-07-23 19:02:19] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.602s
[2025-07-23 19:02:22] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.577s
[2025-07-23 19:02:22] 自旋相关函数计算完成,总耗时 240.61 秒
[2025-07-23 19:02:22] 计算傅里叶变换...
[2025-07-23 19:02:23] 自旋结构因子计算完成
[2025-07-23 19:02:24] 自旋相关函数平均误差: 0.000671
[2025-07-23 19:02:24] ================================================================================
[2025-07-23 19:02:24] 开始计算二聚体结构因子...
[2025-07-23 19:02:24] 识别x和y方向的二聚体...
[2025-07-23 19:02:24] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 19:02:24] 预计算二聚体操作符...
[2025-07-23 19:02:25] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 19:02:28] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.627s
[2025-07-23 19:02:43] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.678s
[2025-07-23 19:02:48] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.746s
[2025-07-23 19:02:59] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.570s
[2025-07-23 19:03:05] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.013s
[2025-07-23 19:03:11] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.020s
[2025-07-23 19:03:17] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.976s
[2025-07-23 19:03:23] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.973s
[2025-07-23 19:03:29] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.016s
[2025-07-23 19:03:35] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.013s
[2025-07-23 19:03:41] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.017s
[2025-07-23 19:03:47] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.014s
[2025-07-23 19:03:53] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.982s
[2025-07-23 19:03:59] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.974s
[2025-07-23 19:04:05] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.015s
[2025-07-23 19:04:11] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.017s
[2025-07-23 19:04:17] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.013s
[2025-07-23 19:04:23] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.974s
[2025-07-23 19:04:29] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.971s
[2025-07-23 19:04:35] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.974s
[2025-07-23 19:04:41] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.016s
[2025-07-23 19:04:47] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.015s
[2025-07-23 19:04:53] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.982s
[2025-07-23 19:04:59] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.973s
[2025-07-23 19:05:05] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.975s
[2025-07-23 19:05:11] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.012s
[2025-07-23 19:05:17] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.018s
[2025-07-23 19:05:23] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.013s
[2025-07-23 19:05:29] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 5.973s
[2025-07-23 19:05:35] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.971s
[2025-07-23 19:05:41] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.014s
[2025-07-23 19:05:47] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.013s
[2025-07-23 19:05:53] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.012s
[2025-07-23 19:05:59] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 5.979s
[2025-07-23 19:06:05] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.974s
[2025-07-23 19:06:11] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.973s
[2025-07-23 19:06:17] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.015s
[2025-07-23 19:06:23] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.016s
[2025-07-23 19:06:29] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.980s
[2025-07-23 19:06:35] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.973s
[2025-07-23 19:06:41] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.978s
[2025-07-23 19:06:47] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.013s
[2025-07-23 19:06:53] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.013s
[2025-07-23 19:06:59] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.014s
[2025-07-23 19:07:05] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.972s
[2025-07-23 19:07:11] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.972s
[2025-07-23 19:07:17] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.973s
[2025-07-23 19:07:23] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.972s
[2025-07-23 19:07:29] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.973s
[2025-07-23 19:07:35] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.012s
[2025-07-23 19:07:41] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.012s
[2025-07-23 19:07:47] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.015s
[2025-07-23 19:07:53] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.975s
[2025-07-23 19:07:59] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.973s
[2025-07-23 19:08:05] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.015s
[2025-07-23 19:08:11] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.015s
[2025-07-23 19:08:17] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.015s
[2025-07-23 19:08:23] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.973s
[2025-07-23 19:08:29] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.987s
[2025-07-23 19:08:35] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.974s
[2025-07-23 19:08:41] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.018s
[2025-07-23 19:08:47] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.013s
[2025-07-23 19:08:53] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.973s
[2025-07-23 19:08:59] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.977s
[2025-07-23 19:08:59] x方向二聚体相关函数计算完成,耗时: 394.38 秒
[2025-07-23 19:08:59] --------------------------------------------------------------------------------
[2025-07-23 19:08:59] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 19:09:03] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.598s
[2025-07-23 19:09:07] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.721s
[2025-07-23 19:09:13] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.018s
[2025-07-23 19:09:19] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.020s
[2025-07-23 19:09:25] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.988s
[2025-07-23 19:09:30] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.688s
[2025-07-23 19:09:36] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.020s
[2025-07-23 19:09:42] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.020s
[2025-07-23 19:09:48] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.020s
[2025-07-23 19:09:54] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 5.988s
[2025-07-23 19:10:00] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.980s
[2025-07-23 19:10:06] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.984s
[2025-07-23 19:10:12] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.018s
[2025-07-23 19:10:18] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.020s
[2025-07-23 19:10:24] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.979s
[2025-07-23 19:10:30] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.980s
[2025-07-23 19:10:36] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.980s
[2025-07-23 19:10:42] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.020s
[2025-07-23 19:10:48] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.019s
[2025-07-23 19:10:54] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.019s
[2025-07-23 19:11:00] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.981s
[2025-07-23 19:11:06] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.980s
[2025-07-23 19:11:12] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.017s
[2025-07-23 19:11:18] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.018s
[2025-07-23 19:11:24] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.019s
[2025-07-23 19:11:30] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.020s
[2025-07-23 19:11:36] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.020s
[2025-07-23 19:11:42] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.018s
[2025-07-23 19:11:48] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.980s
[2025-07-23 19:11:54] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.990s
[2025-07-23 19:12:00] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.018s
[2025-07-23 19:12:06] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.020s
[2025-07-23 19:12:12] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.020s
[2025-07-23 19:12:18] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.985s
[2025-07-23 19:12:24] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.980s
[2025-07-23 19:12:30] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.981s
[2025-07-23 19:12:36] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.019s
[2025-07-23 19:12:42] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.020s
[2025-07-23 19:12:48] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.980s
[2025-07-23 19:12:54] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 5.988s
[2025-07-23 19:13:00] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.982s
[2025-07-23 19:13:06] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.020s
[2025-07-23 19:13:12] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.018s
[2025-07-23 19:13:18] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.020s
[2025-07-23 19:13:24] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.981s
[2025-07-23 19:13:30] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.993s
[2025-07-23 19:13:36] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.019s
[2025-07-23 19:13:42] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.020s
[2025-07-23 19:13:48] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.020s
[2025-07-23 19:13:54] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.991s
[2025-07-23 19:14:00] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.981s
[2025-07-23 19:14:06] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 5.992s
[2025-07-23 19:14:12] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.019s
[2025-07-23 19:14:18] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.017s
[2025-07-23 19:14:24] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 5.991s
[2025-07-23 19:14:30] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.981s
[2025-07-23 19:14:36] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.982s
[2025-07-23 19:14:42] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.019s
[2025-07-23 19:14:48] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.018s
[2025-07-23 19:14:54] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.021s
[2025-07-23 19:15:00] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.982s
[2025-07-23 19:15:06] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 5.989s
[2025-07-23 19:15:12] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.018s
[2025-07-23 19:15:18] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.020s
[2025-07-23 19:15:18] y方向二聚体相关函数计算完成,耗时: 379.30 秒
[2025-07-23 19:15:18] 计算傅里叶变换...
[2025-07-23 19:15:19] 二聚体结构因子计算完成
[2025-07-23 19:15:20] 二聚体相关函数平均误差: 0.000510
[2025-07-23 19:15:20] 恢复原始样本数: 4096
[2025-07-23 19:15:20] ================================================================================
[2025-07-23 19:15:20] 所有分析完成
