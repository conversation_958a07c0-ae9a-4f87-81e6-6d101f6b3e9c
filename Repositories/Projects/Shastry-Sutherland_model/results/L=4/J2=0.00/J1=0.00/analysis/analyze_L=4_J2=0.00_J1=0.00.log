[2025-07-23 18:01:58] ================================================================================
[2025-07-23 18:01:58] 加载量子态: L=4, J2=0.00, J1=0.00
[2025-07-23 18:01:58] 设置样本数为: 1048576
[2025-07-23 18:01:58] 开始生成共享样本集...
[2025-07-23 18:03:09] 样本生成完成,耗时: 71.496 秒
[2025-07-23 18:03:09] ================================================================================
[2025-07-23 18:03:09] 开始计算自旋结构因子...
[2025-07-23 18:03:09] 初始化操作符缓存...
[2025-07-23 18:03:09] 预构建所有自旋相关操作符...
[2025-07-23 18:03:10] 开始计算自旋相关函数...
[2025-07-23 18:03:20] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 9.949s
[2025-07-23 18:03:29] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.273s
[2025-07-23 18:03:32] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.604s
[2025-07-23 18:03:36] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.602s
[2025-07-23 18:03:40] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.601s
[2025-07-23 18:03:43] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.598s
[2025-07-23 18:03:47] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.602s
[2025-07-23 18:03:50] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.577s
[2025-07-23 18:03:54] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.580s
[2025-07-23 18:03:58] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.602s
[2025-07-23 18:04:01] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.600s
[2025-07-23 18:04:05] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.600s
[2025-07-23 18:04:08] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.582s
[2025-07-23 18:04:12] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.578s
[2025-07-23 18:04:16] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.579s
[2025-07-23 18:04:19] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.602s
[2025-07-23 18:04:23] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.602s
[2025-07-23 18:04:26] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.581s
[2025-07-23 18:04:30] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.580s
[2025-07-23 18:04:33] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.582s
[2025-07-23 18:04:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.601s
[2025-07-23 18:04:41] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.601s
[2025-07-23 18:04:44] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.601s
[2025-07-23 18:04:48] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.579s
[2025-07-23 18:04:51] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.578s
[2025-07-23 18:04:55] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.601s
[2025-07-23 18:04:59] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.601s
[2025-07-23 18:05:02] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.599s
[2025-07-23 18:05:06] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.581s
[2025-07-23 18:05:09] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.582s
[2025-07-23 18:05:13] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.579s
[2025-07-23 18:05:17] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.598s
[2025-07-23 18:05:20] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.602s
[2025-07-23 18:05:24] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.577s
[2025-07-23 18:05:27] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.623s
[2025-07-23 18:05:31] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.581s
[2025-07-23 18:05:35] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.600s
[2025-07-23 18:05:38] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.601s
[2025-07-23 18:05:42] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.600s
[2025-07-23 18:05:45] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.577s
[2025-07-23 18:05:49] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.579s
[2025-07-23 18:05:53] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.600s
[2025-07-23 18:05:56] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.597s
[2025-07-23 18:06:00] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.597s
[2025-07-23 18:06:03] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.577s
[2025-07-23 18:06:07] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.577s
[2025-07-23 18:06:10] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.581s
[2025-07-23 18:06:14] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.578s
[2025-07-23 18:06:18] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.578s
[2025-07-23 18:06:21] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.601s
[2025-07-23 18:06:25] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.601s
[2025-07-23 18:06:28] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.601s
[2025-07-23 18:06:32] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.581s
[2025-07-23 18:06:36] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.582s
[2025-07-23 18:06:39] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.577s
[2025-07-23 18:06:43] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.599s
[2025-07-23 18:06:46] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.600s
[2025-07-23 18:06:50] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.580s
[2025-07-23 18:06:54] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.580s
[2025-07-23 18:06:57] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.578s
[2025-07-23 18:07:01] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.600s
[2025-07-23 18:07:04] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.598s
[2025-07-23 18:07:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.601s
[2025-07-23 18:07:12] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.577s
[2025-07-23 18:07:12] 自旋相关函数计算完成,总耗时 241.94 秒
[2025-07-23 18:07:12] 计算傅里叶变换...
[2025-07-23 18:07:12] 自旋结构因子计算完成
[2025-07-23 18:07:13] 自旋相关函数平均误差: 0.000678
[2025-07-23 18:07:13] ================================================================================
[2025-07-23 18:07:13] 开始计算二聚体结构因子...
[2025-07-23 18:07:13] 识别x和y方向的二聚体...
[2025-07-23 18:07:13] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 18:07:13] 预计算二聚体操作符...
[2025-07-23 18:07:14] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 18:07:18] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.624s
[2025-07-23 18:07:32] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.725s
[2025-07-23 18:07:37] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.744s
[2025-07-23 18:07:49] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.612s
[2025-07-23 18:07:55] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.010s
[2025-07-23 18:08:01] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.012s
[2025-07-23 18:08:07] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.978s
[2025-07-23 18:08:13] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.967s
[2025-07-23 18:08:19] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.012s
[2025-07-23 18:08:25] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.008s
[2025-07-23 18:08:31] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.013s
[2025-07-23 18:08:37] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.011s
[2025-07-23 18:08:43] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.982s
[2025-07-23 18:08:49] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.969s
[2025-07-23 18:08:55] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.011s
[2025-07-23 18:09:01] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.013s
[2025-07-23 18:09:07] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.009s
[2025-07-23 18:09:13] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.968s
[2025-07-23 18:09:19] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.973s
[2025-07-23 18:09:25] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.970s
[2025-07-23 18:09:31] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.011s
[2025-07-23 18:09:37] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.011s
[2025-07-23 18:09:43] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.981s
[2025-07-23 18:09:49] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.967s
[2025-07-23 18:09:55] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.977s
[2025-07-23 18:10:01] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.008s
[2025-07-23 18:10:07] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.011s
[2025-07-23 18:10:13] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.008s
[2025-07-23 18:10:19] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 5.974s
[2025-07-23 18:10:25] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.964s
[2025-07-23 18:10:31] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.010s
[2025-07-23 18:10:37] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.010s
[2025-07-23 18:10:43] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.007s
[2025-07-23 18:10:49] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 5.977s
[2025-07-23 18:10:55] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.971s
[2025-07-23 18:11:01] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.965s
[2025-07-23 18:11:07] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.010s
[2025-07-23 18:11:13] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.011s
[2025-07-23 18:11:19] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.980s
[2025-07-23 18:11:25] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.967s
[2025-07-23 18:11:31] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.980s
[2025-07-23 18:11:37] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.009s
[2025-07-23 18:11:43] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.008s
[2025-07-23 18:11:49] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.009s
[2025-07-23 18:11:55] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.974s
[2025-07-23 18:12:01] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.964s
[2025-07-23 18:12:06] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.967s
[2025-07-23 18:12:12] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.970s
[2025-07-23 18:12:18] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.965s
[2025-07-23 18:12:24] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.006s
[2025-07-23 18:12:30] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.006s
[2025-07-23 18:12:36] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.010s
[2025-07-23 18:12:42] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.978s
[2025-07-23 18:12:48] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.966s
[2025-07-23 18:12:54] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.010s
[2025-07-23 18:13:00] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.010s
[2025-07-23 18:13:06] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.011s
[2025-07-23 18:13:12] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.966s
[2025-07-23 18:13:18] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.983s
[2025-07-23 18:13:24] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.964s
[2025-07-23 18:13:30] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.011s
[2025-07-23 18:13:36] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.008s
[2025-07-23 18:13:42] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.967s
[2025-07-23 18:13:48] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.978s
[2025-07-23 18:13:48] x方向二聚体相关函数计算完成,耗时: 394.24 秒
[2025-07-23 18:13:48] --------------------------------------------------------------------------------
[2025-07-23 18:13:48] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 18:13:52] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.596s
[2025-07-23 18:13:57] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.717s
[2025-07-23 18:14:03] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.012s
[2025-07-23 18:14:09] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.018s
[2025-07-23 18:14:15] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.988s
[2025-07-23 18:14:19] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.691s
[2025-07-23 18:14:25] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.017s
[2025-07-23 18:14:31] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.013s
[2025-07-23 18:14:37] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.012s
[2025-07-23 18:14:43] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 5.988s
[2025-07-23 18:14:49] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.973s
[2025-07-23 18:14:55] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.984s
[2025-07-23 18:15:01] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.010s
[2025-07-23 18:15:07] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.011s
[2025-07-23 18:15:13] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.971s
[2025-07-23 18:15:19] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.981s
[2025-07-23 18:15:25] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.972s
[2025-07-23 18:15:31] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.015s
[2025-07-23 18:15:37] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.011s
[2025-07-23 18:15:43] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.014s
[2025-07-23 18:15:49] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.971s
[2025-07-23 18:15:55] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.972s
[2025-07-23 18:16:01] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.011s
[2025-07-23 18:16:07] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.009s
[2025-07-23 18:16:13] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.015s
[2025-07-23 18:16:19] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.011s
[2025-07-23 18:16:25] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.012s
[2025-07-23 18:16:31] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.010s
[2025-07-23 18:16:37] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.973s
[2025-07-23 18:16:43] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.991s
[2025-07-23 18:16:49] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.010s
[2025-07-23 18:16:55] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.013s
[2025-07-23 18:17:01] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.012s
[2025-07-23 18:17:07] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.988s
[2025-07-23 18:17:13] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.973s
[2025-07-23 18:17:19] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.984s
[2025-07-23 18:17:25] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.014s
[2025-07-23 18:17:31] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.011s
[2025-07-23 18:17:37] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.983s
[2025-07-23 18:17:43] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 5.988s
[2025-07-23 18:17:49] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.972s
[2025-07-23 18:17:55] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.010s
[2025-07-23 18:18:01] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.012s
[2025-07-23 18:18:07] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.014s
[2025-07-23 18:18:13] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.972s
[2025-07-23 18:18:19] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.989s
[2025-07-23 18:18:25] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.010s
[2025-07-23 18:18:31] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.010s
[2025-07-23 18:18:37] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.014s
[2025-07-23 18:18:43] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.990s
[2025-07-23 18:18:49] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.975s
[2025-07-23 18:18:55] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 5.990s
[2025-07-23 18:19:01] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.014s
[2025-07-23 18:19:07] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.012s
[2025-07-23 18:19:13] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 5.989s
[2025-07-23 18:19:19] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.974s
[2025-07-23 18:19:25] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.972s
[2025-07-23 18:19:31] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.009s
[2025-07-23 18:19:37] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.011s
[2025-07-23 18:19:43] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.018s
[2025-07-23 18:19:49] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.973s
[2025-07-23 18:19:55] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 5.991s
[2025-07-23 18:20:01] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.011s
[2025-07-23 18:20:07] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.012s
[2025-07-23 18:20:07] y方向二聚体相关函数计算完成,耗时: 378.98 秒
[2025-07-23 18:20:07] 计算傅里叶变换...
[2025-07-23 18:20:08] 二聚体结构因子计算完成
[2025-07-23 18:20:09] 二聚体相关函数平均误差: 0.000516
[2025-07-23 18:20:09] 恢复原始样本数: 4096
[2025-07-23 18:20:09] ================================================================================
[2025-07-23 18:20:09] 所有分析完成
