[2025-07-23 18:38:49] ================================================================================
[2025-07-23 18:38:49] 加载量子态: L=4, J2=0.00, J1=0.02
[2025-07-23 18:38:49] 设置样本数为: 1048576
[2025-07-23 18:38:49] 开始生成共享样本集...
[2025-07-23 18:39:59] 样本生成完成,耗时: 70.137 秒
[2025-07-23 18:39:59] ================================================================================
[2025-07-23 18:39:59] 开始计算自旋结构因子...
[2025-07-23 18:39:59] 初始化操作符缓存...
[2025-07-23 18:39:59] 预构建所有自旋相关操作符...
[2025-07-23 18:39:59] 开始计算自旋相关函数...
[2025-07-23 18:40:08] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.542s
[2025-07-23 18:40:17] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.149s
[2025-07-23 18:40:21] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.602s
[2025-07-23 18:40:24] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.601s
[2025-07-23 18:40:28] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.601s
[2025-07-23 18:40:32] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.598s
[2025-07-23 18:40:35] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.601s
[2025-07-23 18:40:39] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.576s
[2025-07-23 18:40:42] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.578s
[2025-07-23 18:40:46] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.600s
[2025-07-23 18:40:49] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.600s
[2025-07-23 18:40:53] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.598s
[2025-07-23 18:40:57] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.581s
[2025-07-23 18:41:00] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.576s
[2025-07-23 18:41:04] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.577s
[2025-07-23 18:41:07] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.600s
[2025-07-23 18:41:11] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.601s
[2025-07-23 18:41:15] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.579s
[2025-07-23 18:41:18] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.578s
[2025-07-23 18:41:22] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.580s
[2025-07-23 18:41:25] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.600s
[2025-07-23 18:41:29] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.600s
[2025-07-23 18:41:33] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.602s
[2025-07-23 18:41:36] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.578s
[2025-07-23 18:41:40] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.577s
[2025-07-23 18:41:43] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.601s
[2025-07-23 18:41:47] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.600s
[2025-07-23 18:41:51] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.598s
[2025-07-23 18:41:54] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.579s
[2025-07-23 18:41:58] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.578s
[2025-07-23 18:42:01] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.578s
[2025-07-23 18:42:05] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.598s
[2025-07-23 18:42:08] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.601s
[2025-07-23 18:42:12] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.576s
[2025-07-23 18:42:16] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.578s
[2025-07-23 18:42:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.580s
[2025-07-23 18:42:23] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.599s
[2025-07-23 18:42:26] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.599s
[2025-07-23 18:42:30] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.600s
[2025-07-23 18:42:34] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.577s
[2025-07-23 18:42:37] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.576s
[2025-07-23 18:42:41] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.599s
[2025-07-23 18:42:44] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.596s
[2025-07-23 18:42:48] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.596s
[2025-07-23 18:42:52] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.576s
[2025-07-23 18:42:55] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.661s
[2025-07-23 18:42:59] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.580s
[2025-07-23 18:43:02] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.577s
[2025-07-23 18:43:06] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.576s
[2025-07-23 18:43:10] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.600s
[2025-07-23 18:43:13] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.600s
[2025-07-23 18:43:17] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.600s
[2025-07-23 18:43:20] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.580s
[2025-07-23 18:43:24] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.581s
[2025-07-23 18:43:27] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.576s
[2025-07-23 18:43:31] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.599s
[2025-07-23 18:43:35] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.599s
[2025-07-23 18:43:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.580s
[2025-07-23 18:43:42] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.579s
[2025-07-23 18:43:45] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.577s
[2025-07-23 18:43:49] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.599s
[2025-07-23 18:43:53] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.597s
[2025-07-23 18:43:56] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.600s
[2025-07-23 18:44:00] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.575s
[2025-07-23 18:44:00] 自旋相关函数计算完成,总耗时 240.37 秒
[2025-07-23 18:44:00] 计算傅里叶变换...
[2025-07-23 18:44:00] 自旋结构因子计算完成
[2025-07-23 18:44:01] 自旋相关函数平均误差: 0.000675
[2025-07-23 18:44:01] ================================================================================
[2025-07-23 18:44:01] 开始计算二聚体结构因子...
[2025-07-23 18:44:01] 识别x和y方向的二聚体...
[2025-07-23 18:44:01] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 18:44:01] 预计算二聚体操作符...
[2025-07-23 18:44:02] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 18:44:06] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.623s
[2025-07-23 18:44:21] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.659s
[2025-07-23 18:44:25] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.743s
[2025-07-23 18:44:37] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.597s
[2025-07-23 18:44:43] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 6.011s
[2025-07-23 18:44:49] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 6.014s
[2025-07-23 18:44:55] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.969s
[2025-07-23 18:45:01] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.969s
[2025-07-23 18:45:07] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 6.013s
[2025-07-23 18:45:13] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 6.182s
[2025-07-23 18:45:19] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 6.014s
[2025-07-23 18:45:25] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 6.032s
[2025-07-23 18:45:31] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.968s
[2025-07-23 18:45:37] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.970s
[2025-07-23 18:45:43] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 6.012s
[2025-07-23 18:45:49] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 6.015s
[2025-07-23 18:45:55] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 6.032s
[2025-07-23 18:46:01] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.969s
[2025-07-23 18:46:07] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.967s
[2025-07-23 18:46:13] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.969s
[2025-07-23 18:46:19] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 6.012s
[2025-07-23 18:46:25] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 6.072s
[2025-07-23 18:46:31] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.968s
[2025-07-23 18:46:37] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.968s
[2025-07-23 18:46:43] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.968s
[2025-07-23 18:46:49] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 6.009s
[2025-07-23 18:46:55] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 6.082s
[2025-07-23 18:47:01] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 6.009s
[2025-07-23 18:47:07] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 5.967s
[2025-07-23 18:47:13] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.966s
[2025-07-23 18:47:19] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 6.011s
[2025-07-23 18:47:25] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 6.041s
[2025-07-23 18:47:31] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 6.008s
[2025-07-23 18:47:37] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 5.968s
[2025-07-23 18:47:43] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.966s
[2025-07-23 18:47:49] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.967s
[2025-07-23 18:47:55] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 6.084s
[2025-07-23 18:48:01] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 6.013s
[2025-07-23 18:48:07] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.967s
[2025-07-23 18:48:13] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.969s
[2025-07-23 18:48:19] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.969s
[2025-07-23 18:48:25] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 6.074s
[2025-07-23 18:48:31] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 6.009s
[2025-07-23 18:48:37] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 6.010s
[2025-07-23 18:48:43] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.966s
[2025-07-23 18:48:49] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.966s
[2025-07-23 18:48:55] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 6.041s
[2025-07-23 18:49:01] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.967s
[2025-07-23 18:49:07] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.966s
[2025-07-23 18:49:13] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 6.007s
[2025-07-23 18:49:19] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 6.007s
[2025-07-23 18:49:25] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 6.045s
[2025-07-23 18:49:31] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.967s
[2025-07-23 18:49:37] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.967s
[2025-07-23 18:49:43] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 6.012s
[2025-07-23 18:49:49] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 6.011s
[2025-07-23 18:49:55] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 6.035s
[2025-07-23 18:50:01] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.967s
[2025-07-23 18:50:07] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.968s
[2025-07-23 18:50:13] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.965s
[2025-07-23 18:50:19] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 6.012s
[2025-07-23 18:50:25] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 6.088s
[2025-07-23 18:50:31] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.968s
[2025-07-23 18:50:37] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.968s
[2025-07-23 18:50:37] x方向二聚体相关函数计算完成,耗时: 394.79 秒
[2025-07-23 18:50:37] --------------------------------------------------------------------------------
[2025-07-23 18:50:37] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 18:50:41] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.598s
[2025-07-23 18:50:45] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.719s
[2025-07-23 18:50:51] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 6.012s
[2025-07-23 18:50:57] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 6.017s
[2025-07-23 18:51:03] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.972s
[2025-07-23 18:51:08] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.690s
[2025-07-23 18:51:14] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 6.016s
[2025-07-23 18:51:20] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 6.011s
[2025-07-23 18:51:26] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 6.010s
[2025-07-23 18:51:32] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 6.015s
[2025-07-23 18:51:38] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.973s
[2025-07-23 18:51:44] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.972s
[2025-07-23 18:51:50] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 6.010s
[2025-07-23 18:51:56] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 6.011s
[2025-07-23 18:52:02] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 6.029s
[2025-07-23 18:52:08] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.971s
[2025-07-23 18:52:14] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.971s
[2025-07-23 18:52:20] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 6.014s
[2025-07-23 18:52:26] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 6.010s
[2025-07-23 18:52:32] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 6.026s
[2025-07-23 18:52:38] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.971s
[2025-07-23 18:52:44] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.971s
[2025-07-23 18:52:50] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 6.010s
[2025-07-23 18:52:56] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 6.009s
[2025-07-23 18:53:02] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 6.035s
[2025-07-23 18:53:08] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 6.010s
[2025-07-23 18:53:14] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 6.010s
[2025-07-23 18:53:20] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 6.010s
[2025-07-23 18:53:26] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.971s
[2025-07-23 18:53:32] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.991s
[2025-07-23 18:53:38] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 6.010s
[2025-07-23 18:53:44] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 6.011s
[2025-07-23 18:53:50] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 6.010s
[2025-07-23 18:53:56] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.972s
[2025-07-23 18:54:02] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.985s
[2025-07-23 18:54:08] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.971s
[2025-07-23 18:54:14] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 6.012s
[2025-07-23 18:54:20] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 6.010s
[2025-07-23 18:54:26] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.970s
[2025-07-23 18:54:32] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 6.030s
[2025-07-23 18:54:38] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.970s
[2025-07-23 18:54:44] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 6.009s
[2025-07-23 18:54:50] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 6.011s
[2025-07-23 18:54:56] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 6.013s
[2025-07-23 18:55:02] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.993s
[2025-07-23 18:55:08] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.971s
[2025-07-23 18:55:14] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 6.010s
[2025-07-23 18:55:20] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 6.009s
[2025-07-23 18:55:26] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 6.013s
[2025-07-23 18:55:32] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.988s
[2025-07-23 18:55:38] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.973s
[2025-07-23 18:55:44] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 5.972s
[2025-07-23 18:55:50] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.014s
[2025-07-23 18:55:56] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 6.011s
[2025-07-23 18:56:02] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 6.025s
[2025-07-23 18:56:08] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.972s
[2025-07-23 18:56:14] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.972s
[2025-07-23 18:56:20] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 6.009s
[2025-07-23 18:56:26] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 6.011s
[2025-07-23 18:56:32] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 6.024s
[2025-07-23 18:56:38] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.972s
[2025-07-23 18:56:44] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 5.972s
[2025-07-23 18:56:50] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 6.011s
[2025-07-23 18:56:56] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 6.011s
[2025-07-23 18:56:56] y方向二聚体相关函数计算完成,耗时: 379.05 秒
[2025-07-23 18:56:56] 计算傅里叶变换...
[2025-07-23 18:56:57] 二聚体结构因子计算完成
[2025-07-23 18:56:58] 二聚体相关函数平均误差: 0.000506
[2025-07-23 18:56:58] 恢复原始样本数: 4096
[2025-07-23 18:56:58] ================================================================================
[2025-07-23 18:56:58] 所有分析完成
