[2025-07-23 20:47:36] ================================================================================
[2025-07-23 20:47:36] 加载量子态: L=4, J2=0.00, J1=0.09
[2025-07-23 20:47:36] 设置样本数为: 1048576
[2025-07-23 20:47:36] 开始生成共享样本集...
[2025-07-23 20:48:46] 样本生成完成,耗时: 70.295 秒
[2025-07-23 20:48:46] ================================================================================
[2025-07-23 20:48:46] 开始计算自旋结构因子...
[2025-07-23 20:48:46] 初始化操作符缓存...
[2025-07-23 20:48:46] 预构建所有自旋相关操作符...
[2025-07-23 20:48:46] 开始计算自旋相关函数...
[2025-07-23 20:48:55] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.576s
[2025-07-23 20:49:04] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.176s
[2025-07-23 20:49:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.578s
[2025-07-23 20:49:11] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.579s
[2025-07-23 20:49:15] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.580s
[2025-07-23 20:49:18] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.572s
[2025-07-23 20:49:22] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.574s
[2025-07-23 20:49:26] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.572s
[2025-07-23 20:49:29] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.566s
[2025-07-23 20:49:33] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.573s
[2025-07-23 20:49:36] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.579s
[2025-07-23 20:49:40] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.570s
[2025-07-23 20:49:43] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.578s
[2025-07-23 20:49:47] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.569s
[2025-07-23 20:49:51] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.573s
[2025-07-23 20:49:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.573s
[2025-07-23 20:49:58] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.573s
[2025-07-23 20:50:01] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.568s
[2025-07-23 20:50:05] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.576s
[2025-07-23 20:50:08] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.573s
[2025-07-23 20:50:12] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.571s
[2025-07-23 20:50:16] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.571s
[2025-07-23 20:50:19] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.574s
[2025-07-23 20:50:23] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.570s
[2025-07-23 20:50:26] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.574s
[2025-07-23 20:50:30] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.574s
[2025-07-23 20:50:33] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.572s
[2025-07-23 20:50:37] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.574s
[2025-07-23 20:50:41] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.567s
[2025-07-23 20:50:44] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.568s
[2025-07-23 20:50:48] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.573s
[2025-07-23 20:50:51] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.572s
[2025-07-23 20:50:55] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.575s
[2025-07-23 20:50:58] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.578s
[2025-07-23 20:51:02] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.577s
[2025-07-23 20:51:06] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.578s
[2025-07-23 20:51:09] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.571s
[2025-07-23 20:51:13] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.572s
[2025-07-23 20:51:16] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.577s
[2025-07-23 20:51:20] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.574s
[2025-07-23 20:51:23] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.579s
[2025-07-23 20:51:27] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.574s
[2025-07-23 20:51:31] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.575s
[2025-07-23 20:51:34] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.569s
[2025-07-23 20:51:38] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.575s
[2025-07-23 20:51:41] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.576s
[2025-07-23 20:51:45] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.570s
[2025-07-23 20:51:49] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.576s
[2025-07-23 20:51:52] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.569s
[2025-07-23 20:51:56] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.576s
[2025-07-23 20:51:59] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.576s
[2025-07-23 20:52:03] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.573s
[2025-07-23 20:52:06] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.576s
[2025-07-23 20:52:10] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.574s
[2025-07-23 20:52:14] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.570s
[2025-07-23 20:52:17] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.576s
[2025-07-23 20:52:21] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.572s
[2025-07-23 20:52:24] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.576s
[2025-07-23 20:52:28] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.578s
[2025-07-23 20:52:31] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.572s
[2025-07-23 20:52:35] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.577s
[2025-07-23 20:52:39] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.578s
[2025-07-23 20:52:42] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.577s
[2025-07-23 20:52:46] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.572s
[2025-07-23 20:52:46] 自旋相关函数计算完成,总耗时 239.41 秒
[2025-07-23 20:52:46] 计算傅里叶变换...
[2025-07-23 20:52:46] 自旋结构因子计算完成
[2025-07-23 20:52:47] 自旋相关函数平均误差: 0.000649
[2025-07-23 20:52:47] ================================================================================
[2025-07-23 20:52:47] 开始计算二聚体结构因子...
[2025-07-23 20:52:47] 识别x和y方向的二聚体...
[2025-07-23 20:52:47] 找到 64 个x方向二聚体和 64 个y方向二聚体
[2025-07-23 20:52:47] 预计算二聚体操作符...
[2025-07-23 20:52:48] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 20:52:52] x方向算符进度: 1/64, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 3.610s
[2025-07-23 20:53:07] x方向算符进度: 2/64, 当前算符: D_(0, 1) * D_(0, 49), 耗时: 14.701s
[2025-07-23 20:53:11] x方向算符进度: 3/64, 当前算符: D_(0, 1) * D_(1, 16), 耗时: 4.701s
[2025-07-23 20:53:23] x方向算符进度: 4/64, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 11.604s
[2025-07-23 20:53:29] x方向算符进度: 5/64, 当前算符: D_(0, 1) * D_(2, 19), 耗时: 5.962s
[2025-07-23 20:53:35] x方向算符进度: 6/64, 当前算符: D_(0, 1) * D_(3, 50), 耗时: 5.972s
[2025-07-23 20:53:41] x方向算符进度: 7/64, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 5.962s
[2025-07-23 20:53:47] x方向算符进度: 8/64, 当前算符: D_(0, 1) * D_(4, 53), 耗时: 5.959s
[2025-07-23 20:53:53] x方向算符进度: 9/64, 当前算符: D_(0, 1) * D_(5, 20), 耗时: 5.968s
[2025-07-23 20:53:59] x方向算符进度: 10/64, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 5.959s
[2025-07-23 20:54:05] x方向算符进度: 11/64, 当前算符: D_(0, 1) * D_(6, 23), 耗时: 5.970s
[2025-07-23 20:54:11] x方向算符进度: 12/64, 当前算符: D_(0, 1) * D_(7, 54), 耗时: 5.965s
[2025-07-23 20:54:17] x方向算符进度: 13/64, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 5.963s
[2025-07-23 20:54:23] x方向算符进度: 14/64, 当前算符: D_(0, 1) * D_(8, 57), 耗时: 5.959s
[2025-07-23 20:54:29] x方向算符进度: 15/64, 当前算符: D_(0, 1) * D_(9, 24), 耗时: 5.963s
[2025-07-23 20:54:35] x方向算符进度: 16/64, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 5.969s
[2025-07-23 20:54:41] x方向算符进度: 17/64, 当前算符: D_(0, 1) * D_(10, 27), 耗时: 5.958s
[2025-07-23 20:54:46] x方向算符进度: 18/64, 当前算符: D_(0, 1) * D_(11, 58), 耗时: 5.953s
[2025-07-23 20:54:52] x方向算符进度: 19/64, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 5.960s
[2025-07-23 20:54:58] x方向算符进度: 20/64, 当前算符: D_(0, 1) * D_(12, 61), 耗时: 5.955s
[2025-07-23 20:55:04] x方向算符进度: 21/64, 当前算符: D_(0, 1) * D_(13, 28), 耗时: 5.972s
[2025-07-23 20:55:10] x方向算符进度: 22/64, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 5.965s
[2025-07-23 20:55:16] x方向算符进度: 23/64, 当前算符: D_(0, 1) * D_(14, 31), 耗时: 5.962s
[2025-07-23 20:55:22] x方向算符进度: 24/64, 当前算符: D_(0, 1) * D_(15, 62), 耗时: 5.953s
[2025-07-23 20:55:28] x方向算符进度: 25/64, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 5.961s
[2025-07-23 20:55:34] x方向算符进度: 26/64, 当前算符: D_(0, 1) * D_(17, 32), 耗时: 5.959s
[2025-07-23 20:55:40] x方向算符进度: 27/64, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 5.973s
[2025-07-23 20:55:46] x方向算符进度: 28/64, 当前算符: D_(0, 1) * D_(18, 35), 耗时: 5.956s
[2025-07-23 20:55:52] x方向算符进度: 29/64, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 5.961s
[2025-07-23 20:55:58] x方向算符进度: 30/64, 当前算符: D_(0, 1) * D_(21, 36), 耗时: 5.954s
[2025-07-23 20:56:04] x方向算符进度: 31/64, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 5.963s
[2025-07-23 20:56:10] x方向算符进度: 32/64, 当前算符: D_(0, 1) * D_(22, 39), 耗时: 5.970s
[2025-07-23 20:56:16] x方向算符进度: 33/64, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 5.959s
[2025-07-23 20:56:22] x方向算符进度: 34/64, 当前算符: D_(0, 1) * D_(25, 40), 耗时: 5.961s
[2025-07-23 20:56:28] x方向算符进度: 35/64, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 5.960s
[2025-07-23 20:56:34] x方向算符进度: 36/64, 当前算符: D_(0, 1) * D_(26, 43), 耗时: 5.955s
[2025-07-23 20:56:40] x方向算符进度: 37/64, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 5.967s
[2025-07-23 20:56:46] x方向算符进度: 38/64, 当前算符: D_(0, 1) * D_(29, 44), 耗时: 5.968s
[2025-07-23 20:56:52] x方向算符进度: 39/64, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 5.962s
[2025-07-23 20:56:58] x方向算符进度: 40/64, 当前算符: D_(0, 1) * D_(30, 47), 耗时: 5.954s
[2025-07-23 20:57:04] x方向算符进度: 41/64, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 5.962s
[2025-07-23 20:57:10] x方向算符进度: 42/64, 当前算符: D_(0, 1) * D_(33, 48), 耗时: 5.960s
[2025-07-23 20:57:16] x方向算符进度: 43/64, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 5.960s
[2025-07-23 20:57:21] x方向算符进度: 44/64, 当前算符: D_(0, 1) * D_(34, 51), 耗时: 5.961s
[2025-07-23 20:57:27] x方向算符进度: 45/64, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 5.961s
[2025-07-23 20:57:33] x方向算符进度: 46/64, 当前算符: D_(0, 1) * D_(37, 52), 耗时: 5.954s
[2025-07-23 20:57:39] x方向算符进度: 47/64, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 5.956s
[2025-07-23 20:57:45] x方向算符进度: 48/64, 当前算符: D_(0, 1) * D_(38, 55), 耗时: 5.960s
[2025-07-23 20:57:51] x方向算符进度: 49/64, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 5.954s
[2025-07-23 20:57:57] x方向算符进度: 50/64, 当前算符: D_(0, 1) * D_(41, 56), 耗时: 5.962s
[2025-07-23 20:58:03] x方向算符进度: 51/64, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 5.954s
[2025-07-23 20:58:09] x方向算符进度: 52/64, 当前算符: D_(0, 1) * D_(42, 59), 耗时: 5.967s
[2025-07-23 20:58:15] x方向算符进度: 53/64, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 5.961s
[2025-07-23 20:58:21] x方向算符进度: 54/64, 当前算符: D_(0, 1) * D_(45, 60), 耗时: 5.958s
[2025-07-23 20:58:27] x方向算符进度: 55/64, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 5.968s
[2025-07-23 20:58:33] x方向算符进度: 56/64, 当前算符: D_(0, 1) * D_(46, 63), 耗时: 5.962s
[2025-07-23 20:58:39] x方向算符进度: 57/64, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 5.968s
[2025-07-23 20:58:45] x方向算符进度: 58/64, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 5.956s
[2025-07-23 20:58:51] x方向算符进度: 59/64, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 5.967s
[2025-07-23 20:58:57] x方向算符进度: 60/64, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 5.957s
[2025-07-23 20:59:03] x方向算符进度: 61/64, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 5.972s
[2025-07-23 20:59:09] x方向算符进度: 62/64, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 5.957s
[2025-07-23 20:59:15] x方向算符进度: 63/64, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 5.952s
[2025-07-23 20:59:21] x方向算符进度: 64/64, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 5.963s
[2025-07-23 20:59:21] x方向二聚体相关函数计算完成,耗时: 392.36 秒
[2025-07-23 20:59:21] --------------------------------------------------------------------------------
[2025-07-23 20:59:21] 使用y-二聚体 (0, 3) (全局索引 64) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 20:59:24] y方向算符进度: 1/64, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 3.574s
[2025-07-23 20:59:29] y方向算符进度: 2/64, 当前算符: D_(0, 3) * D_(0, 15), 耗时: 4.685s
[2025-07-23 20:59:35] y方向算符进度: 3/64, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 5.965s
[2025-07-23 20:59:41] y方向算符进度: 4/64, 当前算符: D_(0, 3) * D_(1, 14), 耗时: 5.978s
[2025-07-23 20:59:47] y方向算符进度: 5/64, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 5.970s
[2025-07-23 20:59:52] y方向算符进度: 6/64, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 4.682s
[2025-07-23 20:59:58] y方向算符进度: 7/64, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 5.977s
[2025-07-23 21:00:04] y方向算符进度: 8/64, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 5.972s
[2025-07-23 21:00:09] y方向算符进度: 9/64, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 5.970s
[2025-07-23 21:00:15] y方向算符进度: 10/64, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 5.970s
[2025-07-23 21:00:21] y方向算符进度: 11/64, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 5.964s
[2025-07-23 21:00:27] y方向算符进度: 12/64, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 5.977s
[2025-07-23 21:00:33] y方向算符进度: 13/64, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 5.967s
[2025-07-23 21:00:39] y方向算符进度: 14/64, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 5.969s
[2025-07-23 21:00:45] y方向算符进度: 15/64, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 5.971s
[2025-07-23 21:00:51] y方向算符进度: 16/64, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 5.971s
[2025-07-23 21:00:57] y方向算符进度: 17/64, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 5.966s
[2025-07-23 21:01:03] y方向算符进度: 18/64, 当前算符: D_(0, 3) * D_(16, 31), 耗时: 5.974s
[2025-07-23 21:01:09] y方向算符进度: 19/64, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 5.967s
[2025-07-23 21:01:15] y方向算符进度: 20/64, 当前算符: D_(0, 3) * D_(17, 30), 耗时: 5.974s
[2025-07-23 21:01:21] y方向算符进度: 21/64, 当前算符: D_(0, 3) * D_(18, 21), 耗时: 5.961s
[2025-07-23 21:01:27] y方向算符进度: 22/64, 当前算符: D_(0, 3) * D_(19, 20), 耗时: 5.971s
[2025-07-23 21:01:33] y方向算符进度: 23/64, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 5.960s
[2025-07-23 21:01:39] y方向算符进度: 24/64, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 5.973s
[2025-07-23 21:01:45] y方向算符进度: 25/64, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 5.975s
[2025-07-23 21:01:51] y方向算符进度: 26/64, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 5.968s
[2025-07-23 21:01:57] y方向算符进度: 27/64, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 5.970s
[2025-07-23 21:02:03] y方向算符进度: 28/64, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 5.973s
[2025-07-23 21:02:09] y方向算符进度: 29/64, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 5.964s
[2025-07-23 21:02:15] y方向算符进度: 30/64, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 5.970s
[2025-07-23 21:02:21] y方向算符进度: 31/64, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 5.963s
[2025-07-23 21:02:27] y方向算符进度: 32/64, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 5.972s
[2025-07-23 21:02:33] y方向算符进度: 33/64, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 5.970s
[2025-07-23 21:02:39] y方向算符进度: 34/64, 当前算符: D_(0, 3) * D_(32, 47), 耗时: 5.966s
[2025-07-23 21:02:45] y方向算符进度: 35/64, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 5.958s
[2025-07-23 21:02:51] y方向算符进度: 36/64, 当前算符: D_(0, 3) * D_(33, 46), 耗时: 5.966s
[2025-07-23 21:02:57] y方向算符进度: 37/64, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 5.974s
[2025-07-23 21:03:03] y方向算符进度: 38/64, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 5.966s
[2025-07-23 21:03:09] y方向算符进度: 39/64, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 5.965s
[2025-07-23 21:03:15] y方向算符进度: 40/64, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 5.968s
[2025-07-23 21:03:21] y方向算符进度: 41/64, 当前算符: D_(0, 3) * D_(38, 41), 耗时: 5.959s
[2025-07-23 21:03:26] y方向算符进度: 42/64, 当前算符: D_(0, 3) * D_(39, 40), 耗时: 5.967s
[2025-07-23 21:03:32] y方向算符进度: 43/64, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 5.961s
[2025-07-23 21:03:38] y方向算符进度: 44/64, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 5.974s
[2025-07-23 21:03:44] y方向算符进度: 45/64, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 5.958s
[2025-07-23 21:03:50] y方向算符进度: 46/64, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 5.966s
[2025-07-23 21:03:56] y方向算符进度: 47/64, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 5.960s
[2025-07-23 21:04:02] y方向算符进度: 48/64, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 5.967s
[2025-07-23 21:04:08] y方向算符进度: 49/64, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 5.973s
[2025-07-23 21:04:14] y方向算符进度: 50/64, 当前算符: D_(0, 3) * D_(48, 63), 耗时: 5.967s
[2025-07-23 21:04:20] y方向算符进度: 51/64, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 5.957s
[2025-07-23 21:04:26] y方向算符进度: 52/64, 当前算符: D_(0, 3) * D_(49, 62), 耗时: 5.966s
[2025-07-23 21:04:32] y方向算符进度: 53/64, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 6.064s
[2025-07-23 21:04:38] y方向算符进度: 54/64, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 5.960s
[2025-07-23 21:04:44] y方向算符进度: 55/64, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 5.967s
[2025-07-23 21:04:50] y方向算符进度: 56/64, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 5.961s
[2025-07-23 21:04:56] y方向算符进度: 57/64, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 5.958s
[2025-07-23 21:05:02] y方向算符进度: 58/64, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 5.970s
[2025-07-23 21:05:08] y方向算符进度: 59/64, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 5.962s
[2025-07-23 21:05:14] y方向算符进度: 60/64, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 5.978s
[2025-07-23 21:05:20] y方向算符进度: 61/64, 当前算符: D_(0, 3) * D_(58, 61), 耗时: 5.957s
[2025-07-23 21:05:26] y方向算符进度: 62/64, 当前算符: D_(0, 3) * D_(59, 60), 耗时: 5.967s
[2025-07-23 21:05:32] y方向算符进度: 63/64, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 5.964s
[2025-07-23 21:05:38] y方向算符进度: 64/64, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 5.970s
[2025-07-23 21:05:38] y方向二聚体相关函数计算完成,耗时: 377.12 秒
[2025-07-23 21:05:38] 计算傅里叶变换...
[2025-07-23 21:05:39] 二聚体结构因子计算完成
[2025-07-23 21:05:39] 二聚体相关函数平均误差: 0.000487
[2025-07-23 21:05:39] 恢复原始样本数: 4096
[2025-07-23 21:05:39] ================================================================================
[2025-07-23 21:05:39] 所有分析完成
