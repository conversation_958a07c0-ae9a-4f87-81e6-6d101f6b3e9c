[2025-07-23 22:13:41] ================================================================================
[2025-07-23 22:13:41] 加载量子态: L=5, J2=0.00, J1=0.01
[2025-07-23 22:13:41] 设置样本数为: 1048576
[2025-07-23 22:13:41] 开始生成共享样本集...
[2025-07-23 22:17:01] 样本生成完成,耗时: 199.766 秒
[2025-07-23 22:17:01] ================================================================================
[2025-07-23 22:17:01] 开始计算自旋结构因子...
[2025-07-23 22:17:01] 初始化操作符缓存...
[2025-07-23 22:17:01] 预构建所有自旋相关操作符...
[2025-07-23 22:17:01] 开始计算自旋相关函数...
[2025-07-23 22:17:11] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.520s
[2025-07-23 22:17:24] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.113s
[2025-07-23 22:17:30] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.312s
[2025-07-23 22:17:36] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.312s
[2025-07-23 22:17:42] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.281s
[2025-07-23 22:17:49] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.281s
[2025-07-23 22:17:55] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.310s
[2025-07-23 22:18:01] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.281s
[2025-07-23 22:18:08] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.281s
[2025-07-23 22:18:14] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.281s
[2025-07-23 22:18:20] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.281s
[2025-07-23 22:18:26] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.280s
[2025-07-23 22:18:33] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.310s
[2025-07-23 22:18:39] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.311s
[2025-07-23 22:18:45] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.311s
[2025-07-23 22:18:52] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.281s
[2025-07-23 22:18:58] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.280s
[2025-07-23 22:19:04] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.280s
[2025-07-23 22:19:11] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.311s
[2025-07-23 22:19:17] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.310s
[2025-07-23 22:19:23] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.279s
[2025-07-23 22:19:29] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.280s
[2025-07-23 22:19:36] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.283s
[2025-07-23 22:19:42] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.311s
[2025-07-23 22:19:48] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.311s
[2025-07-23 22:19:55] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.310s
[2025-07-23 22:20:01] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.280s
[2025-07-23 22:20:07] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.280s
[2025-07-23 22:20:13] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.280s
[2025-07-23 22:20:20] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.311s
[2025-07-23 22:20:26] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.310s
[2025-07-23 22:20:32] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.311s
[2025-07-23 22:20:39] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.280s
[2025-07-23 22:20:45] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.279s
[2025-07-23 22:20:51] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.280s
[2025-07-23 22:20:58] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.280s
[2025-07-23 22:21:04] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.281s
[2025-07-23 22:21:10] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.280s
[2025-07-23 22:21:16] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.310s
[2025-07-23 22:21:23] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.310s
[2025-07-23 22:21:29] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.310s
[2025-07-23 22:21:35] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.310s
[2025-07-23 22:21:42] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.310s
[2025-07-23 22:21:48] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.310s
[2025-07-23 22:21:54] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.280s
[2025-07-23 22:22:01] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.282s
[2025-07-23 22:22:07] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.310s
[2025-07-23 22:22:13] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.311s
[2025-07-23 22:22:19] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.310s
[2025-07-23 22:22:26] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.280s
[2025-07-23 22:22:32] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.280s
[2025-07-23 22:22:38] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.280s
[2025-07-23 22:22:45] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.311s
[2025-07-23 22:22:51] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.311s
[2025-07-23 22:22:57] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.280s
[2025-07-23 22:23:04] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.280s
[2025-07-23 22:23:10] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.280s
[2025-07-23 22:23:16] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.310s
[2025-07-23 22:23:22] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.311s
[2025-07-23 22:23:29] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.310s
[2025-07-23 22:23:35] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.279s
[2025-07-23 22:23:41] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.279s
[2025-07-23 22:23:48] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.280s
[2025-07-23 22:23:54] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.310s
[2025-07-23 22:24:00] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.310s
[2025-07-23 22:24:06] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.311s
[2025-07-23 22:24:13] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.280s
[2025-07-23 22:24:19] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.280s
[2025-07-23 22:24:25] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.311s
[2025-07-23 22:24:32] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.310s
[2025-07-23 22:24:38] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.311s
[2025-07-23 22:24:44] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.280s
[2025-07-23 22:24:51] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.280s
[2025-07-23 22:24:57] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.280s
[2025-07-23 22:25:03] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.311s
[2025-07-23 22:25:09] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.311s
[2025-07-23 22:25:16] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.280s
[2025-07-23 22:25:22] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.280s
[2025-07-23 22:25:28] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.280s
[2025-07-23 22:25:35] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.311s
[2025-07-23 22:25:41] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.310s
[2025-07-23 22:25:47] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.280s
[2025-07-23 22:25:53] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.280s
[2025-07-23 22:26:00] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.279s
[2025-07-23 22:26:06] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.311s
[2025-07-23 22:26:12] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.311s
[2025-07-23 22:26:19] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.311s
[2025-07-23 22:26:25] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.280s
[2025-07-23 22:26:31] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.280s
[2025-07-23 22:26:38] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.311s
[2025-07-23 22:26:44] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.311s
[2025-07-23 22:26:50] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.311s
[2025-07-23 22:26:56] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.280s
[2025-07-23 22:27:03] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.311s
[2025-07-23 22:27:09] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.279s
[2025-07-23 22:27:15] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.280s
[2025-07-23 22:27:22] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.311s
[2025-07-23 22:27:28] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.310s
[2025-07-23 22:27:34] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.311s
[2025-07-23 22:27:41] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.280s
[2025-07-23 22:27:41] 自旋相关函数计算完成,总耗时 639.69 秒
[2025-07-23 22:27:41] 计算傅里叶变换...
[2025-07-23 22:27:41] 自旋结构因子计算完成
[2025-07-23 22:27:42] 自旋相关函数平均误差: 0.000730
[2025-07-23 22:27:42] ================================================================================
[2025-07-23 22:27:42] 开始计算二聚体结构因子...
[2025-07-23 22:27:42] 识别x和y方向的二聚体...
[2025-07-23 22:27:42] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-23 22:27:42] 预计算二聚体操作符...
[2025-07-23 22:27:43] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 22:27:50] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.320s
[2025-07-23 22:28:09] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.965s
[2025-07-23 22:28:18] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.836s
[2025-07-23 22:28:34] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.303s
[2025-07-23 22:28:44] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.534s
[2025-07-23 22:28:55] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.595s
[2025-07-23 22:29:06] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.538s
[2025-07-23 22:29:16] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.541s
[2025-07-23 22:29:27] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.538s
[2025-07-23 22:29:37] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.541s
[2025-07-23 22:29:48] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.540s
[2025-07-23 22:29:58] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.541s
[2025-07-23 22:30:09] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.598s
[2025-07-23 22:30:19] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.598s
[2025-07-23 22:30:30] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.540s
[2025-07-23 22:30:41] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.538s
[2025-07-23 22:30:51] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.539s
[2025-07-23 22:31:02] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.597s
[2025-07-23 22:31:12] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.598s
[2025-07-23 22:31:23] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.600s
[2025-07-23 22:31:33] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.541s
[2025-07-23 22:31:44] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.540s
[2025-07-23 22:31:55] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.597s
[2025-07-23 22:32:05] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.598s
[2025-07-23 22:32:16] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.596s
[2025-07-23 22:32:26] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.600s
[2025-07-23 22:32:37] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.540s
[2025-07-23 22:32:47] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.538s
[2025-07-23 22:32:58] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.598s
[2025-07-23 22:33:09] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.598s
[2025-07-23 22:33:19] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.598s
[2025-07-23 22:33:30] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.538s
[2025-07-23 22:33:40] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.541s
[2025-07-23 22:33:51] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.540s
[2025-07-23 22:34:01] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.599s
[2025-07-23 22:34:12] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.600s
[2025-07-23 22:34:23] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.538s
[2025-07-23 22:34:33] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.542s
[2025-07-23 22:34:44] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.597s
[2025-07-23 22:34:54] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.599s
[2025-07-23 22:35:05] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.599s
[2025-07-23 22:35:16] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.598s
[2025-07-23 22:35:26] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.596s
[2025-07-23 22:35:37] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.538s
[2025-07-23 22:35:47] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.539s
[2025-07-23 22:35:58] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.542s
[2025-07-23 22:36:08] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.594s
[2025-07-23 22:36:19] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.599s
[2025-07-23 22:36:30] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.537s
[2025-07-23 22:36:40] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.539s
[2025-07-23 22:36:51] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.540s
[2025-07-23 22:37:01] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.597s
[2025-07-23 22:37:12] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.597s
[2025-07-23 22:37:22] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.597s
[2025-07-23 22:37:33] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.537s
[2025-07-23 22:37:43] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.538s
[2025-07-23 22:37:54] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.596s
[2025-07-23 22:38:05] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.594s
[2025-07-23 22:38:15] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.595s
[2025-07-23 22:38:26] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.539s
[2025-07-23 22:38:36] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.540s
[2025-07-23 22:38:47] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.595s
[2025-07-23 22:38:58] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.597s
[2025-07-23 22:39:08] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.598s
[2025-07-23 22:39:19] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.536s
[2025-07-23 22:39:29] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.540s
[2025-07-23 22:39:40] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.538s
[2025-07-23 22:39:50] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.598s
[2025-07-23 22:40:01] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.596s
[2025-07-23 22:40:11] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.539s
[2025-07-23 22:40:22] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.539s
[2025-07-23 22:40:33] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.536s
[2025-07-23 22:40:43] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.597s
[2025-07-23 22:40:54] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.596s
[2025-07-23 22:41:04] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.598s
[2025-07-23 22:41:15] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.540s
[2025-07-23 22:41:25] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.537s
[2025-07-23 22:41:36] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.539s
[2025-07-23 22:41:47] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.538s
[2025-07-23 22:41:57] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.597s
[2025-07-23 22:42:08] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.597s
[2025-07-23 22:42:18] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.599s
[2025-07-23 22:42:29] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.537s
[2025-07-23 22:42:39] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.539s
[2025-07-23 22:42:50] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.598s
[2025-07-23 22:43:01] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.598s
[2025-07-23 22:43:11] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.599s
[2025-07-23 22:43:22] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.539s
[2025-07-23 22:43:32] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.541s
[2025-07-23 22:43:43] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.538s
[2025-07-23 22:43:53] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.599s
[2025-07-23 22:44:04] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.598s
[2025-07-23 22:44:15] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.596s
[2025-07-23 22:44:25] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.599s
[2025-07-23 22:44:36] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.541s
[2025-07-23 22:44:46] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.539s
[2025-07-23 22:44:57] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.540s
[2025-07-23 22:45:07] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.597s
[2025-07-23 22:45:18] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.598s
[2025-07-23 22:45:29] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.597s
[2025-07-23 22:45:29] x方向二聚体相关函数计算完成,耗时: 1065.15 秒
[2025-07-23 22:45:29] --------------------------------------------------------------------------------
[2025-07-23 22:45:29] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 22:45:35] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.289s
[2025-07-23 22:45:44] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.819s
[2025-07-23 22:45:54] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.542s
[2025-07-23 22:46:05] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.542s
[2025-07-23 22:46:15] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.542s
[2025-07-23 22:46:24] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.816s
[2025-07-23 22:46:35] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.602s
[2025-07-23 22:46:45] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.605s
[2025-07-23 22:46:56] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.551s
[2025-07-23 22:47:07] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.603s
[2025-07-23 22:47:17] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.547s
[2025-07-23 22:47:28] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.552s
[2025-07-23 22:47:38] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.545s
[2025-07-23 22:47:49] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.602s
[2025-07-23 22:47:59] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.601s
[2025-07-23 22:48:10] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.546s
[2025-07-23 22:48:20] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.551s
[2025-07-23 22:48:31] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.544s
[2025-07-23 22:48:42] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.603s
[2025-07-23 22:48:52] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.601s
[2025-07-23 22:49:03] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.602s
[2025-07-23 22:49:13] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.550s
[2025-07-23 22:49:24] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.550s
[2025-07-23 22:49:35] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.605s
[2025-07-23 22:49:45] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.602s
[2025-07-23 22:49:56] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.602s
[2025-07-23 22:50:06] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.601s
[2025-07-23 22:50:17] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.601s
[2025-07-23 22:50:27] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.544s
[2025-07-23 22:50:38] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.543s
[2025-07-23 22:50:49] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.544s
[2025-07-23 22:50:59] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.601s
[2025-07-23 22:51:10] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.602s
[2025-07-23 22:51:20] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.602s
[2025-07-23 22:51:31] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.546s
[2025-07-23 22:51:41] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.546s
[2025-07-23 22:51:52] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.601s
[2025-07-23 22:52:03] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.601s
[2025-07-23 22:52:13] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.601s
[2025-07-23 22:52:24] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.545s
[2025-07-23 22:52:34] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.548s
[2025-07-23 22:52:45] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.543s
[2025-07-23 22:52:56] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.604s
[2025-07-23 22:53:06] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.600s
[2025-07-23 22:53:17] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.550s
[2025-07-23 22:53:27] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.546s
[2025-07-23 22:53:38] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.545s
[2025-07-23 22:53:48] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.545s
[2025-07-23 22:53:59] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.545s
[2025-07-23 22:54:09] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.548s
[2025-07-23 22:54:20] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.599s
[2025-07-23 22:54:31] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.601s
[2025-07-23 22:54:41] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.544s
[2025-07-23 22:54:52] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.550s
[2025-07-23 22:55:02] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.545s
[2025-07-23 22:55:13] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.604s
[2025-07-23 22:55:23] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.600s
[2025-07-23 22:55:34] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.600s
[2025-07-23 22:55:45] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.551s
[2025-07-23 22:55:55] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.547s
[2025-07-23 22:56:06] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.603s
[2025-07-23 22:56:16] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.601s
[2025-07-23 22:56:27] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.602s
[2025-07-23 22:56:38] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.543s
[2025-07-23 22:56:48] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.543s
[2025-07-23 22:56:59] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.546s
[2025-07-23 22:57:09] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.600s
[2025-07-23 22:57:20] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.612s
[2025-07-23 22:57:30] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.542s
[2025-07-23 22:57:41] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.549s
[2025-07-23 22:57:51] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.544s
[2025-07-23 22:58:02] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.604s
[2025-07-23 22:58:13] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.600s
[2025-07-23 22:58:23] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.602s
[2025-07-23 22:58:34] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.551s
[2025-07-23 22:58:44] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.543s
[2025-07-23 22:58:55] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.602s
[2025-07-23 22:59:06] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.602s
[2025-07-23 22:59:16] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.601s
[2025-07-23 22:59:27] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.544s
[2025-07-23 22:59:37] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.543s
[2025-07-23 22:59:48] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.547s
[2025-07-23 22:59:58] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.600s
[2025-07-23 23:00:09] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.601s
[2025-07-23 23:00:20] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.547s
[2025-07-23 23:00:30] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.547s
[2025-07-23 23:00:41] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.548s
[2025-07-23 23:00:51] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.603s
[2025-07-23 23:01:02] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.603s
[2025-07-23 23:01:12] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.601s
[2025-07-23 23:01:23] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.601s
[2025-07-23 23:01:34] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.601s
[2025-07-23 23:01:44] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.547s
[2025-07-23 23:01:55] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.544s
[2025-07-23 23:02:05] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.550s
[2025-07-23 23:02:16] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.601s
[2025-07-23 23:02:27] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.605s
[2025-07-23 23:02:37] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.601s
[2025-07-23 23:02:48] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.551s
[2025-07-23 23:02:58] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.548s
[2025-07-23 23:02:58] y方向二聚体相关函数计算完成,耗时: 1049.64 秒
[2025-07-23 23:02:58] 计算傅里叶变换...
[2025-07-23 23:02:59] 二聚体结构因子计算完成
[2025-07-23 23:03:00] 二聚体相关函数平均误差: 0.000563
[2025-07-23 23:03:00] 恢复原始样本数: 4096
[2025-07-23 23:03:00] ================================================================================
[2025-07-23 23:03:00] 所有分析完成
