[2025-07-24 03:11:02] ================================================================================
[2025-07-24 03:11:02] 加载量子态: L=5, J2=0.00, J1=0.07
[2025-07-24 03:11:02] 设置样本数为: 1048576
[2025-07-24 03:11:02] 开始生成共享样本集...
[2025-07-24 03:14:22] 样本生成完成,耗时: 199.816 秒
[2025-07-24 03:14:22] ================================================================================
[2025-07-24 03:14:22] 开始计算自旋结构因子...
[2025-07-24 03:14:22] 初始化操作符缓存...
[2025-07-24 03:14:22] 预构建所有自旋相关操作符...
[2025-07-24 03:14:22] 开始计算自旋相关函数...
[2025-07-24 03:14:32] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.439s
[2025-07-24 03:14:45] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.155s
[2025-07-24 03:14:51] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.312s
[2025-07-24 03:14:57] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.317s
[2025-07-24 03:15:04] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.295s
[2025-07-24 03:15:10] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.316s
[2025-07-24 03:15:16] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.313s
[2025-07-24 03:15:22] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.294s
[2025-07-24 03:15:29] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.317s
[2025-07-24 03:15:35] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.295s
[2025-07-24 03:15:41] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.310s
[2025-07-24 03:15:48] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.295s
[2025-07-24 03:15:54] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.315s
[2025-07-24 03:16:00] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.311s
[2025-07-24 03:16:07] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.317s
[2025-07-24 03:16:13] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.295s
[2025-07-24 03:16:19] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.320s
[2025-07-24 03:16:26] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.294s
[2025-07-24 03:16:32] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.311s
[2025-07-24 03:16:38] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.319s
[2025-07-24 03:16:44] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.295s
[2025-07-24 03:16:51] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.293s
[2025-07-24 03:16:57] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.320s
[2025-07-24 03:17:03] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.312s
[2025-07-24 03:17:10] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.313s
[2025-07-24 03:17:16] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.316s
[2025-07-24 03:17:22] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.294s
[2025-07-24 03:17:29] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.320s
[2025-07-24 03:17:35] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.295s
[2025-07-24 03:17:41] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.312s
[2025-07-24 03:17:48] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.313s
[2025-07-24 03:17:54] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.315s
[2025-07-24 03:18:00] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.295s
[2025-07-24 03:18:06] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.296s
[2025-07-24 03:18:13] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.310s
[2025-07-24 03:18:19] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.295s
[2025-07-24 03:18:25] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.321s
[2025-07-24 03:18:32] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.317s
[2025-07-24 03:18:38] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.312s
[2025-07-24 03:18:44] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.311s
[2025-07-24 03:18:51] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.311s
[2025-07-24 03:18:57] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.311s
[2025-07-24 03:19:03] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.312s
[2025-07-24 03:19:10] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.310s
[2025-07-24 03:19:16] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.312s
[2025-07-24 03:19:22] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.319s
[2025-07-24 03:19:29] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.312s
[2025-07-24 03:19:35] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.312s
[2025-07-24 03:19:41] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.311s
[2025-07-24 03:19:47] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.294s
[2025-07-24 03:19:54] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.316s
[2025-07-24 03:20:00] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.294s
[2025-07-24 03:20:06] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.311s
[2025-07-24 03:20:13] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.311s
[2025-07-24 03:20:19] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.316s
[2025-07-24 03:20:25] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.294s
[2025-07-24 03:20:32] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.398s
[2025-07-24 03:20:38] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.319s
[2025-07-24 03:20:44] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.311s
[2025-07-24 03:20:51] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.318s
[2025-07-24 03:20:57] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.293s
[2025-07-24 03:21:03] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.295s
[2025-07-24 03:21:10] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.318s
[2025-07-24 03:21:16] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.311s
[2025-07-24 03:21:22] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.312s
[2025-07-24 03:21:29] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.321s
[2025-07-24 03:21:35] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.299s
[2025-07-24 03:21:41] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.322s
[2025-07-24 03:21:47] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.312s
[2025-07-24 03:21:54] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.311s
[2025-07-24 03:22:00] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.320s
[2025-07-24 03:22:06] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.295s
[2025-07-24 03:22:13] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.316s
[2025-07-24 03:22:19] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.313s
[2025-07-24 03:22:25] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.312s
[2025-07-24 03:22:32] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.378s
[2025-07-24 03:22:38] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.295s
[2025-07-24 03:22:44] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.300s
[2025-07-24 03:22:51] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.319s
[2025-07-24 03:22:57] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.312s
[2025-07-24 03:23:03] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.316s
[2025-07-24 03:23:10] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.310s
[2025-07-24 03:23:16] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.294s
[2025-07-24 03:23:22] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.313s
[2025-07-24 03:23:28] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.314s
[2025-07-24 03:23:35] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.315s
[2025-07-24 03:23:41] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.312s
[2025-07-24 03:23:47] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.318s
[2025-07-24 03:23:54] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.301s
[2025-07-24 03:24:00] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.312s
[2025-07-24 03:24:06] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.316s
[2025-07-24 03:24:13] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.312s
[2025-07-24 03:24:19] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.320s
[2025-07-24 03:24:25] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.317s
[2025-07-24 03:24:32] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.384s
[2025-07-24 03:24:38] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.326s
[2025-07-24 03:24:44] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.312s
[2025-07-24 03:24:51] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.312s
[2025-07-24 03:24:57] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.316s
[2025-07-24 03:25:03] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.298s
[2025-07-24 03:25:03] 自旋相关函数计算完成,总耗时 641.27 秒
[2025-07-24 03:25:03] 计算傅里叶变换...
[2025-07-24 03:25:04] 自旋结构因子计算完成
[2025-07-24 03:25:05] 自旋相关函数平均误差: 0.000683
[2025-07-24 03:25:05] ================================================================================
[2025-07-24 03:25:05] 开始计算二聚体结构因子...
[2025-07-24 03:25:05] 识别x和y方向的二聚体...
[2025-07-24 03:25:05] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-24 03:25:05] 预计算二聚体操作符...
[2025-07-24 03:25:06] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-24 03:25:12] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.350s
[2025-07-24 03:25:31] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.984s
[2025-07-24 03:25:40] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.836s
[2025-07-24 03:25:57] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.371s
[2025-07-24 03:26:07] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.586s
[2025-07-24 03:26:18] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.618s
[2025-07-24 03:26:28] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.586s
[2025-07-24 03:26:39] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.624s
[2025-07-24 03:26:50] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.625s
[2025-07-24 03:27:00] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.585s
[2025-07-24 03:27:11] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.614s
[2025-07-24 03:27:22] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.630s
[2025-07-24 03:27:32] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.666s
[2025-07-24 03:27:43] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.621s
[2025-07-24 03:27:53] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.584s
[2025-07-24 03:28:04] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.615s
[2025-07-24 03:28:15] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.584s
[2025-07-24 03:28:25] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.600s
[2025-07-24 03:28:36] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.617s
[2025-07-24 03:28:46] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.601s
[2025-07-24 03:28:57] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.622s
[2025-07-24 03:29:08] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.584s
[2025-07-24 03:29:18] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.621s
[2025-07-24 03:29:29] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.599s
[2025-07-24 03:29:40] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.632s
[2025-07-24 03:29:50] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.618s
[2025-07-24 03:30:01] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.582s
[2025-07-24 03:30:11] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.621s
[2025-07-24 03:30:22] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.601s
[2025-07-24 03:30:33] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.632s
[2025-07-24 03:30:43] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.621s
[2025-07-24 03:30:54] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.581s
[2025-07-24 03:31:04] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.624s
[2025-07-24 03:31:15] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.584s
[2025-07-24 03:31:26] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.616s
[2025-07-24 03:31:36] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.603s
[2025-07-24 03:31:47] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.610s
[2025-07-24 03:31:57] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.597s
[2025-07-24 03:32:08] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.621s
[2025-07-24 03:32:19] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.601s
[2025-07-24 03:32:29] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.612s
[2025-07-24 03:32:40] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.601s
[2025-07-24 03:32:50] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.599s
[2025-07-24 03:33:01] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.620s
[2025-07-24 03:33:12] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.583s
[2025-07-24 03:33:22] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.613s
[2025-07-24 03:33:33] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.617s
[2025-07-24 03:33:44] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.603s
[2025-07-24 03:33:54] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.617s
[2025-07-24 03:34:05] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.584s
[2025-07-24 03:34:15] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.619s
[2025-07-24 03:34:26] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.601s
[2025-07-24 03:34:37] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.624s
[2025-07-24 03:34:47] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.619s
[2025-07-24 03:34:58] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.632s
[2025-07-24 03:35:08] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.601s
[2025-07-24 03:35:19] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.621s
[2025-07-24 03:35:30] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.598s
[2025-07-24 03:35:40] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.614s
[2025-07-24 03:35:51] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.583s
[2025-07-24 03:36:01] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.621s
[2025-07-24 03:36:12] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.599s
[2025-07-24 03:36:23] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.597s
[2025-07-24 03:36:33] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.612s
[2025-07-24 03:36:44] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.583s
[2025-07-24 03:36:54] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.614s
[2025-07-24 03:37:05] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.584s
[2025-07-24 03:37:16] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.621s
[2025-07-24 03:37:26] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.615s
[2025-07-24 03:37:37] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.584s
[2025-07-24 03:37:47] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.616s
[2025-07-24 03:37:58] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.584s
[2025-07-24 03:38:09] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.613s
[2025-07-24 03:38:19] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.599s
[2025-07-24 03:38:30] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.617s
[2025-07-24 03:38:41] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.584s
[2025-07-24 03:38:51] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.618s
[2025-07-24 03:39:02] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.585s
[2025-07-24 03:39:12] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.584s
[2025-07-24 03:39:23] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.620s
[2025-07-24 03:39:34] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.599s
[2025-07-24 03:39:44] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.618s
[2025-07-24 03:39:55] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.584s
[2025-07-24 03:40:05] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.611s
[2025-07-24 03:40:16] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.600s
[2025-07-24 03:40:27] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.599s
[2025-07-24 03:40:37] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.625s
[2025-07-24 03:40:48] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.584s
[2025-07-24 03:40:58] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.620s
[2025-07-24 03:41:09] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.584s
[2025-07-24 03:41:20] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.626s
[2025-07-24 03:41:30] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.601s
[2025-07-24 03:41:41] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.620s
[2025-07-24 03:41:51] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.603s
[2025-07-24 03:42:02] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.586s
[2025-07-24 03:42:13] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.623s
[2025-07-24 03:42:23] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.584s
[2025-07-24 03:42:34] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.615s
[2025-07-24 03:42:44] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.599s
[2025-07-24 03:42:55] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.624s
[2025-07-24 03:42:55] x方向二聚体相关函数计算完成,耗时: 1068.89 秒
[2025-07-24 03:42:55] --------------------------------------------------------------------------------
[2025-07-24 03:42:55] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-24 03:43:01] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.301s
[2025-07-24 03:43:10] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.829s
[2025-07-24 03:43:21] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.643s
[2025-07-24 03:43:31] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.589s
[2025-07-24 03:43:42] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.589s
[2025-07-24 03:43:51] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.817s
[2025-07-24 03:44:01] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.603s
[2025-07-24 03:44:12] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.651s
[2025-07-24 03:44:23] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.592s
[2025-07-24 03:44:33] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.647s
[2025-07-24 03:44:44] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.591s
[2025-07-24 03:44:55] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.631s
[2025-07-24 03:45:05] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.589s
[2025-07-24 03:45:16] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.623s
[2025-07-24 03:45:26] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.601s
[2025-07-24 03:45:37] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.625s
[2025-07-24 03:45:48] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.591s
[2025-07-24 03:45:58] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.583s
[2025-07-24 03:46:09] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.653s
[2025-07-24 03:46:19] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.602s
[2025-07-24 03:46:30] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.648s
[2025-07-24 03:46:41] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.590s
[2025-07-24 03:46:51] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.650s
[2025-07-24 03:47:02] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.606s
[2025-07-24 03:47:13] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.636s
[2025-07-24 03:47:23] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.604s
[2025-07-24 03:47:34] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.654s
[2025-07-24 03:47:44] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.602s
[2025-07-24 03:47:55] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.655s
[2025-07-24 03:48:06] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.626s
[2025-07-24 03:48:16] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.593s
[2025-07-24 03:48:27] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.644s
[2025-07-24 03:48:38] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.602s
[2025-07-24 03:48:48] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.604s
[2025-07-24 03:48:59] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.626s
[2025-07-24 03:49:09] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.591s
[2025-07-24 03:49:20] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.635s
[2025-07-24 03:49:31] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.602s
[2025-07-24 03:49:41] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.652s
[2025-07-24 03:49:52] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.590s
[2025-07-24 03:50:03] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.652s
[2025-07-24 03:50:13] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.591s
[2025-07-24 03:50:24] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.649s
[2025-07-24 03:50:34] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.602s
[2025-07-24 03:50:45] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.627s
[2025-07-24 03:50:56] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.649s
[2025-07-24 03:51:06] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.590s
[2025-07-24 03:51:17] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.650s
[2025-07-24 03:51:27] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.590s
[2025-07-24 03:51:38] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.622s
[2025-07-24 03:51:49] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.635s
[2025-07-24 03:51:59] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.603s
[2025-07-24 03:52:10] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.627s
[2025-07-24 03:52:21] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.591s
[2025-07-24 03:52:31] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.650s
[2025-07-24 03:52:42] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.602s
[2025-07-24 03:52:52] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.645s
[2025-07-24 03:53:03] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.602s
[2025-07-24 03:53:14] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.655s
[2025-07-24 03:53:24] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.591s
[2025-07-24 03:53:35] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.634s
[2025-07-24 03:53:46] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.649s
[2025-07-24 03:53:56] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.602s
[2025-07-24 03:54:07] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.650s
[2025-07-24 03:54:17] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.590s
[2025-07-24 03:54:28] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.623s
[2025-07-24 03:54:39] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.602s
[2025-07-24 03:54:49] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.602s
[2025-07-24 03:55:00] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.635s
[2025-07-24 03:55:10] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.590s
[2025-07-24 03:55:21] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.629s
[2025-07-24 03:55:32] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.606s
[2025-07-24 03:55:42] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.635s
[2025-07-24 03:55:53] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.602s
[2025-07-24 03:56:04] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.626s
[2025-07-24 03:56:14] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.592s
[2025-07-24 03:56:25] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.627s
[2025-07-24 03:56:35] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.626s
[2025-07-24 03:56:46] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.602s
[2025-07-24 03:56:57] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.624s
[2025-07-24 03:57:07] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.590s
[2025-07-24 03:57:18] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.649s
[2025-07-24 03:57:29] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.600s
[2025-07-24 03:57:39] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.647s
[2025-07-24 03:57:50] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.590s
[2025-07-24 03:58:00] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.623s
[2025-07-24 03:58:11] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.589s
[2025-07-24 03:58:22] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.636s
[2025-07-24 03:58:32] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.639s
[2025-07-24 03:58:43] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.654s
[2025-07-24 03:58:53] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.602s
[2025-07-24 03:59:04] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.650s
[2025-07-24 03:59:15] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.589s
[2025-07-24 03:59:25] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.593s
[2025-07-24 03:59:36] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.645s
[2025-07-24 03:59:47] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.603s
[2025-07-24 03:59:57] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.650s
[2025-07-24 04:00:08] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.602s
[2025-07-24 04:00:18] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.636s
[2025-07-24 04:00:29] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.591s
[2025-07-24 04:00:29] y方向二聚体相关函数计算完成,耗时: 1054.02 秒
[2025-07-24 04:00:29] 计算傅里叶变换...
[2025-07-24 04:00:30] 二聚体结构因子计算完成
[2025-07-24 04:00:31] 二聚体相关函数平均误差: 0.000519
[2025-07-24 04:00:31] 恢复原始样本数: 4096
[2025-07-24 04:00:31] ================================================================================
[2025-07-24 04:00:31] 所有分析完成
