[2025-07-23 23:03:13] ================================================================================
[2025-07-23 23:03:13] 加载量子态: L=5, J2=0.00, J1=0.02
[2025-07-23 23:03:13] 设置样本数为: 1048576
[2025-07-23 23:03:13] 开始生成共享样本集...
[2025-07-23 23:06:31] 样本生成完成,耗时: 198.603 秒
[2025-07-23 23:06:31] ================================================================================
[2025-07-23 23:06:31] 开始计算自旋结构因子...
[2025-07-23 23:06:31] 初始化操作符缓存...
[2025-07-23 23:06:31] 预构建所有自旋相关操作符...
[2025-07-23 23:06:31] 开始计算自旋相关函数...
[2025-07-23 23:06:42] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.411s
[2025-07-23 23:06:54] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.119s
[2025-07-23 23:07:00] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.273s
[2025-07-23 23:07:06] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.272s
[2025-07-23 23:07:13] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.244s
[2025-07-23 23:07:19] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.269s
[2025-07-23 23:07:25] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.270s
[2025-07-23 23:07:31] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.244s
[2025-07-23 23:07:38] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.272s
[2025-07-23 23:07:44] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.245s
[2025-07-23 23:07:50] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.261s
[2025-07-23 23:07:57] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.245s
[2025-07-23 23:08:03] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.275s
[2025-07-23 23:08:09] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.274s
[2025-07-23 23:08:15] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.276s
[2025-07-23 23:08:22] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.244s
[2025-07-23 23:08:28] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.275s
[2025-07-23 23:08:34] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.250s
[2025-07-23 23:08:40] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.277s
[2025-07-23 23:08:47] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.276s
[2025-07-23 23:08:53] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.251s
[2025-07-23 23:08:59] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.246s
[2025-07-23 23:09:05] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.274s
[2025-07-23 23:09:12] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.272s
[2025-07-23 23:09:18] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.276s
[2025-07-23 23:09:24] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.274s
[2025-07-23 23:09:31] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.246s
[2025-07-23 23:09:37] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.275s
[2025-07-23 23:09:43] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.245s
[2025-07-23 23:09:49] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.270s
[2025-07-23 23:09:56] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.277s
[2025-07-23 23:10:02] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.273s
[2025-07-23 23:10:08] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.243s
[2025-07-23 23:10:14] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.246s
[2025-07-23 23:10:21] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.261s
[2025-07-23 23:10:27] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.244s
[2025-07-23 23:10:33] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.275s
[2025-07-23 23:10:39] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.271s
[2025-07-23 23:10:46] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.274s
[2025-07-23 23:10:52] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.276s
[2025-07-23 23:10:58] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.276s
[2025-07-23 23:11:05] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.276s
[2025-07-23 23:11:11] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.270s
[2025-07-23 23:11:17] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.277s
[2025-07-23 23:11:23] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.264s
[2025-07-23 23:11:30] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.272s
[2025-07-23 23:11:36] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.271s
[2025-07-23 23:11:42] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.273s
[2025-07-23 23:11:48] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.277s
[2025-07-23 23:11:55] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.245s
[2025-07-23 23:12:01] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.268s
[2025-07-23 23:12:07] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.248s
[2025-07-23 23:12:13] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.274s
[2025-07-23 23:12:20] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.267s
[2025-07-23 23:12:26] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.270s
[2025-07-23 23:12:32] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.246s
[2025-07-23 23:12:39] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.276s
[2025-07-23 23:12:45] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.274s
[2025-07-23 23:12:51] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.273s
[2025-07-23 23:12:57] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.329s
[2025-07-23 23:13:04] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.245s
[2025-07-23 23:13:10] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.243s
[2025-07-23 23:13:16] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.271s
[2025-07-23 23:13:22] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.274s
[2025-07-23 23:13:29] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.274s
[2025-07-23 23:13:35] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.274s
[2025-07-23 23:13:41] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.245s
[2025-07-23 23:13:48] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.269s
[2025-07-23 23:13:54] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.273s
[2025-07-23 23:14:00] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.276s
[2025-07-23 23:14:06] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.273s
[2025-07-23 23:14:13] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.243s
[2025-07-23 23:14:19] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.267s
[2025-07-23 23:14:25] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.326s
[2025-07-23 23:14:31] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.270s
[2025-07-23 23:14:38] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.280s
[2025-07-23 23:14:44] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.246s
[2025-07-23 23:14:50] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.244s
[2025-07-23 23:14:57] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.272s
[2025-07-23 23:15:03] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.272s
[2025-07-23 23:15:09] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.275s
[2025-07-23 23:15:15] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.263s
[2025-07-23 23:15:22] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.246s
[2025-07-23 23:15:28] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.268s
[2025-07-23 23:15:34] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.270s
[2025-07-23 23:15:40] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.281s
[2025-07-23 23:15:47] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.273s
[2025-07-23 23:15:53] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.273s
[2025-07-23 23:15:59] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.247s
[2025-07-23 23:16:05] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.274s
[2025-07-23 23:16:12] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.275s
[2025-07-23 23:16:18] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.275s
[2025-07-23 23:16:24] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.276s
[2025-07-23 23:16:31] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.268s
[2025-07-23 23:16:37] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.248s
[2025-07-23 23:16:43] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.268s
[2025-07-23 23:16:49] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.273s
[2025-07-23 23:16:56] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.275s
[2025-07-23 23:17:02] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.267s
[2025-07-23 23:17:08] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.243s
[2025-07-23 23:17:08] 自旋相关函数计算完成,总耗时 636.76 秒
[2025-07-23 23:17:08] 计算傅里叶变换...
[2025-07-23 23:17:09] 自旋结构因子计算完成
[2025-07-23 23:17:10] 自旋相关函数平均误差: 0.000747
[2025-07-23 23:17:10] ================================================================================
[2025-07-23 23:17:10] 开始计算二聚体结构因子...
[2025-07-23 23:17:10] 识别x和y方向的二聚体...
[2025-07-23 23:17:10] 找到 100 个x方向二聚体和 100 个y方向二聚体
[2025-07-23 23:17:10] 预计算二聚体操作符...
[2025-07-23 23:17:11] 使用x-二聚体 (0, 1) (全局索引 0) 作为参考点, 计算x方向连通二聚体相关函数...
[2025-07-23 23:17:17] x方向算符进度: 1/100, 当前算符: D_(0, 1) * D_(0, 1), 耗时: 6.309s
[2025-07-23 23:17:36] x方向算符进度: 2/100, 当前算符: D_(0, 1) * D_(0, 81), 耗时: 18.889s
[2025-07-23 23:17:45] x方向算符进度: 3/100, 当前算符: D_(0, 1) * D_(1, 20), 耗时: 8.770s
[2025-07-23 23:18:01] x方向算符进度: 4/100, 当前算符: D_(0, 1) * D_(2, 3), 耗时: 16.269s
[2025-07-23 23:18:12] x方向算符进度: 5/100, 当前算符: D_(0, 1) * D_(2, 23), 耗时: 10.461s
[2025-07-23 23:18:22] x方向算符进度: 6/100, 当前算符: D_(0, 1) * D_(3, 82), 耗时: 10.511s
[2025-07-23 23:18:33] x方向算符进度: 7/100, 当前算符: D_(0, 1) * D_(4, 5), 耗时: 10.461s
[2025-07-23 23:18:43] x方向算符进度: 8/100, 当前算符: D_(0, 1) * D_(4, 85), 耗时: 10.524s
[2025-07-23 23:18:54] x方向算符进度: 9/100, 当前算符: D_(0, 1) * D_(5, 24), 耗时: 10.539s
[2025-07-23 23:19:04] x方向算符进度: 10/100, 当前算符: D_(0, 1) * D_(6, 7), 耗时: 10.461s
[2025-07-23 23:19:15] x方向算符进度: 11/100, 当前算符: D_(0, 1) * D_(6, 27), 耗时: 10.501s
[2025-07-23 23:19:25] x方向算符进度: 12/100, 当前算符: D_(0, 1) * D_(7, 86), 耗时: 10.527s
[2025-07-23 23:19:36] x方向算符进度: 13/100, 当前算符: D_(0, 1) * D_(8, 9), 耗时: 10.520s
[2025-07-23 23:19:46] x方向算符进度: 14/100, 当前算符: D_(0, 1) * D_(8, 89), 耗时: 10.522s
[2025-07-23 23:19:57] x方向算符进度: 15/100, 当前算符: D_(0, 1) * D_(9, 28), 耗时: 10.460s
[2025-07-23 23:20:07] x方向算符进度: 16/100, 当前算符: D_(0, 1) * D_(10, 11), 耗时: 10.508s
[2025-07-23 23:20:18] x方向算符进度: 17/100, 当前算符: D_(0, 1) * D_(10, 31), 耗时: 10.459s
[2025-07-23 23:20:28] x方向算符进度: 18/100, 当前算符: D_(0, 1) * D_(11, 90), 耗时: 10.518s
[2025-07-23 23:20:39] x方向算符进度: 19/100, 当前算符: D_(0, 1) * D_(12, 13), 耗时: 10.517s
[2025-07-23 23:20:49] x方向算符进度: 20/100, 当前算符: D_(0, 1) * D_(12, 93), 耗时: 10.496s
[2025-07-23 23:21:00] x方向算符进度: 21/100, 当前算符: D_(0, 1) * D_(13, 32), 耗时: 10.516s
[2025-07-23 23:21:10] x方向算符进度: 22/100, 当前算符: D_(0, 1) * D_(14, 15), 耗时: 10.459s
[2025-07-23 23:21:21] x方向算符进度: 23/100, 当前算符: D_(0, 1) * D_(14, 35), 耗时: 10.523s
[2025-07-23 23:21:31] x方向算符进度: 24/100, 当前算符: D_(0, 1) * D_(15, 94), 耗时: 10.522s
[2025-07-23 23:21:42] x方向算符进度: 25/100, 当前算符: D_(0, 1) * D_(16, 17), 耗时: 10.530s
[2025-07-23 23:21:52] x方向算符进度: 26/100, 当前算符: D_(0, 1) * D_(16, 97), 耗时: 10.518s
[2025-07-23 23:22:03] x方向算符进度: 27/100, 当前算符: D_(0, 1) * D_(17, 36), 耗时: 10.460s
[2025-07-23 23:22:13] x方向算符进度: 28/100, 当前算符: D_(0, 1) * D_(18, 19), 耗时: 10.516s
[2025-07-23 23:22:24] x方向算符进度: 29/100, 当前算符: D_(0, 1) * D_(18, 39), 耗时: 10.511s
[2025-07-23 23:22:34] x方向算符进度: 30/100, 当前算符: D_(0, 1) * D_(19, 98), 耗时: 10.529s
[2025-07-23 23:22:45] x方向算符进度: 31/100, 当前算符: D_(0, 1) * D_(20, 21), 耗时: 10.522s
[2025-07-23 23:22:55] x方向算符进度: 32/100, 当前算符: D_(0, 1) * D_(21, 40), 耗时: 10.459s
[2025-07-23 23:23:06] x方向算符进度: 33/100, 当前算符: D_(0, 1) * D_(22, 23), 耗时: 10.524s
[2025-07-23 23:23:16] x方向算符进度: 34/100, 当前算符: D_(0, 1) * D_(22, 43), 耗时: 10.459s
[2025-07-23 23:23:27] x方向算符进度: 35/100, 当前算符: D_(0, 1) * D_(24, 25), 耗时: 10.517s
[2025-07-23 23:23:37] x方向算符进度: 36/100, 当前算符: D_(0, 1) * D_(25, 44), 耗时: 10.512s
[2025-07-23 23:23:48] x方向算符进度: 37/100, 当前算符: D_(0, 1) * D_(26, 27), 耗时: 10.500s
[2025-07-23 23:23:58] x方向算符进度: 38/100, 当前算符: D_(0, 1) * D_(26, 47), 耗时: 10.459s
[2025-07-23 23:24:09] x方向算符进度: 39/100, 当前算符: D_(0, 1) * D_(28, 29), 耗时: 10.520s
[2025-07-23 23:24:19] x方向算符进度: 40/100, 当前算符: D_(0, 1) * D_(29, 48), 耗时: 10.516s
[2025-07-23 23:24:30] x方向算符进度: 41/100, 当前算符: D_(0, 1) * D_(30, 31), 耗时: 10.512s
[2025-07-23 23:24:40] x方向算符进度: 42/100, 当前算符: D_(0, 1) * D_(30, 51), 耗时: 10.517s
[2025-07-23 23:24:51] x方向算符进度: 43/100, 当前算符: D_(0, 1) * D_(32, 33), 耗时: 10.523s
[2025-07-23 23:25:01] x方向算符进度: 44/100, 当前算符: D_(0, 1) * D_(33, 52), 耗时: 10.519s
[2025-07-23 23:25:12] x方向算符进度: 45/100, 当前算符: D_(0, 1) * D_(34, 35), 耗时: 10.459s
[2025-07-23 23:25:22] x方向算符进度: 46/100, 当前算符: D_(0, 1) * D_(34, 55), 耗时: 10.503s
[2025-07-23 23:25:33] x方向算符进度: 47/100, 当前算符: D_(0, 1) * D_(36, 37), 耗时: 10.525s
[2025-07-23 23:25:43] x方向算符进度: 48/100, 当前算符: D_(0, 1) * D_(37, 56), 耗时: 10.512s
[2025-07-23 23:25:54] x方向算符进度: 49/100, 当前算符: D_(0, 1) * D_(38, 39), 耗时: 10.507s
[2025-07-23 23:26:04] x方向算符进度: 50/100, 当前算符: D_(0, 1) * D_(38, 59), 耗时: 10.459s
[2025-07-23 23:26:15] x方向算符进度: 51/100, 当前算符: D_(0, 1) * D_(40, 41), 耗时: 10.513s
[2025-07-23 23:26:25] x方向算符进度: 52/100, 当前算符: D_(0, 1) * D_(41, 60), 耗时: 10.518s
[2025-07-23 23:26:36] x方向算符进度: 53/100, 当前算符: D_(0, 1) * D_(42, 43), 耗时: 10.523s
[2025-07-23 23:26:46] x方向算符进度: 54/100, 当前算符: D_(0, 1) * D_(42, 63), 耗时: 10.507s
[2025-07-23 23:26:57] x方向算符进度: 55/100, 当前算符: D_(0, 1) * D_(44, 45), 耗时: 10.534s
[2025-07-23 23:27:07] x方向算符进度: 56/100, 当前算符: D_(0, 1) * D_(45, 64), 耗时: 10.463s
[2025-07-23 23:27:18] x方向算符进度: 57/100, 当前算符: D_(0, 1) * D_(46, 47), 耗时: 10.517s
[2025-07-23 23:27:28] x方向算符进度: 58/100, 当前算符: D_(0, 1) * D_(46, 67), 耗时: 10.525s
[2025-07-23 23:27:39] x方向算符进度: 59/100, 当前算符: D_(0, 1) * D_(48, 49), 耗时: 10.522s
[2025-07-23 23:27:49] x方向算符进度: 60/100, 当前算符: D_(0, 1) * D_(49, 68), 耗时: 10.463s
[2025-07-23 23:28:00] x方向算符进度: 61/100, 当前算符: D_(0, 1) * D_(50, 51), 耗时: 10.522s
[2025-07-23 23:28:11] x方向算符进度: 62/100, 当前算符: D_(0, 1) * D_(50, 71), 耗时: 10.523s
[2025-07-23 23:28:21] x方向算符进度: 63/100, 当前算符: D_(0, 1) * D_(52, 53), 耗时: 10.522s
[2025-07-23 23:28:32] x方向算符进度: 64/100, 当前算符: D_(0, 1) * D_(53, 72), 耗时: 10.513s
[2025-07-23 23:28:42] x方向算符进度: 65/100, 当前算符: D_(0, 1) * D_(54, 55), 耗时: 10.558s
[2025-07-23 23:28:53] x方向算符进度: 66/100, 当前算符: D_(0, 1) * D_(54, 75), 耗时: 10.526s
[2025-07-23 23:29:03] x方向算符进度: 67/100, 当前算符: D_(0, 1) * D_(56, 57), 耗时: 10.459s
[2025-07-23 23:29:14] x方向算符进度: 68/100, 当前算符: D_(0, 1) * D_(57, 76), 耗时: 10.522s
[2025-07-23 23:29:24] x方向算符进度: 69/100, 当前算符: D_(0, 1) * D_(58, 59), 耗时: 10.522s
[2025-07-23 23:29:35] x方向算符进度: 70/100, 当前算符: D_(0, 1) * D_(58, 79), 耗时: 10.459s
[2025-07-23 23:29:45] x方向算符进度: 71/100, 当前算符: D_(0, 1) * D_(60, 61), 耗时: 10.505s
[2025-07-23 23:29:56] x方向算符进度: 72/100, 当前算符: D_(0, 1) * D_(61, 80), 耗时: 10.460s
[2025-07-23 23:30:06] x方向算符进度: 73/100, 当前算符: D_(0, 1) * D_(62, 63), 耗时: 10.516s
[2025-07-23 23:30:17] x方向算符进度: 74/100, 当前算符: D_(0, 1) * D_(62, 83), 耗时: 10.517s
[2025-07-23 23:30:27] x方向算符进度: 75/100, 当前算符: D_(0, 1) * D_(64, 65), 耗时: 10.509s
[2025-07-23 23:30:38] x方向算符进度: 76/100, 当前算符: D_(0, 1) * D_(65, 84), 耗时: 10.459s
[2025-07-23 23:30:48] x方向算符进度: 77/100, 当前算符: D_(0, 1) * D_(66, 67), 耗时: 10.512s
[2025-07-23 23:30:59] x方向算符进度: 78/100, 当前算符: D_(0, 1) * D_(66, 87), 耗时: 10.459s
[2025-07-23 23:31:09] x方向算符进度: 79/100, 当前算符: D_(0, 1) * D_(68, 69), 耗时: 10.459s
[2025-07-23 23:31:20] x方向算符进度: 80/100, 当前算符: D_(0, 1) * D_(69, 88), 耗时: 10.518s
[2025-07-23 23:31:30] x方向算符进度: 81/100, 当前算符: D_(0, 1) * D_(70, 71), 耗时: 10.519s
[2025-07-23 23:31:41] x方向算符进度: 82/100, 当前算符: D_(0, 1) * D_(70, 91), 耗时: 10.514s
[2025-07-23 23:31:51] x方向算符进度: 83/100, 当前算符: D_(0, 1) * D_(72, 73), 耗时: 10.461s
[2025-07-23 23:32:02] x方向算符进度: 84/100, 当前算符: D_(0, 1) * D_(73, 92), 耗时: 10.498s
[2025-07-23 23:32:12] x方向算符进度: 85/100, 当前算符: D_(0, 1) * D_(74, 75), 耗时: 10.517s
[2025-07-23 23:32:23] x方向算符进度: 86/100, 当前算符: D_(0, 1) * D_(74, 95), 耗时: 10.522s
[2025-07-23 23:32:33] x方向算符进度: 87/100, 当前算符: D_(0, 1) * D_(76, 77), 耗时: 10.526s
[2025-07-23 23:32:44] x方向算符进度: 88/100, 当前算符: D_(0, 1) * D_(77, 96), 耗时: 10.459s
[2025-07-23 23:32:54] x方向算符进度: 89/100, 当前算符: D_(0, 1) * D_(78, 79), 耗时: 10.519s
[2025-07-23 23:33:05] x方向算符进度: 90/100, 当前算符: D_(0, 1) * D_(78, 99), 耗时: 10.459s
[2025-07-23 23:33:15] x方向算符进度: 91/100, 当前算符: D_(0, 1) * D_(80, 81), 耗时: 10.526s
[2025-07-23 23:33:26] x方向算符进度: 92/100, 当前算符: D_(0, 1) * D_(82, 83), 耗时: 10.517s
[2025-07-23 23:33:36] x方向算符进度: 93/100, 当前算符: D_(0, 1) * D_(84, 85), 耗时: 10.523s
[2025-07-23 23:33:47] x方向算符进度: 94/100, 当前算符: D_(0, 1) * D_(86, 87), 耗时: 10.500s
[2025-07-23 23:33:57] x方向算符进度: 95/100, 当前算符: D_(0, 1) * D_(88, 89), 耗时: 10.459s
[2025-07-23 23:34:08] x方向算符进度: 96/100, 当前算符: D_(0, 1) * D_(90, 91), 耗时: 10.524s
[2025-07-23 23:34:18] x方向算符进度: 97/100, 当前算符: D_(0, 1) * D_(92, 93), 耗时: 10.460s
[2025-07-23 23:34:29] x方向算符进度: 98/100, 当前算符: D_(0, 1) * D_(94, 95), 耗时: 10.519s
[2025-07-23 23:34:39] x方向算符进度: 99/100, 当前算符: D_(0, 1) * D_(96, 97), 耗时: 10.519s
[2025-07-23 23:34:50] x方向算符进度: 100/100, 当前算符: D_(0, 1) * D_(98, 99), 耗时: 10.524s
[2025-07-23 23:34:50] x方向二聚体相关函数计算完成,耗时: 1058.64 秒
[2025-07-23 23:34:50] --------------------------------------------------------------------------------
[2025-07-23 23:34:50] 使用y-二聚体 (0, 3) (全局索引 100) 作为参考点, 计算y方向连通二聚体相关函数...
[2025-07-23 23:34:56] y方向算符进度: 1/100, 当前算符: D_(0, 3) * D_(0, 3), 耗时: 6.254s
[2025-07-23 23:35:05] y方向算符进度: 2/100, 当前算符: D_(0, 3) * D_(0, 19), 耗时: 8.757s
[2025-07-23 23:35:15] y方向算符进度: 3/100, 当前算符: D_(0, 3) * D_(1, 2), 耗时: 10.546s
[2025-07-23 23:35:26] y方向算符进度: 4/100, 当前算符: D_(0, 3) * D_(1, 18), 耗时: 10.507s
[2025-07-23 23:35:36] y方向算符进度: 5/100, 当前算符: D_(0, 3) * D_(2, 5), 耗时: 10.506s
[2025-07-23 23:35:45] y方向算符进度: 6/100, 当前算符: D_(0, 3) * D_(3, 4), 耗时: 8.755s
[2025-07-23 23:35:55] y方向算符进度: 7/100, 当前算符: D_(0, 3) * D_(4, 7), 耗时: 10.515s
[2025-07-23 23:36:06] y方向算符进度: 8/100, 当前算符: D_(0, 3) * D_(5, 6), 耗时: 10.547s
[2025-07-23 23:36:17] y方向算符进度: 9/100, 当前算符: D_(0, 3) * D_(6, 9), 耗时: 10.507s
[2025-07-23 23:36:27] y方向算符进度: 10/100, 当前算符: D_(0, 3) * D_(7, 8), 耗时: 10.547s
[2025-07-23 23:36:38] y方向算符进度: 11/100, 当前算符: D_(0, 3) * D_(8, 11), 耗时: 10.509s
[2025-07-23 23:36:48] y方向算符进度: 12/100, 当前算符: D_(0, 3) * D_(9, 10), 耗时: 10.538s
[2025-07-23 23:36:59] y方向算符进度: 13/100, 当前算符: D_(0, 3) * D_(10, 13), 耗时: 10.508s
[2025-07-23 23:37:09] y方向算符进度: 14/100, 当前算符: D_(0, 3) * D_(11, 12), 耗时: 10.534s
[2025-07-23 23:37:20] y方向算符进度: 15/100, 当前算符: D_(0, 3) * D_(12, 15), 耗时: 10.522s
[2025-07-23 23:37:30] y方向算符进度: 16/100, 当前算符: D_(0, 3) * D_(13, 14), 耗时: 10.536s
[2025-07-23 23:37:41] y方向算符进度: 17/100, 当前算符: D_(0, 3) * D_(14, 17), 耗时: 10.506s
[2025-07-23 23:37:51] y方向算符进度: 18/100, 当前算符: D_(0, 3) * D_(15, 16), 耗时: 10.507s
[2025-07-23 23:38:02] y方向算符进度: 19/100, 当前算符: D_(0, 3) * D_(16, 19), 耗时: 10.554s
[2025-07-23 23:38:12] y方向算符进度: 20/100, 当前算符: D_(0, 3) * D_(17, 18), 耗时: 10.522s
[2025-07-23 23:38:23] y方向算符进度: 21/100, 当前算符: D_(0, 3) * D_(20, 23), 耗时: 10.548s
[2025-07-23 23:38:33] y方向算符进度: 22/100, 当前算符: D_(0, 3) * D_(20, 39), 耗时: 10.509s
[2025-07-23 23:38:44] y方向算符进度: 23/100, 当前算符: D_(0, 3) * D_(21, 22), 耗时: 10.551s
[2025-07-23 23:38:54] y方向算符进度: 24/100, 当前算符: D_(0, 3) * D_(21, 38), 耗时: 10.507s
[2025-07-23 23:39:05] y方向算符进度: 25/100, 当前算符: D_(0, 3) * D_(22, 25), 耗时: 10.540s
[2025-07-23 23:39:16] y方向算符进度: 26/100, 当前算符: D_(0, 3) * D_(23, 24), 耗时: 10.514s
[2025-07-23 23:39:26] y方向算符进度: 27/100, 当前算符: D_(0, 3) * D_(24, 27), 耗时: 10.553s
[2025-07-23 23:39:37] y方向算符进度: 28/100, 当前算符: D_(0, 3) * D_(25, 26), 耗时: 10.521s
[2025-07-23 23:39:47] y方向算符进度: 29/100, 当前算符: D_(0, 3) * D_(26, 29), 耗时: 10.557s
[2025-07-23 23:39:58] y方向算符进度: 30/100, 当前算符: D_(0, 3) * D_(27, 28), 耗时: 10.535s
[2025-07-23 23:40:08] y方向算符进度: 31/100, 当前算符: D_(0, 3) * D_(28, 31), 耗时: 10.507s
[2025-07-23 23:40:19] y方向算符进度: 32/100, 当前算符: D_(0, 3) * D_(29, 30), 耗时: 10.545s
[2025-07-23 23:40:29] y方向算符进度: 33/100, 当前算符: D_(0, 3) * D_(30, 33), 耗时: 10.523s
[2025-07-23 23:40:40] y方向算符进度: 34/100, 当前算符: D_(0, 3) * D_(31, 32), 耗时: 10.514s
[2025-07-23 23:40:50] y方向算符进度: 35/100, 当前算符: D_(0, 3) * D_(32, 35), 耗时: 10.537s
[2025-07-23 23:41:01] y方向算符进度: 36/100, 当前算符: D_(0, 3) * D_(33, 34), 耗时: 10.510s
[2025-07-23 23:41:11] y方向算符进度: 37/100, 当前算符: D_(0, 3) * D_(34, 37), 耗时: 10.541s
[2025-07-23 23:41:22] y方向算符进度: 38/100, 当前算符: D_(0, 3) * D_(35, 36), 耗时: 10.523s
[2025-07-23 23:41:32] y方向算符进度: 39/100, 当前算符: D_(0, 3) * D_(36, 39), 耗时: 10.554s
[2025-07-23 23:41:43] y方向算符进度: 40/100, 当前算符: D_(0, 3) * D_(37, 38), 耗时: 10.509s
[2025-07-23 23:41:54] y方向算符进度: 41/100, 当前算符: D_(0, 3) * D_(40, 43), 耗时: 10.554s
[2025-07-23 23:42:04] y方向算符进度: 42/100, 当前算符: D_(0, 3) * D_(40, 59), 耗时: 10.508s
[2025-07-23 23:42:15] y方向算符进度: 43/100, 当前算符: D_(0, 3) * D_(41, 42), 耗时: 10.552s
[2025-07-23 23:42:25] y方向算符进度: 44/100, 当前算符: D_(0, 3) * D_(41, 58), 耗时: 10.603s
[2025-07-23 23:42:36] y方向算符进度: 45/100, 当前算符: D_(0, 3) * D_(42, 45), 耗时: 10.537s
[2025-07-23 23:42:46] y方向算符进度: 46/100, 当前算符: D_(0, 3) * D_(43, 44), 耗时: 10.550s
[2025-07-23 23:42:57] y方向算符进度: 47/100, 当前算符: D_(0, 3) * D_(44, 47), 耗时: 10.508s
[2025-07-23 23:43:07] y方向算符进度: 48/100, 当前算符: D_(0, 3) * D_(45, 46), 耗时: 10.554s
[2025-07-23 23:43:18] y方向算符进度: 49/100, 当前算符: D_(0, 3) * D_(46, 49), 耗时: 10.511s
[2025-07-23 23:43:28] y方向算符进度: 50/100, 当前算符: D_(0, 3) * D_(47, 48), 耗时: 10.538s
[2025-07-23 23:43:39] y方向算符进度: 51/100, 当前算符: D_(0, 3) * D_(48, 51), 耗时: 10.541s
[2025-07-23 23:43:49] y方向算符进度: 52/100, 当前算符: D_(0, 3) * D_(49, 50), 耗时: 10.518s
[2025-07-23 23:44:00] y方向算符进度: 53/100, 当前算符: D_(0, 3) * D_(50, 53), 耗时: 10.542s
[2025-07-23 23:44:11] y方向算符进度: 54/100, 当前算符: D_(0, 3) * D_(51, 52), 耗时: 10.512s
[2025-07-23 23:44:21] y方向算符进度: 55/100, 当前算符: D_(0, 3) * D_(52, 55), 耗时: 10.556s
[2025-07-23 23:44:32] y方向算符进度: 56/100, 当前算符: D_(0, 3) * D_(53, 54), 耗时: 10.506s
[2025-07-23 23:44:42] y方向算符进度: 57/100, 当前算符: D_(0, 3) * D_(54, 57), 耗时: 10.641s
[2025-07-23 23:44:53] y方向算符进度: 58/100, 当前算符: D_(0, 3) * D_(55, 56), 耗时: 10.523s
[2025-07-23 23:45:03] y方向算符进度: 59/100, 当前算符: D_(0, 3) * D_(56, 59), 耗时: 10.558s
[2025-07-23 23:45:14] y方向算符进度: 60/100, 当前算符: D_(0, 3) * D_(57, 58), 耗时: 10.511s
[2025-07-23 23:45:24] y方向算符进度: 61/100, 当前算符: D_(0, 3) * D_(60, 63), 耗时: 10.547s
[2025-07-23 23:45:35] y方向算符进度: 62/100, 当前算符: D_(0, 3) * D_(60, 79), 耗时: 10.552s
[2025-07-23 23:45:45] y方向算符进度: 63/100, 当前算符: D_(0, 3) * D_(61, 62), 耗时: 10.523s
[2025-07-23 23:45:56] y方向算符进度: 64/100, 当前算符: D_(0, 3) * D_(61, 78), 耗时: 10.554s
[2025-07-23 23:46:07] y方向算符进度: 65/100, 当前算符: D_(0, 3) * D_(62, 65), 耗时: 10.509s
[2025-07-23 23:46:17] y方向算符进度: 66/100, 当前算符: D_(0, 3) * D_(63, 64), 耗时: 10.533s
[2025-07-23 23:46:28] y方向算符进度: 67/100, 当前算符: D_(0, 3) * D_(64, 67), 耗时: 10.519s
[2025-07-23 23:46:38] y方向算符进度: 68/100, 当前算符: D_(0, 3) * D_(65, 66), 耗时: 10.518s
[2025-07-23 23:46:49] y方向算符进度: 69/100, 当前算符: D_(0, 3) * D_(66, 69), 耗时: 10.541s
[2025-07-23 23:46:59] y方向算符进度: 70/100, 当前算符: D_(0, 3) * D_(67, 68), 耗时: 10.510s
[2025-07-23 23:47:10] y方向算符进度: 71/100, 当前算符: D_(0, 3) * D_(68, 71), 耗时: 10.539s
[2025-07-23 23:47:20] y方向算符进度: 72/100, 当前算符: D_(0, 3) * D_(69, 70), 耗时: 10.511s
[2025-07-23 23:47:31] y方向算符进度: 73/100, 当前算符: D_(0, 3) * D_(70, 73), 耗时: 10.543s
[2025-07-23 23:47:41] y方向算符进度: 74/100, 当前算符: D_(0, 3) * D_(71, 72), 耗时: 10.522s
[2025-07-23 23:47:52] y方向算符进度: 75/100, 当前算符: D_(0, 3) * D_(72, 75), 耗时: 10.537s
[2025-07-23 23:48:02] y方向算符进度: 76/100, 当前算符: D_(0, 3) * D_(73, 74), 耗时: 10.506s
[2025-07-23 23:48:13] y方向算符进度: 77/100, 当前算符: D_(0, 3) * D_(74, 77), 耗时: 10.536s
[2025-07-23 23:48:23] y方向算符进度: 78/100, 当前算符: D_(0, 3) * D_(75, 76), 耗时: 10.536s
[2025-07-23 23:48:34] y方向算符进度: 79/100, 当前算符: D_(0, 3) * D_(76, 79), 耗时: 10.522s
[2025-07-23 23:48:44] y方向算符进度: 80/100, 当前算符: D_(0, 3) * D_(77, 78), 耗时: 10.535s
[2025-07-23 23:48:55] y方向算符进度: 81/100, 当前算符: D_(0, 3) * D_(80, 83), 耗时: 10.509s
[2025-07-23 23:49:05] y方向算符进度: 82/100, 当前算符: D_(0, 3) * D_(80, 99), 耗时: 10.551s
[2025-07-23 23:49:16] y方向算符进度: 83/100, 当前算符: D_(0, 3) * D_(81, 82), 耗时: 10.520s
[2025-07-23 23:49:27] y方向算符进度: 84/100, 当前算符: D_(0, 3) * D_(81, 98), 耗时: 10.549s
[2025-07-23 23:49:37] y方向算符进度: 85/100, 当前算符: D_(0, 3) * D_(82, 85), 耗时: 10.509s
[2025-07-23 23:49:48] y方向算符进度: 86/100, 当前算符: D_(0, 3) * D_(83, 84), 耗时: 10.533s
[2025-07-23 23:49:58] y方向算符进度: 87/100, 当前算符: D_(0, 3) * D_(84, 87), 耗时: 10.510s
[2025-07-23 23:50:09] y方向算符进度: 88/100, 当前算符: D_(0, 3) * D_(85, 86), 耗时: 10.541s
[2025-07-23 23:50:19] y方向算符进度: 89/100, 当前算符: D_(0, 3) * D_(86, 89), 耗时: 10.513s
[2025-07-23 23:50:30] y方向算符进度: 90/100, 当前算符: D_(0, 3) * D_(87, 88), 耗时: 10.558s
[2025-07-23 23:50:40] y方向算符进度: 91/100, 当前算符: D_(0, 3) * D_(88, 91), 耗时: 10.523s
[2025-07-23 23:50:51] y方向算符进度: 92/100, 当前算符: D_(0, 3) * D_(89, 90), 耗时: 10.552s
[2025-07-23 23:51:01] y方向算符进度: 93/100, 当前算符: D_(0, 3) * D_(90, 93), 耗时: 10.509s
[2025-07-23 23:51:12] y方向算符进度: 94/100, 当前算符: D_(0, 3) * D_(91, 92), 耗时: 10.506s
[2025-07-23 23:51:22] y方向算符进度: 95/100, 当前算符: D_(0, 3) * D_(92, 95), 耗时: 10.546s
[2025-07-23 23:51:33] y方向算符进度: 96/100, 当前算符: D_(0, 3) * D_(93, 94), 耗时: 10.509s
[2025-07-23 23:51:43] y方向算符进度: 97/100, 当前算符: D_(0, 3) * D_(94, 97), 耗时: 10.553s
[2025-07-23 23:51:54] y方向算符进度: 98/100, 当前算符: D_(0, 3) * D_(95, 96), 耗时: 10.523s
[2025-07-23 23:52:05] y方向算符进度: 99/100, 当前算符: D_(0, 3) * D_(96, 99), 耗时: 10.543s
[2025-07-23 23:52:15] y方向算符进度: 100/100, 当前算符: D_(0, 3) * D_(97, 98), 耗时: 10.510s
[2025-07-23 23:52:15] y方向二聚体相关函数计算完成,耗时: 1045.38 秒
[2025-07-23 23:52:15] 计算傅里叶变换...
[2025-07-23 23:52:16] 二聚体结构因子计算完成
[2025-07-23 23:52:17] 二聚体相关函数平均误差: 0.000571
[2025-07-23 23:52:17] 恢复原始样本数: 4096
[2025-07-23 23:52:17] ================================================================================
[2025-07-23 23:52:17] 所有分析完成
