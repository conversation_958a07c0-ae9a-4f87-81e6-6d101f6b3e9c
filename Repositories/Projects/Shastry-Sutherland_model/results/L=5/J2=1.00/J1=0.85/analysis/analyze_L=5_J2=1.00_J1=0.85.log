[2025-07-25 13:18:19] ================================================================================
[2025-07-25 13:18:19] 加载量子态: L=5, J2=1.00, J1=0.85
[2025-07-25 13:18:19] 设置样本数为: 1048576
[2025-07-25 13:18:19] 开始生成共享样本集...
[2025-07-25 13:21:38] 样本生成完成,耗时: 199.389 秒
[2025-07-25 13:21:38] ================================================================================
[2025-07-25 13:21:38] 开始计算自旋结构因子...
[2025-07-25 13:21:38] 初始化操作符缓存...
[2025-07-25 13:21:38] 预构建所有自旋相关操作符...
[2025-07-25 13:21:38] 开始计算自旋相关函数...
[2025-07-25 13:21:49] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.775s
[2025-07-25 13:22:00] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.326s
[2025-07-25 13:22:07] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.308s
[2025-07-25 13:22:13] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.317s
[2025-07-25 13:22:19] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.302s
[2025-07-25 13:22:26] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.315s
[2025-07-25 13:22:32] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.315s
[2025-07-25 13:22:38] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.302s
[2025-07-25 13:22:44] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.317s
[2025-07-25 13:22:51] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.301s
[2025-07-25 13:22:57] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.308s
[2025-07-25 13:23:03] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.302s
[2025-07-25 13:23:10] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.316s
[2025-07-25 13:23:16] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.315s
[2025-07-25 13:23:22] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.317s
[2025-07-25 13:23:29] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.304s
[2025-07-25 13:23:35] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.316s
[2025-07-25 13:23:41] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.303s
[2025-07-25 13:23:48] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.320s
[2025-07-25 13:23:54] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.325s
[2025-07-25 13:24:00] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.306s
[2025-07-25 13:24:07] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.305s
[2025-07-25 13:24:13] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.316s
[2025-07-25 13:24:19] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.308s
[2025-07-25 13:24:25] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.316s
[2025-07-25 13:24:32] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.318s
[2025-07-25 13:24:38] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.302s
[2025-07-25 13:24:44] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.315s
[2025-07-25 13:24:51] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.305s
[2025-07-25 13:24:57] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.311s
[2025-07-25 13:25:03] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.319s
[2025-07-25 13:25:10] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.315s
[2025-07-25 13:25:16] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.301s
[2025-07-25 13:25:22] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.302s
[2025-07-25 13:25:29] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.307s
[2025-07-25 13:25:35] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.308s
[2025-07-25 13:25:41] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.316s
[2025-07-25 13:25:48] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.320s
[2025-07-25 13:25:54] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.314s
[2025-07-25 13:26:00] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.320s
[2025-07-25 13:26:06] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.317s
[2025-07-25 13:26:13] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.324s
[2025-07-25 13:26:19] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.312s
[2025-07-25 13:26:25] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.313s
[2025-07-25 13:26:32] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.316s
[2025-07-25 13:26:38] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.316s
[2025-07-25 13:26:44] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.312s
[2025-07-25 13:26:51] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.318s
[2025-07-25 13:26:57] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.324s
[2025-07-25 13:27:03] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.306s
[2025-07-25 13:27:10] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.315s
[2025-07-25 13:27:16] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.304s
[2025-07-25 13:27:22] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.314s
[2025-07-25 13:27:29] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.311s
[2025-07-25 13:27:35] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.316s
[2025-07-25 13:27:41] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.307s
[2025-07-25 13:27:47] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.309s
[2025-07-25 13:27:54] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.317s
[2025-07-25 13:28:00] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.316s
[2025-07-25 13:28:06] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.324s
[2025-07-25 13:28:13] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.308s
[2025-07-25 13:28:19] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.308s
[2025-07-25 13:28:25] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.317s
[2025-07-25 13:28:32] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.315s
[2025-07-25 13:28:38] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.317s
[2025-07-25 13:28:44] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.316s
[2025-07-25 13:28:51] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.308s
[2025-07-25 13:28:57] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.316s
[2025-07-25 13:29:03] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.317s
[2025-07-25 13:29:10] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.320s
[2025-07-25 13:29:16] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.317s
[2025-07-25 13:29:22] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.342s
[2025-07-25 13:29:29] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.316s
[2025-07-25 13:29:35] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.315s
[2025-07-25 13:29:41] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.309s
[2025-07-25 13:29:48] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.316s
[2025-07-25 13:29:54] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.303s
[2025-07-25 13:30:00] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.306s
[2025-07-25 13:30:06] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.317s
[2025-07-25 13:30:13] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.317s
[2025-07-25 13:30:19] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.323s
[2025-07-25 13:30:25] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.312s
[2025-07-25 13:30:32] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.305s
[2025-07-25 13:30:38] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.314s
[2025-07-25 13:30:44] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.314s
[2025-07-25 13:30:51] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.316s
[2025-07-25 13:30:57] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.318s
[2025-07-25 13:31:03] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.317s
[2025-07-25 13:31:10] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.306s
[2025-07-25 13:31:16] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.317s
[2025-07-25 13:31:22] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.316s
[2025-07-25 13:31:29] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.309s
[2025-07-25 13:31:35] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.317s
[2025-07-25 13:31:41] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.315s
[2025-07-25 13:31:48] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.302s
[2025-07-25 13:31:54] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.316s
[2025-07-25 13:32:00] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.312s
[2025-07-25 13:32:06] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.317s
[2025-07-25 13:32:13] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.309s
[2025-07-25 13:32:19] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.305s
[2025-07-25 13:32:19] 自旋相关函数计算完成,总耗时 640.92 秒
[2025-07-25 13:32:19] 计算傅里叶变换...
[2025-07-25 13:32:20] 自旋结构因子计算完成
[2025-07-25 13:32:21] 自旋相关函数平均误差: 0.000563
[2025-07-25 13:32:21] ================================================================================
[2025-07-25 13:32:21] 开始计算对角二聚体结构因子...
[2025-07-25 13:32:21] 识别所有对角二聚体...
[2025-07-25 13:32:21] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 13:32:21] 预计算对角二聚体操作符...
[2025-07-25 13:32:23] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 13:32:30] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.362s
[2025-07-25 13:32:45] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.424s
[2025-07-25 13:32:56] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.647s
[2025-07-25 13:33:06] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.574s
[2025-07-25 13:33:17] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.618s
[2025-07-25 13:33:27] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.611s
[2025-07-25 13:33:38] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.623s
[2025-07-25 13:33:49] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.579s
[2025-07-25 13:33:59] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.637s
[2025-07-25 13:34:10] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.579s
[2025-07-25 13:34:20] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.621s
[2025-07-25 13:34:31] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.648s
[2025-07-25 13:34:42] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.614s
[2025-07-25 13:34:52] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.636s
[2025-07-25 13:35:03] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.579s
[2025-07-25 13:35:14] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.665s
[2025-07-25 13:35:24] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.609s
[2025-07-25 13:35:35] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.628s
[2025-07-25 13:35:52] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.613s
[2025-07-25 13:36:03] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.594s
[2025-07-25 13:36:14] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.617s
[2025-07-25 13:36:24] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.574s
[2025-07-25 13:36:35] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.622s
[2025-07-25 13:36:46] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.611s
[2025-07-25 13:36:56] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.617s
[2025-07-25 13:37:07] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.605s
[2025-07-25 13:37:17] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.578s
[2025-07-25 13:37:28] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.622s
[2025-07-25 13:37:39] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.597s
[2025-07-25 13:37:49] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.615s
[2025-07-25 13:38:00] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.597s
[2025-07-25 13:38:10] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.623s
[2025-07-25 13:38:21] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.577s
[2025-07-25 13:38:32] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.579s
[2025-07-25 13:38:42] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.620s
[2025-07-25 13:38:53] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.600s
[2025-07-25 13:39:03] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.624s
[2025-07-25 13:39:14] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.579s
[2025-07-25 13:39:25] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.610s
[2025-07-25 13:39:35] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.598s
[2025-07-25 13:39:46] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.621s
[2025-07-25 13:39:56] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.606s
[2025-07-25 13:40:07] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.613s
[2025-07-25 13:40:18] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.624s
[2025-07-25 13:40:28] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.588s
[2025-07-25 13:40:39] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.624s
[2025-07-25 13:40:49] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.602s
[2025-07-25 13:41:00] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.621s
[2025-07-25 13:41:11] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.586s
[2025-07-25 13:41:21] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.587s
[2025-07-25 13:41:32] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.624s
[2025-07-25 13:41:43] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.602s
[2025-07-25 13:41:53] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.623s
[2025-07-25 13:42:04] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.605s
[2025-07-25 13:42:14] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.622s
[2025-07-25 13:42:25] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.596s
[2025-07-25 13:42:36] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.623s
[2025-07-25 13:42:46] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.611s
[2025-07-25 13:42:57] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.623s
[2025-07-25 13:43:07] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.594s
[2025-07-25 13:43:18] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.625s
[2025-07-25 13:43:29] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.626s
[2025-07-25 13:43:39] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.624s
[2025-07-25 13:43:50] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.601s
[2025-07-25 13:44:01] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.622s
[2025-07-25 13:44:11] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.616s
[2025-07-25 13:44:22] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.698s
[2025-07-25 13:44:32] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.610s
[2025-07-25 13:44:43] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.623s
[2025-07-25 13:44:54] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.579s
[2025-07-25 13:45:04] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.616s
[2025-07-25 13:45:15] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.589s
[2025-07-25 13:45:25] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.621s
[2025-07-25 13:45:36] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.606s
[2025-07-25 13:45:47] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.621s
[2025-07-25 13:45:57] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.577s
[2025-07-25 13:46:08] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.621s
[2025-07-25 13:46:19] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.588s
[2025-07-25 13:46:29] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.623s
[2025-07-25 13:46:40] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.603s
[2025-07-25 13:46:50] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.623s
[2025-07-25 13:47:01] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.623s
[2025-07-25 13:47:10] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.847s
[2025-07-25 13:47:20] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.604s
[2025-07-25 13:47:31] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.606s
[2025-07-25 13:47:42] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.612s
[2025-07-25 13:47:52] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.623s
[2025-07-25 13:48:03] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.595s
[2025-07-25 13:48:14] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.622s
[2025-07-25 13:48:24] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.581s
[2025-07-25 13:48:35] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.624s
[2025-07-25 13:48:45] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.609s
[2025-07-25 13:48:56] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.624s
[2025-07-25 13:49:07] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.604s
[2025-07-25 13:49:17] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.582s
[2025-07-25 13:49:28] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.622s
[2025-07-25 13:49:38] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.595s
[2025-07-25 13:49:49] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.609s
[2025-07-25 13:50:00] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.612s
[2025-07-25 13:50:10] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.622s
[2025-07-25 13:50:10] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1067.02 秒
[2025-07-25 13:50:10] ================================================================================
[2025-07-25 13:50:10] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 13:50:17] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.305s
[2025-07-25 13:50:27] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.632s
[2025-07-25 13:50:36] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.828s
[2025-07-25 13:50:47] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.606s
[2025-07-25 13:50:57] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.608s
[2025-07-25 13:51:08] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.625s
[2025-07-25 13:51:18] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.628s
[2025-07-25 13:51:29] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.623s
[2025-07-25 13:51:40] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.592s
[2025-07-25 13:51:50] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.619s
[2025-07-25 13:52:01] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.607s
[2025-07-25 13:52:12] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.606s
[2025-07-25 13:52:22] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.595s
[2025-07-25 13:52:33] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.727s
[2025-07-25 13:52:43] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.619s
[2025-07-25 13:52:54] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.625s
[2025-07-25 13:53:05] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.597s
[2025-07-25 13:53:15] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.623s
[2025-07-25 13:53:26] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.614s
[2025-07-25 13:53:37] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.627s
[2025-07-25 13:53:47] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.626s
[2025-07-25 13:53:58] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.593s
[2025-07-25 13:54:08] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.623s
[2025-07-25 13:54:19] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.612s
[2025-07-25 13:54:30] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.615s
[2025-07-25 13:54:40] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.619s
[2025-07-25 13:54:51] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.621s
[2025-07-25 13:55:01] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.619s
[2025-07-25 13:55:12] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.621s
[2025-07-25 13:55:23] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.633s
[2025-07-25 13:55:33] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.625s
[2025-07-25 13:55:44] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.625s
[2025-07-25 13:55:55] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.627s
[2025-07-25 13:56:05] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.614s
[2025-07-25 13:56:16] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.622s
[2025-07-25 13:56:26] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.594s
[2025-07-25 13:56:37] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.636s
[2025-07-25 13:56:48] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.617s
[2025-07-25 13:56:58] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.618s
[2025-07-25 13:57:09] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.598s
[2025-07-25 13:57:20] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.624s
[2025-07-25 13:57:30] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.600s
[2025-07-25 13:57:41] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.623s
[2025-07-25 13:57:51] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.628s
[2025-07-25 13:58:02] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.623s
[2025-07-25 13:58:13] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.601s
[2025-07-25 13:58:23] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.622s
[2025-07-25 13:58:34] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.598s
[2025-07-25 13:58:44] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.590s
[2025-07-25 13:58:55] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.622s
[2025-07-25 13:59:06] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.626s
[2025-07-25 13:59:16] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.619s
[2025-07-25 13:59:27] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.617s
[2025-07-25 13:59:38] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.590s
[2025-07-25 13:59:48] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.621s
[2025-07-25 13:59:59] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.605s
[2025-07-25 14:00:09] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.625s
[2025-07-25 14:00:20] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.629s
[2025-07-25 14:00:31] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.623s
[2025-07-25 14:00:41] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.594s
[2025-07-25 14:00:52] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.624s
[2025-07-25 14:01:03] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.626s
[2025-07-25 14:01:13] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.629s
[2025-07-25 14:01:24] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.603s
[2025-07-25 14:01:34] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.605s
[2025-07-25 14:01:45] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.624s
[2025-07-25 14:01:56] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.637s
[2025-07-25 14:02:06] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.633s
[2025-07-25 14:02:17] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.624s
[2025-07-25 14:02:27] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.595s
[2025-07-25 14:02:38] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.621s
[2025-07-25 14:02:49] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.612s
[2025-07-25 14:02:59] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.629s
[2025-07-25 14:03:10] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.629s
[2025-07-25 14:03:21] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.622s
[2025-07-25 14:03:31] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.599s
[2025-07-25 14:03:42] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.621s
[2025-07-25 14:03:52] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.627s
[2025-07-25 14:04:03] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.619s
[2025-07-25 14:04:14] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.604s
[2025-07-25 14:04:24] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.606s
[2025-07-25 14:04:35] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.617s
[2025-07-25 14:04:46] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.632s
[2025-07-25 14:04:56] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.641s
[2025-07-25 14:05:07] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.608s
[2025-07-25 14:05:17] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.624s
[2025-07-25 14:05:28] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.597s
[2025-07-25 14:05:39] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.623s
[2025-07-25 14:05:49] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.616s
[2025-07-25 14:06:00] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.625s
[2025-07-25 14:06:11] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.619s
[2025-07-25 14:06:21] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.618s
[2025-07-25 14:06:32] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.603s
[2025-07-25 14:06:42] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.623s
[2025-07-25 14:06:53] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.594s
[2025-07-25 14:07:04] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.624s
[2025-07-25 14:07:14] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.622s
[2025-07-25 14:07:25] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.618s
[2025-07-25 14:07:34] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.822s
[2025-07-25 14:07:44] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.606s
[2025-07-25 14:07:44] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1054.03 秒
[2025-07-25 14:07:44] 计算傅里叶变换...
[2025-07-25 14:07:46] 对角二聚体结构因子计算完成
[2025-07-25 14:07:46] 对角二聚体相关函数平均误差: 0.000119
