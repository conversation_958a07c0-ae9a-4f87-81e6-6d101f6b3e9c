[2025-07-25 05:50:47] ================================================================================
[2025-07-25 05:50:47] 加载量子态: L=5, J2=1.00, J1=0.76
[2025-07-25 05:50:47] 设置样本数为: 1048576
[2025-07-25 05:50:47] 开始生成共享样本集...
[2025-07-25 05:54:06] 样本生成完成,耗时: 198.829 秒
[2025-07-25 05:54:06] ================================================================================
[2025-07-25 05:54:06] 开始计算自旋结构因子...
[2025-07-25 05:54:06] 初始化操作符缓存...
[2025-07-25 05:54:06] 预构建所有自旋相关操作符...
[2025-07-25 05:54:06] 开始计算自旋相关函数...
[2025-07-25 05:54:16] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.645s
[2025-07-25 05:54:27] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.262s
[2025-07-25 05:54:33] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.263s
[2025-07-25 05:54:40] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.298s
[2025-07-25 05:54:46] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.270s
[2025-07-25 05:54:52] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.380s
[2025-07-25 05:54:58] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.297s
[2025-07-25 05:55:05] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.277s
[2025-07-25 05:55:11] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.298s
[2025-07-25 05:55:17] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.276s
[2025-07-25 05:55:24] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.297s
[2025-07-25 05:55:30] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.275s
[2025-07-25 05:55:36] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.297s
[2025-07-25 05:55:42] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.297s
[2025-07-25 05:55:49] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.297s
[2025-07-25 05:55:55] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.276s
[2025-07-25 05:56:01] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.300s
[2025-07-25 05:56:08] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.276s
[2025-07-25 05:56:14] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.266s
[2025-07-25 05:56:20] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.298s
[2025-07-25 05:56:26] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.264s
[2025-07-25 05:56:33] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.281s
[2025-07-25 05:56:39] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.299s
[2025-07-25 05:56:45] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.263s
[2025-07-25 05:56:52] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.296s
[2025-07-25 05:56:58] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.296s
[2025-07-25 05:57:04] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.273s
[2025-07-25 05:57:10] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.300s
[2025-07-25 05:57:17] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.276s
[2025-07-25 05:57:23] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.263s
[2025-07-25 05:57:29] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.295s
[2025-07-25 05:57:36] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.296s
[2025-07-25 05:57:42] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.269s
[2025-07-25 05:57:48] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.262s
[2025-07-25 05:57:54] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.296s
[2025-07-25 05:58:01] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.262s
[2025-07-25 05:58:07] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.301s
[2025-07-25 05:58:13] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.296s
[2025-07-25 05:58:20] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.263s
[2025-07-25 05:58:26] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.296s
[2025-07-25 05:58:32] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.266s
[2025-07-25 05:58:38] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.270s
[2025-07-25 05:58:45] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.297s
[2025-07-25 05:58:51] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.265s
[2025-07-25 05:58:57] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.296s
[2025-07-25 05:59:04] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.300s
[2025-07-25 05:59:10] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.269s
[2025-07-25 05:59:16] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.297s
[2025-07-25 05:59:22] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.273s
[2025-07-25 05:59:29] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.266s
[2025-07-25 05:59:35] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.296s
[2025-07-25 05:59:41] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.276s
[2025-07-25 05:59:48] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.275s
[2025-07-25 05:59:54] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.262s
[2025-07-25 06:00:00] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.295s
[2025-07-25 06:00:06] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.260s
[2025-07-25 06:00:13] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.296s
[2025-07-25 06:00:19] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.299s
[2025-07-25 06:00:25] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.262s
[2025-07-25 06:00:32] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.298s
[2025-07-25 06:00:38] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.278s
[2025-07-25 06:00:44] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.266s
[2025-07-25 06:00:50] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.297s
[2025-07-25 06:00:57] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.267s
[2025-07-25 06:01:03] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.296s
[2025-07-25 06:01:09] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.301s
[2025-07-25 06:01:16] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.274s
[2025-07-25 06:01:22] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.295s
[2025-07-25 06:01:28] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.279s
[2025-07-25 06:01:34] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.272s
[2025-07-25 06:01:41] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.297s
[2025-07-25 06:01:47] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.269s
[2025-07-25 06:01:53] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.296s
[2025-07-25 06:02:00] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.295s
[2025-07-25 06:02:06] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.265s
[2025-07-25 06:02:12] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.298s
[2025-07-25 06:02:18] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.279s
[2025-07-25 06:02:25] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.263s
[2025-07-25 06:02:31] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.297s
[2025-07-25 06:02:37] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.272s
[2025-07-25 06:02:44] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.296s
[2025-07-25 06:02:50] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.296s
[2025-07-25 06:02:56] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.261s
[2025-07-25 06:03:02] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.295s
[2025-07-25 06:03:09] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.296s
[2025-07-25 06:03:15] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.295s
[2025-07-25 06:03:21] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.273s
[2025-07-25 06:03:28] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.297s
[2025-07-25 06:03:34] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.262s
[2025-07-25 06:03:40] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.265s
[2025-07-25 06:03:46] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.296s
[2025-07-25 06:03:53] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.351s
[2025-07-25 06:03:59] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.299s
[2025-07-25 06:04:05] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.295s
[2025-07-25 06:04:12] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.269s
[2025-07-25 06:04:18] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.296s
[2025-07-25 06:04:24] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.325s
[2025-07-25 06:04:30] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.272s
[2025-07-25 06:04:37] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.296s
[2025-07-25 06:04:43] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.266s
[2025-07-25 06:04:43] 自旋相关函数计算完成,总耗时 637.02 秒
[2025-07-25 06:04:43] 计算傅里叶变换...
[2025-07-25 06:04:44] 自旋结构因子计算完成
[2025-07-25 06:04:45] 自旋相关函数平均误差: 0.000625
[2025-07-25 06:04:45] ================================================================================
[2025-07-25 06:04:45] 开始计算对角二聚体结构因子...
[2025-07-25 06:04:45] 识别所有对角二聚体...
[2025-07-25 06:04:45] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 06:04:45] 预计算对角二聚体操作符...
[2025-07-25 06:04:47] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 06:04:53] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.337s
[2025-07-25 06:05:09] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.353s
[2025-07-25 06:05:19] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.618s
[2025-07-25 06:05:30] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.559s
[2025-07-25 06:05:41] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.609s
[2025-07-25 06:05:51] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.563s
[2025-07-25 06:06:02] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.609s
[2025-07-25 06:06:12] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.563s
[2025-07-25 06:06:23] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.616s
[2025-07-25 06:06:33] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.564s
[2025-07-25 06:06:44] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.608s
[2025-07-25 06:06:55] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.618s
[2025-07-25 06:07:05] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.551s
[2025-07-25 06:07:16] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.616s
[2025-07-25 06:07:26] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.563s
[2025-07-25 06:07:37] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.615s
[2025-07-25 06:07:48] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.563s
[2025-07-25 06:07:58] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.608s
[2025-07-25 06:08:16] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.590s
[2025-07-25 06:08:26] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.535s
[2025-07-25 06:08:37] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.576s
[2025-07-25 06:08:47] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.503s
[2025-07-25 06:08:58] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.585s
[2025-07-25 06:09:09] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.516s
[2025-07-25 06:09:19] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.575s
[2025-07-25 06:09:30] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.569s
[2025-07-25 06:09:40] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.534s
[2025-07-25 06:09:51] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.576s
[2025-07-25 06:10:01] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.504s
[2025-07-25 06:10:12] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.574s
[2025-07-25 06:10:22] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.502s
[2025-07-25 06:10:33] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.583s
[2025-07-25 06:10:43] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.504s
[2025-07-25 06:10:54] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.504s
[2025-07-25 06:11:05] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.575s
[2025-07-25 06:11:15] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.503s
[2025-07-25 06:11:26] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.581s
[2025-07-25 06:11:36] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.507s
[2025-07-25 06:11:47] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.568s
[2025-07-25 06:11:57] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.507s
[2025-07-25 06:12:08] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.576s
[2025-07-25 06:12:18] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.514s
[2025-07-25 06:12:29] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.514s
[2025-07-25 06:12:39] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.579s
[2025-07-25 06:12:50] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.507s
[2025-07-25 06:13:00] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.577s
[2025-07-25 06:13:11] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.510s
[2025-07-25 06:13:22] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.576s
[2025-07-25 06:13:32] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.531s
[2025-07-25 06:13:43] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.505s
[2025-07-25 06:13:53] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.581s
[2025-07-25 06:14:04] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.510s
[2025-07-25 06:14:14] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.584s
[2025-07-25 06:14:25] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.504s
[2025-07-25 06:14:35] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.577s
[2025-07-25 06:14:46] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.503s
[2025-07-25 06:14:56] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.580s
[2025-07-25 06:15:07] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.512s
[2025-07-25 06:15:18] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.584s
[2025-07-25 06:15:28] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.514s
[2025-07-25 06:15:39] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.582s
[2025-07-25 06:15:49] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.518s
[2025-07-25 06:16:00] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.578s
[2025-07-25 06:16:10] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.505s
[2025-07-25 06:16:21] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.577s
[2025-07-25 06:16:31] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.574s
[2025-07-25 06:16:42] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.521s
[2025-07-25 06:16:52] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.571s
[2025-07-25 06:17:03] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.577s
[2025-07-25 06:17:14] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.502s
[2025-07-25 06:17:24] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.666s
[2025-07-25 06:17:35] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.517s
[2025-07-25 06:17:45] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.576s
[2025-07-25 06:17:56] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.510s
[2025-07-25 06:18:06] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.576s
[2025-07-25 06:18:17] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.532s
[2025-07-25 06:18:28] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.576s
[2025-07-25 06:18:38] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.506s
[2025-07-25 06:18:49] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.584s
[2025-07-25 06:18:59] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.511s
[2025-07-25 06:19:10] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.581s
[2025-07-25 06:19:20] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.581s
[2025-07-25 06:19:29] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.769s
[2025-07-25 06:19:40] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.567s
[2025-07-25 06:19:50] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.518s
[2025-07-25 06:20:01] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.518s
[2025-07-25 06:20:11] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.579s
[2025-07-25 06:20:22] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.547s
[2025-07-25 06:20:32] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.577s
[2025-07-25 06:20:43] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.503s
[2025-07-25 06:20:53] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.582s
[2025-07-25 06:21:04] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.514s
[2025-07-25 06:21:15] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.578s
[2025-07-25 06:21:25] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.504s
[2025-07-25 06:21:36] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.523s
[2025-07-25 06:21:46] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.578s
[2025-07-25 06:21:57] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.521s
[2025-07-25 06:22:07] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.572s
[2025-07-25 06:22:18] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.513s
[2025-07-25 06:22:28] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.579s
[2025-07-25 06:22:28] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1061.32 秒
[2025-07-25 06:22:28] ================================================================================
[2025-07-25 06:22:28] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 06:22:35] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.284s
[2025-07-25 06:22:45] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.590s
[2025-07-25 06:22:54] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.803s
[2025-07-25 06:23:05] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.520s
[2025-07-25 06:23:15] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.519s
[2025-07-25 06:23:26] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.590s
[2025-07-25 06:23:36] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.524s
[2025-07-25 06:23:47] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.589s
[2025-07-25 06:23:57] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.548s
[2025-07-25 06:24:08] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.590s
[2025-07-25 06:24:18] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.539s
[2025-07-25 06:24:29] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.585s
[2025-07-25 06:24:40] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.518s
[2025-07-25 06:24:50] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.592s
[2025-07-25 06:25:01] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.525s
[2025-07-25 06:25:11] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.594s
[2025-07-25 06:25:22] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.528s
[2025-07-25 06:25:32] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.589s
[2025-07-25 06:25:43] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.546s
[2025-07-25 06:25:54] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.557s
[2025-07-25 06:26:04] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.594s
[2025-07-25 06:26:15] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.519s
[2025-07-25 06:26:25] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.590s
[2025-07-25 06:26:36] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.524s
[2025-07-25 06:26:46] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.585s
[2025-07-25 06:26:57] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.553s
[2025-07-25 06:27:07] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.587s
[2025-07-25 06:27:18] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.520s
[2025-07-25 06:27:29] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.588s
[2025-07-25 06:27:39] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.521s
[2025-07-25 06:27:50] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.593s
[2025-07-25 06:28:00] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.523s
[2025-07-25 06:28:11] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.546s
[2025-07-25 06:28:21] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.525s
[2025-07-25 06:28:32] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.589s
[2025-07-25 06:28:42] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.528s
[2025-07-25 06:28:53] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.588s
[2025-07-25 06:29:04] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.525s
[2025-07-25 06:29:14] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.586s
[2025-07-25 06:29:25] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.530s
[2025-07-25 06:29:35] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.593s
[2025-07-25 06:29:46] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.522s
[2025-07-25 06:29:56] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.590s
[2025-07-25 06:30:07] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.517s
[2025-07-25 06:30:17] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.591s
[2025-07-25 06:30:28] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.524s
[2025-07-25 06:30:39] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.589s
[2025-07-25 06:30:49] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.557s
[2025-07-25 06:31:00] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.520s
[2025-07-25 06:31:10] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.589s
[2025-07-25 06:31:21] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.596s
[2025-07-25 06:31:31] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.519s
[2025-07-25 06:31:42] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.589s
[2025-07-25 06:31:53] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.544s
[2025-07-25 06:32:03] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.589s
[2025-07-25 06:32:14] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.524s
[2025-07-25 06:32:24] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.593s
[2025-07-25 06:32:35] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.521s
[2025-07-25 06:32:45] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.591s
[2025-07-25 06:32:56] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.529s
[2025-07-25 06:33:06] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.594s
[2025-07-25 06:33:17] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.521s
[2025-07-25 06:33:28] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.592s
[2025-07-25 06:33:38] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.526s
[2025-07-25 06:33:49] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.518s
[2025-07-25 06:33:59] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.592s
[2025-07-25 06:34:10] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.522s
[2025-07-25 06:34:20] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.525s
[2025-07-25 06:34:31] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.592s
[2025-07-25 06:34:41] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.518s
[2025-07-25 06:34:52] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.589s
[2025-07-25 06:35:02] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.517s
[2025-07-25 06:35:13] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.595s
[2025-07-25 06:35:24] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.518s
[2025-07-25 06:35:34] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.588s
[2025-07-25 06:35:45] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.520s
[2025-07-25 06:35:55] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.588s
[2025-07-25 06:36:06] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.520s
[2025-07-25 06:36:16] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.588s
[2025-07-25 06:36:27] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.522s
[2025-07-25 06:36:37] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.521s
[2025-07-25 06:36:48] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.589s
[2025-07-25 06:36:59] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.538s
[2025-07-25 06:37:09] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.590s
[2025-07-25 06:37:20] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.524s
[2025-07-25 06:37:30] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.591s
[2025-07-25 06:37:41] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.536s
[2025-07-25 06:37:51] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.588s
[2025-07-25 06:38:02] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.546s
[2025-07-25 06:38:13] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.588s
[2025-07-25 06:38:23] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.546s
[2025-07-25 06:38:34] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.589s
[2025-07-25 06:38:44] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.519s
[2025-07-25 06:38:55] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.591s
[2025-07-25 06:39:05] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.535s
[2025-07-25 06:39:16] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.590s
[2025-07-25 06:39:27] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.588s
[2025-07-25 06:39:37] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.521s
[2025-07-25 06:39:46] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.800s
[2025-07-25 06:39:56] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.524s
[2025-07-25 06:39:56] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1048.02 秒
[2025-07-25 06:39:57] 计算傅里叶变换...
[2025-07-25 06:39:57] 对角二聚体结构因子计算完成
[2025-07-25 06:39:58] 对角二聚体相关函数平均误差: 0.000134
