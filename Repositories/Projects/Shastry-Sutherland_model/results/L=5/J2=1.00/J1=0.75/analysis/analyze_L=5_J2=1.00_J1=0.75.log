[2025-07-25 05:01:19] ================================================================================
[2025-07-25 05:01:19] 加载量子态: L=5, J2=1.00, J1=0.75
[2025-07-25 05:01:19] 设置样本数为: 1048576
[2025-07-25 05:01:19] 开始生成共享样本集...
[2025-07-25 05:04:38] 样本生成完成,耗时: 198.541 秒
[2025-07-25 05:04:38] ================================================================================
[2025-07-25 05:04:38] 开始计算自旋结构因子...
[2025-07-25 05:04:38] 初始化操作符缓存...
[2025-07-25 05:04:38] 预构建所有自旋相关操作符...
[2025-07-25 05:04:38] 开始计算自旋相关函数...
[2025-07-25 05:04:47] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.590s
[2025-07-25 05:04:59] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.334s
[2025-07-25 05:05:05] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.296s
[2025-07-25 05:05:11] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.295s
[2025-07-25 05:05:17] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.265s
[2025-07-25 05:05:24] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.267s
[2025-07-25 05:05:30] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.297s
[2025-07-25 05:05:36] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.265s
[2025-07-25 05:05:43] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.265s
[2025-07-25 05:05:49] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.265s
[2025-07-25 05:05:55] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.264s
[2025-07-25 05:06:01] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.265s
[2025-07-25 05:06:08] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.297s
[2025-07-25 05:06:14] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.297s
[2025-07-25 05:06:20] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.296s
[2025-07-25 05:06:27] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.265s
[2025-07-25 05:06:33] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.266s
[2025-07-25 05:06:39] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.264s
[2025-07-25 05:06:45] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.298s
[2025-07-25 05:06:52] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.408s
[2025-07-25 05:06:58] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.266s
[2025-07-25 05:07:04] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.267s
[2025-07-25 05:07:11] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.265s
[2025-07-25 05:07:17] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.297s
[2025-07-25 05:07:23] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.298s
[2025-07-25 05:07:29] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.297s
[2025-07-25 05:07:36] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.266s
[2025-07-25 05:07:42] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.267s
[2025-07-25 05:07:48] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.264s
[2025-07-25 05:07:55] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.296s
[2025-07-25 05:08:01] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.298s
[2025-07-25 05:08:07] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.298s
[2025-07-25 05:08:13] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.265s
[2025-07-25 05:08:20] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.265s
[2025-07-25 05:08:26] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.266s
[2025-07-25 05:08:32] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.265s
[2025-07-25 05:08:39] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.268s
[2025-07-25 05:08:45] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.265s
[2025-07-25 05:08:51] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.298s
[2025-07-25 05:08:57] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.298s
[2025-07-25 05:09:04] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.298s
[2025-07-25 05:09:10] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.298s
[2025-07-25 05:09:16] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.297s
[2025-07-25 05:09:23] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.297s
[2025-07-25 05:09:29] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.265s
[2025-07-25 05:09:35] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.265s
[2025-07-25 05:09:41] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.296s
[2025-07-25 05:09:48] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.298s
[2025-07-25 05:09:54] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.298s
[2025-07-25 05:10:00] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.266s
[2025-07-25 05:10:07] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.265s
[2025-07-25 05:10:13] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.264s
[2025-07-25 05:10:19] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.296s
[2025-07-25 05:10:25] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.296s
[2025-07-25 05:10:32] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.265s
[2025-07-25 05:10:38] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.267s
[2025-07-25 05:10:44] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.269s
[2025-07-25 05:10:51] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.297s
[2025-07-25 05:10:57] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.298s
[2025-07-25 05:11:03] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.298s
[2025-07-25 05:11:09] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.269s
[2025-07-25 05:11:16] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.268s
[2025-07-25 05:11:22] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.268s
[2025-07-25 05:11:28] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.298s
[2025-07-25 05:11:35] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.301s
[2025-07-25 05:11:41] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.297s
[2025-07-25 05:11:47] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.269s
[2025-07-25 05:11:53] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.269s
[2025-07-25 05:12:00] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.302s
[2025-07-25 05:12:06] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.300s
[2025-07-25 05:12:12] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.297s
[2025-07-25 05:12:19] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.266s
[2025-07-25 05:12:25] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.266s
[2025-07-25 05:12:31] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.265s
[2025-07-25 05:12:37] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.297s
[2025-07-25 05:12:44] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.298s
[2025-07-25 05:12:50] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.266s
[2025-07-25 05:12:56] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.265s
[2025-07-25 05:13:02] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.268s
[2025-07-25 05:13:09] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.303s
[2025-07-25 05:13:15] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.301s
[2025-07-25 05:13:21] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.268s
[2025-07-25 05:13:28] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.265s
[2025-07-25 05:13:34] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.270s
[2025-07-25 05:13:40] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.301s
[2025-07-25 05:13:46] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.300s
[2025-07-25 05:13:53] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.299s
[2025-07-25 05:13:59] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.268s
[2025-07-25 05:14:05] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.267s
[2025-07-25 05:14:12] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.302s
[2025-07-25 05:14:18] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.300s
[2025-07-25 05:14:24] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.300s
[2025-07-25 05:14:30] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.271s
[2025-07-25 05:14:37] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.300s
[2025-07-25 05:14:43] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.270s
[2025-07-25 05:14:49] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.269s
[2025-07-25 05:14:56] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.300s
[2025-07-25 05:15:02] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.302s
[2025-07-25 05:15:08] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.299s
[2025-07-25 05:15:15] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.266s
[2025-07-25 05:15:15] 自旋相关函数计算完成,总耗时 636.81 秒
[2025-07-25 05:15:15] 计算傅里叶变换...
[2025-07-25 05:15:15] 自旋结构因子计算完成
[2025-07-25 05:15:16] 自旋相关函数平均误差: 0.000575
[2025-07-25 05:15:16] ================================================================================
[2025-07-25 05:15:16] 开始计算对角二聚体结构因子...
[2025-07-25 05:15:16] 识别所有对角二聚体...
[2025-07-25 05:15:16] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 05:15:16] 预计算对角二聚体操作符...
[2025-07-25 05:15:18] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 05:15:25] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.309s
[2025-07-25 05:15:40] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.444s
[2025-07-25 05:15:51] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.605s
[2025-07-25 05:16:01] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.537s
[2025-07-25 05:16:12] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.538s
[2025-07-25 05:16:23] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.604s
[2025-07-25 05:16:33] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.539s
[2025-07-25 05:16:44] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.537s
[2025-07-25 05:16:54] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.637s
[2025-07-25 05:17:05] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.540s
[2025-07-25 05:17:15] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.541s
[2025-07-25 05:17:26] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.538s
[2025-07-25 05:17:36] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.604s
[2025-07-25 05:17:47] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.605s
[2025-07-25 05:17:58] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.537s
[2025-07-25 05:18:08] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.543s
[2025-07-25 05:18:19] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.544s
[2025-07-25 05:18:29] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.610s
[2025-07-25 05:18:47] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.604s
[2025-07-25 05:18:57] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.580s
[2025-07-25 05:19:08] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.516s
[2025-07-25 05:19:19] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.517s
[2025-07-25 05:19:29] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.578s
[2025-07-25 05:19:40] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.578s
[2025-07-25 05:19:50] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.575s
[2025-07-25 05:20:01] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.576s
[2025-07-25 05:20:11] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.516s
[2025-07-25 05:20:22] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.516s
[2025-07-25 05:20:32] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.576s
[2025-07-25 05:20:43] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.577s
[2025-07-25 05:20:54] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.575s
[2025-07-25 05:21:04] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.514s
[2025-07-25 05:21:15] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.517s
[2025-07-25 05:21:25] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.516s
[2025-07-25 05:21:36] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.577s
[2025-07-25 05:21:46] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.575s
[2025-07-25 05:21:57] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.516s
[2025-07-25 05:22:07] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.517s
[2025-07-25 05:22:18] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.578s
[2025-07-25 05:22:28] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.575s
[2025-07-25 05:22:39] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.578s
[2025-07-25 05:22:50] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.581s
[2025-07-25 05:23:00] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.577s
[2025-07-25 05:23:11] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.516s
[2025-07-25 05:23:21] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.517s
[2025-07-25 05:23:32] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.518s
[2025-07-25 05:23:42] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.579s
[2025-07-25 05:23:53] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.576s
[2025-07-25 05:24:03] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.515s
[2025-07-25 05:24:14] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.515s
[2025-07-25 05:24:24] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.519s
[2025-07-25 05:24:35] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.578s
[2025-07-25 05:24:46] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.581s
[2025-07-25 05:24:56] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.578s
[2025-07-25 05:25:07] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.517s
[2025-07-25 05:25:17] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.517s
[2025-07-25 05:25:28] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.576s
[2025-07-25 05:25:38] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.580s
[2025-07-25 05:25:49] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.578s
[2025-07-25 05:26:00] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.514s
[2025-07-25 05:26:10] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.517s
[2025-07-25 05:26:21] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.578s
[2025-07-25 05:26:31] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.577s
[2025-07-25 05:26:42] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.577s
[2025-07-25 05:26:52] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.516s
[2025-07-25 05:27:03] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.517s
[2025-07-25 05:27:13] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.519s
[2025-07-25 05:27:24] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.580s
[2025-07-25 05:27:34] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.579s
[2025-07-25 05:27:45] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.517s
[2025-07-25 05:27:56] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.517s
[2025-07-25 05:28:06] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.515s
[2025-07-25 05:28:17] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.578s
[2025-07-25 05:28:27] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.578s
[2025-07-25 05:28:38] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.577s
[2025-07-25 05:28:48] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.517s
[2025-07-25 05:28:59] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.516s
[2025-07-25 05:29:09] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.518s
[2025-07-25 05:29:20] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.516s
[2025-07-25 05:29:30] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.577s
[2025-07-25 05:29:41] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.578s
[2025-07-25 05:29:52] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.575s
[2025-07-25 05:30:00] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.819s
[2025-07-25 05:30:11] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.518s
[2025-07-25 05:30:22] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.580s
[2025-07-25 05:30:32] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.583s
[2025-07-25 05:30:43] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.581s
[2025-07-25 05:30:53] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.524s
[2025-07-25 05:31:04] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.523s
[2025-07-25 05:31:14] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.524s
[2025-07-25 05:31:25] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.587s
[2025-07-25 05:31:35] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.583s
[2025-07-25 05:31:46] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.581s
[2025-07-25 05:31:57] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.579s
[2025-07-25 05:32:07] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.520s
[2025-07-25 05:32:18] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.521s
[2025-07-25 05:32:28] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.526s
[2025-07-25 05:32:39] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.580s
[2025-07-25 05:32:49] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.580s
[2025-07-25 05:33:00] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.579s
[2025-07-25 05:33:00] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1061.43 秒
[2025-07-25 05:33:00] ================================================================================
[2025-07-25 05:33:00] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 05:33:06] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.294s
[2025-07-25 05:33:17] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.587s
[2025-07-25 05:33:26] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.817s
[2025-07-25 05:33:36] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.525s
[2025-07-25 05:33:47] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.522s
[2025-07-25 05:33:57] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.584s
[2025-07-25 05:34:08] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.588s
[2025-07-25 05:34:18] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.589s
[2025-07-25 05:34:29] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.529s
[2025-07-25 05:34:40] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.585s
[2025-07-25 05:34:50] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.527s
[2025-07-25 05:35:01] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.529s
[2025-07-25 05:35:11] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.526s
[2025-07-25 05:35:22] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.665s
[2025-07-25 05:35:32] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.590s
[2025-07-25 05:35:43] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.527s
[2025-07-25 05:35:53] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.529s
[2025-07-25 05:36:04] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.522s
[2025-07-25 05:36:15] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.585s
[2025-07-25 05:36:25] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.586s
[2025-07-25 05:36:36] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.582s
[2025-07-25 05:36:46] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.523s
[2025-07-25 05:36:57] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.524s
[2025-07-25 05:37:07] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.582s
[2025-07-25 05:37:18] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.584s
[2025-07-25 05:37:29] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.582s
[2025-07-25 05:37:39] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.584s
[2025-07-25 05:37:50] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.583s
[2025-07-25 05:38:00] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.523s
[2025-07-25 05:38:11] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.522s
[2025-07-25 05:38:21] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.528s
[2025-07-25 05:38:32] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.585s
[2025-07-25 05:38:42] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.584s
[2025-07-25 05:38:53] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.583s
[2025-07-25 05:39:04] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.521s
[2025-07-25 05:39:14] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.523s
[2025-07-25 05:39:25] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.585s
[2025-07-25 05:39:35] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.581s
[2025-07-25 05:39:46] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.583s
[2025-07-25 05:39:56] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.522s
[2025-07-25 05:40:07] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.528s
[2025-07-25 05:40:17] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.523s
[2025-07-25 05:40:28] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.583s
[2025-07-25 05:40:39] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.586s
[2025-07-25 05:40:49] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.525s
[2025-07-25 05:41:00] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.523s
[2025-07-25 05:41:10] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.522s
[2025-07-25 05:41:21] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.521s
[2025-07-25 05:41:31] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.522s
[2025-07-25 05:41:42] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.522s
[2025-07-25 05:41:52] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.587s
[2025-07-25 05:42:03] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.587s
[2025-07-25 05:42:13] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.522s
[2025-07-25 05:42:24] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.525s
[2025-07-25 05:42:34] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.523s
[2025-07-25 05:42:45] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.583s
[2025-07-25 05:42:56] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.613s
[2025-07-25 05:43:06] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.590s
[2025-07-25 05:43:17] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.524s
[2025-07-25 05:43:27] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.524s
[2025-07-25 05:43:38] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.585s
[2025-07-25 05:43:48] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.589s
[2025-07-25 05:43:59] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.587s
[2025-07-25 05:44:10] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.526s
[2025-07-25 05:44:20] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.524s
[2025-07-25 05:44:31] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.527s
[2025-07-25 05:44:41] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.589s
[2025-07-25 05:44:52] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.588s
[2025-07-25 05:45:02] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.529s
[2025-07-25 05:45:13] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.524s
[2025-07-25 05:45:23] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.521s
[2025-07-25 05:45:34] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.581s
[2025-07-25 05:45:45] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.585s
[2025-07-25 05:45:55] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.585s
[2025-07-25 05:46:06] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.525s
[2025-07-25 05:46:16] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.523s
[2025-07-25 05:46:27] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.583s
[2025-07-25 05:46:37] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.586s
[2025-07-25 05:46:48] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.589s
[2025-07-25 05:46:58] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.522s
[2025-07-25 05:47:09] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.521s
[2025-07-25 05:47:20] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.522s
[2025-07-25 05:47:30] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.584s
[2025-07-25 05:47:41] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.586s
[2025-07-25 05:47:51] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.525s
[2025-07-25 05:48:02] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.525s
[2025-07-25 05:48:12] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.529s
[2025-07-25 05:48:23] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.584s
[2025-07-25 05:48:33] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.582s
[2025-07-25 05:48:44] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.583s
[2025-07-25 05:48:55] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.584s
[2025-07-25 05:49:05] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.582s
[2025-07-25 05:49:16] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.521s
[2025-07-25 05:49:26] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.522s
[2025-07-25 05:49:37] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.526s
[2025-07-25 05:49:47] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.592s
[2025-07-25 05:49:58] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.589s
[2025-07-25 05:50:09] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.590s
[2025-07-25 05:50:17] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.771s
[2025-07-25 05:50:28] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.528s
[2025-07-25 05:50:28] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1047.95 秒
[2025-07-25 05:50:28] 计算傅里叶变换...
[2025-07-25 05:50:29] 对角二聚体结构因子计算完成
[2025-07-25 05:50:29] 对角二聚体相关函数平均误差: 0.000124
