[2025-07-25 07:29:47] ================================================================================
[2025-07-25 07:29:47] 加载量子态: L=5, J2=1.00, J1=0.78
[2025-07-25 07:29:47] 设置样本数为: 1048576
[2025-07-25 07:29:47] 开始生成共享样本集...
[2025-07-25 07:33:06] 样本生成完成,耗时: 199.200 秒
[2025-07-25 07:33:06] ================================================================================
[2025-07-25 07:33:06] 开始计算自旋结构因子...
[2025-07-25 07:33:06] 初始化操作符缓存...
[2025-07-25 07:33:06] 预构建所有自旋相关操作符...
[2025-07-25 07:33:06] 开始计算自旋相关函数...
[2025-07-25 07:33:16] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.553s
[2025-07-25 07:33:27] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.305s
[2025-07-25 07:33:33] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.305s
[2025-07-25 07:33:40] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.319s
[2025-07-25 07:33:46] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.289s
[2025-07-25 07:33:52] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.343s
[2025-07-25 07:33:58] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.316s
[2025-07-25 07:34:05] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.288s
[2025-07-25 07:34:11] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.318s
[2025-07-25 07:34:17] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.288s
[2025-07-25 07:34:24] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.308s
[2025-07-25 07:34:30] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.292s
[2025-07-25 07:34:36] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.316s
[2025-07-25 07:34:43] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.308s
[2025-07-25 07:34:49] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.318s
[2025-07-25 07:34:55] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.288s
[2025-07-25 07:35:02] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.319s
[2025-07-25 07:35:08] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.288s
[2025-07-25 07:35:14] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.305s
[2025-07-25 07:35:20] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.319s
[2025-07-25 07:35:27] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.289s
[2025-07-25 07:35:33] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.288s
[2025-07-25 07:35:39] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.320s
[2025-07-25 07:35:46] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.308s
[2025-07-25 07:35:52] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.315s
[2025-07-25 07:35:58] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.316s
[2025-07-25 07:36:05] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.288s
[2025-07-25 07:36:11] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.319s
[2025-07-25 07:36:17] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.287s
[2025-07-25 07:36:24] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.307s
[2025-07-25 07:36:30] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.313s
[2025-07-25 07:36:36] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.315s
[2025-07-25 07:36:42] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.287s
[2025-07-25 07:36:49] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.290s
[2025-07-25 07:36:55] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.307s
[2025-07-25 07:37:01] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.290s
[2025-07-25 07:37:08] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.318s
[2025-07-25 07:37:14] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.317s
[2025-07-25 07:37:20] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.307s
[2025-07-25 07:37:27] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.310s
[2025-07-25 07:37:33] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.307s
[2025-07-25 07:37:39] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.308s
[2025-07-25 07:37:46] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.308s
[2025-07-25 07:37:52] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.308s
[2025-07-25 07:37:58] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.317s
[2025-07-25 07:38:04] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.319s
[2025-07-25 07:38:11] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.308s
[2025-07-25 07:38:17] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.307s
[2025-07-25 07:38:23] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.308s
[2025-07-25 07:38:30] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.288s
[2025-07-25 07:38:36] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.315s
[2025-07-25 07:38:42] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.287s
[2025-07-25 07:38:49] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.307s
[2025-07-25 07:38:55] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.307s
[2025-07-25 07:39:01] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.316s
[2025-07-25 07:39:08] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.292s
[2025-07-25 07:39:14] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.308s
[2025-07-25 07:39:20] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.319s
[2025-07-25 07:39:26] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.308s
[2025-07-25 07:39:33] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.319s
[2025-07-25 07:39:39] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.288s
[2025-07-25 07:39:45] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.288s
[2025-07-25 07:39:52] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.319s
[2025-07-25 07:39:58] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.307s
[2025-07-25 07:40:04] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.310s
[2025-07-25 07:40:11] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.318s
[2025-07-25 07:40:17] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.288s
[2025-07-25 07:40:23] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.316s
[2025-07-25 07:40:30] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.309s
[2025-07-25 07:40:36] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.305s
[2025-07-25 07:40:42] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.319s
[2025-07-25 07:40:48] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.287s
[2025-07-25 07:40:55] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.314s
[2025-07-25 07:41:01] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.314s
[2025-07-25 07:41:07] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.308s
[2025-07-25 07:41:14] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.319s
[2025-07-25 07:41:20] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.287s
[2025-07-25 07:41:26] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.288s
[2025-07-25 07:41:33] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.318s
[2025-07-25 07:41:39] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.306s
[2025-07-25 07:41:45] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.315s
[2025-07-25 07:41:52] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.309s
[2025-07-25 07:41:58] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.291s
[2025-07-25 07:42:04] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.312s
[2025-07-25 07:42:10] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.312s
[2025-07-25 07:42:17] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.315s
[2025-07-25 07:42:23] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.307s
[2025-07-25 07:42:29] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.318s
[2025-07-25 07:42:36] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.289s
[2025-07-25 07:42:42] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.303s
[2025-07-25 07:42:48] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.315s
[2025-07-25 07:42:55] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.304s
[2025-07-25 07:43:01] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.319s
[2025-07-25 07:43:07] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.315s
[2025-07-25 07:43:14] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.288s
[2025-07-25 07:43:20] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.316s
[2025-07-25 07:43:26] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.306s
[2025-07-25 07:43:32] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.306s
[2025-07-25 07:43:39] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.307s
[2025-07-25 07:43:45] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.288s
[2025-07-25 07:43:45] 自旋相关函数计算完成,总耗时 639.01 秒
[2025-07-25 07:43:45] 计算傅里叶变换...
[2025-07-25 07:43:46] 自旋结构因子计算完成
[2025-07-25 07:43:47] 自旋相关函数平均误差: 0.000627
[2025-07-25 07:43:47] ================================================================================
[2025-07-25 07:43:47] 开始计算对角二聚体结构因子...
[2025-07-25 07:43:47] 识别所有对角二聚体...
[2025-07-25 07:43:47] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 07:43:47] 预计算对角二聚体操作符...
[2025-07-25 07:43:49] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 07:43:55] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.343s
[2025-07-25 07:44:11] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.489s
[2025-07-25 07:44:21] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.648s
[2025-07-25 07:44:32] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.576s
[2025-07-25 07:44:43] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.617s
[2025-07-25 07:44:53] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.613s
[2025-07-25 07:45:04] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.619s
[2025-07-25 07:45:14] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.579s
[2025-07-25 07:45:25] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.636s
[2025-07-25 07:45:36] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.578s
[2025-07-25 07:45:46] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.618s
[2025-07-25 07:45:57] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.652s
[2025-07-25 07:46:08] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.612s
[2025-07-25 07:46:18] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.635s
[2025-07-25 07:46:29] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.578s
[2025-07-25 07:46:39] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.649s
[2025-07-25 07:46:50] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.582s
[2025-07-25 07:47:01] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.619s
[2025-07-25 07:47:18] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.692s
[2025-07-25 07:47:29] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.591s
[2025-07-25 07:47:40] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.623s
[2025-07-25 07:47:50] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.575s
[2025-07-25 07:48:01] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.629s
[2025-07-25 07:48:11] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.585s
[2025-07-25 07:48:22] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.616s
[2025-07-25 07:48:33] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.608s
[2025-07-25 07:48:43] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.570s
[2025-07-25 07:48:54] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.623s
[2025-07-25 07:49:04] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.593s
[2025-07-25 07:49:15] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.614s
[2025-07-25 07:49:26] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.590s
[2025-07-25 07:49:36] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.628s
[2025-07-25 07:49:47] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.574s
[2025-07-25 07:49:57] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.574s
[2025-07-25 07:50:08] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.619s
[2025-07-25 07:50:19] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.594s
[2025-07-25 07:50:29] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.627s
[2025-07-25 07:50:40] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.574s
[2025-07-25 07:50:50] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.608s
[2025-07-25 07:51:01] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.594s
[2025-07-25 07:51:12] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.622s
[2025-07-25 07:51:22] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.590s
[2025-07-25 07:51:33] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.590s
[2025-07-25 07:51:43] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.626s
[2025-07-25 07:51:54] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.574s
[2025-07-25 07:52:05] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.625s
[2025-07-25 07:52:15] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.588s
[2025-07-25 07:52:26] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.619s
[2025-07-25 07:52:36] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.571s
[2025-07-25 07:52:47] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.573s
[2025-07-25 07:52:58] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.627s
[2025-07-25 07:53:08] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.591s
[2025-07-25 07:53:19] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.628s
[2025-07-25 07:53:29] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.588s
[2025-07-25 07:53:40] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.624s
[2025-07-25 07:53:51] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.574s
[2025-07-25 07:54:01] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.626s
[2025-07-25 07:54:12] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.590s
[2025-07-25 07:54:22] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.627s
[2025-07-25 07:54:33] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.574s
[2025-07-25 07:54:44] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.628s
[2025-07-25 07:54:54] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.593s
[2025-07-25 07:55:05] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.628s
[2025-07-25 07:55:15] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.591s
[2025-07-25 07:55:26] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.624s
[2025-07-25 07:55:37] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.615s
[2025-07-25 07:55:47] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.574s
[2025-07-25 07:55:58] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.610s
[2025-07-25 07:56:09] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.624s
[2025-07-25 07:56:19] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.575s
[2025-07-25 07:56:30] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.615s
[2025-07-25 07:56:40] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.573s
[2025-07-25 07:56:51] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.624s
[2025-07-25 07:57:02] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.591s
[2025-07-25 07:57:12] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.623s
[2025-07-25 07:57:23] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.569s
[2025-07-25 07:57:33] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.624s
[2025-07-25 07:57:44] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.574s
[2025-07-25 07:57:55] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.628s
[2025-07-25 07:58:05] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.589s
[2025-07-25 07:58:16] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.627s
[2025-07-25 07:58:26] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.626s
[2025-07-25 07:58:35] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.822s
[2025-07-25 07:58:46] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.607s
[2025-07-25 07:58:56] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.588s
[2025-07-25 07:59:07] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.587s
[2025-07-25 07:59:18] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.626s
[2025-07-25 07:59:28] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.573s
[2025-07-25 07:59:39] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.624s
[2025-07-25 07:59:49] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.574s
[2025-07-25 08:00:00] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.628s
[2025-07-25 08:00:11] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.588s
[2025-07-25 08:00:21] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.625s
[2025-07-25 08:00:32] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.592s
[2025-07-25 08:00:42] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.575s
[2025-07-25 08:00:53] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.627s
[2025-07-25 08:01:04] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.573s
[2025-07-25 08:01:14] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.611s
[2025-07-25 08:01:25] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.589s
[2025-07-25 08:01:35] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.626s
[2025-07-25 08:01:35] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1066.46 秒
[2025-07-25 08:01:35] ================================================================================
[2025-07-25 08:01:35] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 08:01:42] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.298s
[2025-07-25 08:01:52] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.636s
[2025-07-25 08:02:01] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.829s
[2025-07-25 08:02:12] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.577s
[2025-07-25 08:02:22] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.577s
[2025-07-25 08:02:33] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.627s
[2025-07-25 08:02:44] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.594s
[2025-07-25 08:02:54] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.626s
[2025-07-25 08:03:05] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.567s
[2025-07-25 08:03:15] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.617s
[2025-07-25 08:03:26] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.569s
[2025-07-25 08:03:37] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.610s
[2025-07-25 08:03:47] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.578s
[2025-07-25 08:03:58] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.629s
[2025-07-25 08:04:08] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.601s
[2025-07-25 08:04:19] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.632s
[2025-07-25 08:04:30] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.571s
[2025-07-25 08:04:40] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.626s
[2025-07-25 08:04:51] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.605s
[2025-07-25 08:05:01] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.601s
[2025-07-25 08:05:12] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.629s
[2025-07-25 08:05:23] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.647s
[2025-07-25 08:05:33] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.625s
[2025-07-25 08:05:44] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.604s
[2025-07-25 08:05:55] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.610s
[2025-07-25 08:06:05] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.600s
[2025-07-25 08:06:16] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.622s
[2025-07-25 08:06:26] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.602s
[2025-07-25 08:06:37] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.625s
[2025-07-25 08:06:48] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.575s
[2025-07-25 08:06:58] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.630s
[2025-07-25 08:07:09] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.598s
[2025-07-25 08:07:19] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.597s
[2025-07-25 08:07:30] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.599s
[2025-07-25 08:07:41] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.625s
[2025-07-25 08:07:51] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.570s
[2025-07-25 08:08:02] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.610s
[2025-07-25 08:08:12] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.599s
[2025-07-25 08:08:23] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.610s
[2025-07-25 08:08:34] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.570s
[2025-07-25 08:08:44] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.629s
[2025-07-25 08:08:55] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.574s
[2025-07-25 08:09:05] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.626s
[2025-07-25 08:09:16] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.605s
[2025-07-25 08:09:27] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.627s
[2025-07-25 08:09:37] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.572s
[2025-07-25 08:09:48] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.625s
[2025-07-25 08:09:58] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.567s
[2025-07-25 08:10:09] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.578s
[2025-07-25 08:10:20] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.625s
[2025-07-25 08:10:30] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.630s
[2025-07-25 08:10:41] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.603s
[2025-07-25 08:10:52] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.618s
[2025-07-25 08:11:02] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.569s
[2025-07-25 08:11:13] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.624s
[2025-07-25 08:11:23] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.602s
[2025-07-25 08:11:34] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.627s
[2025-07-25 08:11:45] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.594s
[2025-07-25 08:11:55] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.628s
[2025-07-25 08:12:06] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.571s
[2025-07-25 08:12:16] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.630s
[2025-07-25 08:12:27] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.599s
[2025-07-25 08:12:38] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.629s
[2025-07-25 08:12:48] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.572s
[2025-07-25 08:12:59] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.578s
[2025-07-25 08:13:09] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.629s
[2025-07-25 08:13:20] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.597s
[2025-07-25 08:13:31] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.603s
[2025-07-25 08:13:41] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.628s
[2025-07-25 08:13:52] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.589s
[2025-07-25 08:14:02] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.623s
[2025-07-25 08:14:13] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.606s
[2025-07-25 08:14:24] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.629s
[2025-07-25 08:14:34] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.599s
[2025-07-25 08:14:45] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.625s
[2025-07-25 08:14:55] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.578s
[2025-07-25 08:15:06] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.625s
[2025-07-25 08:15:17] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.596s
[2025-07-25 08:15:27] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.613s
[2025-07-25 08:15:38] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.576s
[2025-07-25 08:15:48] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.576s
[2025-07-25 08:15:59] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.619s
[2025-07-25 08:16:10] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.597s
[2025-07-25 08:16:20] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.617s
[2025-07-25 08:16:31] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.574s
[2025-07-25 08:16:42] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.630s
[2025-07-25 08:16:52] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.656s
[2025-07-25 08:17:03] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.628s
[2025-07-25 08:17:13] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.602s
[2025-07-25 08:17:24] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.627s
[2025-07-25 08:17:35] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.604s
[2025-07-25 08:17:45] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.612s
[2025-07-25 08:17:56] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.577s
[2025-07-25 08:18:06] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.629s
[2025-07-25 08:18:17] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.570s
[2025-07-25 08:18:28] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.628s
[2025-07-25 08:18:38] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.626s
[2025-07-25 08:18:49] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.597s
[2025-07-25 08:18:58] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.822s
[2025-07-25 08:19:08] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.576s
[2025-07-25 08:19:08] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1052.82 秒
[2025-07-25 08:19:08] 计算傅里叶变换...
[2025-07-25 08:19:10] 对角二聚体结构因子计算完成
[2025-07-25 08:19:10] 对角二聚体相关函数平均误差: 0.000122
