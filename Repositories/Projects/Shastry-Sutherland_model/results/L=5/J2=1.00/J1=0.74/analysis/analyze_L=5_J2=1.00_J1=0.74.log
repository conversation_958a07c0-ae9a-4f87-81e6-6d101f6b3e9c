[2025-07-25 04:11:40] ================================================================================
[2025-07-25 04:11:40] 加载量子态: L=5, J2=1.00, J1=0.74
[2025-07-25 04:11:40] 设置样本数为: 1048576
[2025-07-25 04:11:40] 开始生成共享样本集...
[2025-07-25 04:15:00] 样本生成完成,耗时: 199.429 秒
[2025-07-25 04:15:00] ================================================================================
[2025-07-25 04:15:00] 开始计算自旋结构因子...
[2025-07-25 04:15:00] 初始化操作符缓存...
[2025-07-25 04:15:00] 预构建所有自旋相关操作符...
[2025-07-25 04:15:00] 开始计算自旋相关函数...
[2025-07-25 04:15:09] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.550s
[2025-07-25 04:15:21] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.317s
[2025-07-25 04:15:27] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.300s
[2025-07-25 04:15:33] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.319s
[2025-07-25 04:15:40] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.299s
[2025-07-25 04:15:46] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.318s
[2025-07-25 04:15:52] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.316s
[2025-07-25 04:15:59] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.299s
[2025-07-25 04:16:05] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.321s
[2025-07-25 04:16:11] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.299s
[2025-07-25 04:16:18] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.313s
[2025-07-25 04:16:24] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.298s
[2025-07-25 04:16:30] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.316s
[2025-07-25 04:16:37] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.312s
[2025-07-25 04:16:43] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.319s
[2025-07-25 04:16:49] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.301s
[2025-07-25 04:16:55] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.321s
[2025-07-25 04:17:02] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.298s
[2025-07-25 04:17:08] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.299s
[2025-07-25 04:17:14] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.319s
[2025-07-25 04:17:21] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.299s
[2025-07-25 04:17:27] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.298s
[2025-07-25 04:17:33] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.320s
[2025-07-25 04:17:40] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.298s
[2025-07-25 04:17:46] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.315s
[2025-07-25 04:17:52] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.316s
[2025-07-25 04:17:59] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.299s
[2025-07-25 04:18:05] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.333s
[2025-07-25 04:18:11] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.299s
[2025-07-25 04:18:17] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.299s
[2025-07-25 04:18:24] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.315s
[2025-07-25 04:18:30] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.316s
[2025-07-25 04:18:36] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.298s
[2025-07-25 04:18:43] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.298s
[2025-07-25 04:18:49] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.313s
[2025-07-25 04:18:55] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.298s
[2025-07-25 04:19:02] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.321s
[2025-07-25 04:19:08] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.318s
[2025-07-25 04:19:14] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.299s
[2025-07-25 04:19:21] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.314s
[2025-07-25 04:19:27] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.297s
[2025-07-25 04:19:33] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.299s
[2025-07-25 04:19:39] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.311s
[2025-07-25 04:19:46] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.299s
[2025-07-25 04:19:52] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.358s
[2025-07-25 04:19:58] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.320s
[2025-07-25 04:20:05] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.298s
[2025-07-25 04:20:11] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.313s
[2025-07-25 04:20:17] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.297s
[2025-07-25 04:20:24] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.298s
[2025-07-25 04:20:30] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.316s
[2025-07-25 04:20:36] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.298s
[2025-07-25 04:20:43] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.299s
[2025-07-25 04:20:49] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.299s
[2025-07-25 04:20:55] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.315s
[2025-07-25 04:21:02] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.297s
[2025-07-25 04:21:08] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.312s
[2025-07-25 04:21:14] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.321s
[2025-07-25 04:21:20] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.298s
[2025-07-25 04:21:27] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.318s
[2025-07-25 04:21:33] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.298s
[2025-07-25 04:21:39] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.298s
[2025-07-25 04:21:46] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.319s
[2025-07-25 04:21:52] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.298s
[2025-07-25 04:21:58] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.314s
[2025-07-25 04:22:05] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.322s
[2025-07-25 04:22:11] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.298s
[2025-07-25 04:22:17] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.316s
[2025-07-25 04:22:24] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.298s
[2025-07-25 04:22:30] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.298s
[2025-07-25 04:22:36] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.319s
[2025-07-25 04:22:42] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.298s
[2025-07-25 04:22:49] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.315s
[2025-07-25 04:22:55] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.315s
[2025-07-25 04:23:01] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.298s
[2025-07-25 04:23:08] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.319s
[2025-07-25 04:23:14] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.298s
[2025-07-25 04:23:20] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.299s
[2025-07-25 04:23:27] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.319s
[2025-07-25 04:23:33] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.298s
[2025-07-25 04:23:39] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.316s
[2025-07-25 04:23:46] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.314s
[2025-07-25 04:23:52] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.297s
[2025-07-25 04:23:58] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.315s
[2025-07-25 04:24:04] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.315s
[2025-07-25 04:24:11] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.316s
[2025-07-25 04:24:17] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.298s
[2025-07-25 04:24:23] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.317s
[2025-07-25 04:24:30] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.298s
[2025-07-25 04:24:36] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.297s
[2025-07-25 04:24:42] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.315s
[2025-07-25 04:24:49] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.298s
[2025-07-25 04:24:55] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.319s
[2025-07-25 04:25:01] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.316s
[2025-07-25 04:25:08] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.299s
[2025-07-25 04:25:14] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.315s
[2025-07-25 04:25:20] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.298s
[2025-07-25 04:25:26] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.297s
[2025-07-25 04:25:33] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.312s
[2025-07-25 04:25:39] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.298s
[2025-07-25 04:25:39] 自旋相关函数计算完成,总耗时 639.15 秒
[2025-07-25 04:25:39] 计算傅里叶变换...
[2025-07-25 04:25:40] 自旋结构因子计算完成
[2025-07-25 04:25:41] 自旋相关函数平均误差: 0.000658
[2025-07-25 04:25:41] ================================================================================
[2025-07-25 04:25:41] 开始计算对角二聚体结构因子...
[2025-07-25 04:25:41] 识别所有对角二聚体...
[2025-07-25 04:25:41] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 04:25:41] 预计算对角二聚体操作符...
[2025-07-25 04:25:43] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 04:25:49] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.345s
[2025-07-25 04:26:05] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.371s
[2025-07-25 04:26:15] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.648s
[2025-07-25 04:26:26] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.598s
[2025-07-25 04:26:37] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.628s
[2025-07-25 04:26:47] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.597s
[2025-07-25 04:26:58] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.629s
[2025-07-25 04:27:08] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.597s
[2025-07-25 04:27:19] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.637s
[2025-07-25 04:27:30] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.597s
[2025-07-25 04:27:40] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.629s
[2025-07-25 04:27:51] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.652s
[2025-07-25 04:28:02] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.593s
[2025-07-25 04:28:12] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.637s
[2025-07-25 04:28:23] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.597s
[2025-07-25 04:28:33] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.643s
[2025-07-25 04:28:44] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.596s
[2025-07-25 04:28:55] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.628s
[2025-07-25 04:29:12] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.572s
[2025-07-25 04:29:23] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.593s
[2025-07-25 04:29:33] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.625s
[2025-07-25 04:29:44] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.590s
[2025-07-25 04:29:55] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.635s
[2025-07-25 04:30:05] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.590s
[2025-07-25 04:30:16] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.620s
[2025-07-25 04:30:26] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.615s
[2025-07-25 04:30:37] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.591s
[2025-07-25 04:30:48] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.625s
[2025-07-25 04:30:58] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.587s
[2025-07-25 04:31:09] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.618s
[2025-07-25 04:31:19] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.589s
[2025-07-25 04:31:30] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.630s
[2025-07-25 04:31:41] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.586s
[2025-07-25 04:31:51] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.585s
[2025-07-25 04:32:02] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.622s
[2025-07-25 04:32:12] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.585s
[2025-07-25 04:32:23] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.631s
[2025-07-25 04:32:34] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.589s
[2025-07-25 04:32:44] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.617s
[2025-07-25 04:32:55] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.589s
[2025-07-25 04:33:06] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.623s
[2025-07-25 04:33:16] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.589s
[2025-07-25 04:33:27] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.588s
[2025-07-25 04:33:37] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.627s
[2025-07-25 04:33:48] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.585s
[2025-07-25 04:33:59] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.624s
[2025-07-25 04:34:09] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.589s
[2025-07-25 04:34:20] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.622s
[2025-07-25 04:34:30] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.589s
[2025-07-25 04:34:41] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.589s
[2025-07-25 04:34:52] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.626s
[2025-07-25 04:35:02] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.588s
[2025-07-25 04:35:13] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.634s
[2025-07-25 04:35:23] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.589s
[2025-07-25 04:35:34] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.626s
[2025-07-25 04:35:45] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.587s
[2025-07-25 04:35:55] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.629s
[2025-07-25 04:36:06] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.587s
[2025-07-25 04:36:16] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.628s
[2025-07-25 04:36:27] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.589s
[2025-07-25 04:36:38] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.628s
[2025-07-25 04:36:48] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.586s
[2025-07-25 04:36:59] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.627s
[2025-07-25 04:37:09] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.588s
[2025-07-25 04:37:20] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.625s
[2025-07-25 04:37:31] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.619s
[2025-07-25 04:37:41] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.589s
[2025-07-25 04:37:52] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.617s
[2025-07-25 04:38:03] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.626s
[2025-07-25 04:38:13] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.589s
[2025-07-25 04:38:24] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.621s
[2025-07-25 04:38:34] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.589s
[2025-07-25 04:38:45] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.623s
[2025-07-25 04:38:56] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.587s
[2025-07-25 04:39:06] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.624s
[2025-07-25 04:39:17] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.590s
[2025-07-25 04:39:27] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.623s
[2025-07-25 04:39:38] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.585s
[2025-07-25 04:39:49] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.632s
[2025-07-25 04:39:59] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.587s
[2025-07-25 04:40:10] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.626s
[2025-07-25 04:40:20] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.631s
[2025-07-25 04:40:29] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.801s
[2025-07-25 04:40:40] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.614s
[2025-07-25 04:40:50] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.589s
[2025-07-25 04:41:01] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.584s
[2025-07-25 04:41:12] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.629s
[2025-07-25 04:41:22] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.589s
[2025-07-25 04:41:33] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.623s
[2025-07-25 04:41:44] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.588s
[2025-07-25 04:41:54] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.709s
[2025-07-25 04:42:05] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.590s
[2025-07-25 04:42:15] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.624s
[2025-07-25 04:42:26] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.589s
[2025-07-25 04:42:37] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.589s
[2025-07-25 04:42:47] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.626s
[2025-07-25 04:42:58] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.590s
[2025-07-25 04:43:08] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.618s
[2025-07-25 04:43:19] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.588s
[2025-07-25 04:43:30] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.628s
[2025-07-25 04:43:30] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1066.70 秒
[2025-07-25 04:43:30] ================================================================================
[2025-07-25 04:43:30] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 04:43:36] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.303s
[2025-07-25 04:43:47] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.634s
[2025-07-25 04:43:55] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.831s
[2025-07-25 04:44:06] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.594s
[2025-07-25 04:44:17] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.593s
[2025-07-25 04:44:27] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.630s
[2025-07-25 04:44:38] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.593s
[2025-07-25 04:44:48] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.629s
[2025-07-25 04:44:59] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.593s
[2025-07-25 04:45:10] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.625s
[2025-07-25 04:45:20] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.594s
[2025-07-25 04:45:31] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.620s
[2025-07-25 04:45:42] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.594s
[2025-07-25 04:45:52] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.642s
[2025-07-25 04:46:03] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.593s
[2025-07-25 04:46:13] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.635s
[2025-07-25 04:46:24] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.593s
[2025-07-25 04:46:35] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.628s
[2025-07-25 04:46:45] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.594s
[2025-07-25 04:46:56] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.596s
[2025-07-25 04:47:06] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.635s
[2025-07-25 04:47:17] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.595s
[2025-07-25 04:47:28] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.629s
[2025-07-25 04:47:38] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.594s
[2025-07-25 04:47:49] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.619s
[2025-07-25 04:47:59] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.593s
[2025-07-25 04:48:10] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.628s
[2025-07-25 04:48:21] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.594s
[2025-07-25 04:48:31] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.629s
[2025-07-25 04:48:42] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.594s
[2025-07-25 04:48:53] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.636s
[2025-07-25 04:49:03] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.595s
[2025-07-25 04:49:14] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.593s
[2025-07-25 04:49:24] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.595s
[2025-07-25 04:49:35] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.629s
[2025-07-25 04:49:46] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.593s
[2025-07-25 04:49:56] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.620s
[2025-07-25 04:50:07] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.593s
[2025-07-25 04:50:17] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.620s
[2025-07-25 04:50:28] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.594s
[2025-07-25 04:50:39] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.634s
[2025-07-25 04:50:49] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.594s
[2025-07-25 04:51:00] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.629s
[2025-07-25 04:51:10] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.593s
[2025-07-25 04:51:21] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.628s
[2025-07-25 04:51:32] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.593s
[2025-07-25 04:51:42] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.629s
[2025-07-25 04:51:53] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.593s
[2025-07-25 04:52:03] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.594s
[2025-07-25 04:52:14] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.629s
[2025-07-25 04:52:25] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.635s
[2025-07-25 04:52:35] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.594s
[2025-07-25 04:52:46] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.627s
[2025-07-25 04:52:57] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.593s
[2025-07-25 04:53:07] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.627s
[2025-07-25 04:53:18] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.593s
[2025-07-25 04:53:28] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.631s
[2025-07-25 04:53:39] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.594s
[2025-07-25 04:53:50] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.632s
[2025-07-25 04:54:00] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.593s
[2025-07-25 04:54:11] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.635s
[2025-07-25 04:54:21] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.595s
[2025-07-25 04:54:32] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.631s
[2025-07-25 04:54:43] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.593s
[2025-07-25 04:54:53] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.593s
[2025-07-25 04:55:04] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.632s
[2025-07-25 04:55:15] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.593s
[2025-07-25 04:55:25] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.593s
[2025-07-25 04:55:36] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.634s
[2025-07-25 04:55:46] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.594s
[2025-07-25 04:55:57] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.627s
[2025-07-25 04:56:08] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.593s
[2025-07-25 04:56:18] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.634s
[2025-07-25 04:56:29] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.594s
[2025-07-25 04:56:39] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.629s
[2025-07-25 04:56:50] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.595s
[2025-07-25 04:57:01] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.628s
[2025-07-25 04:57:11] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.594s
[2025-07-25 04:57:22] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.620s
[2025-07-25 04:57:32] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.594s
[2025-07-25 04:57:43] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.593s
[2025-07-25 04:57:54] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.625s
[2025-07-25 04:58:04] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.593s
[2025-07-25 04:58:15] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.624s
[2025-07-25 04:58:26] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.593s
[2025-07-25 04:58:36] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.630s
[2025-07-25 04:58:47] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.593s
[2025-07-25 04:58:57] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.629s
[2025-07-25 04:59:08] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.593s
[2025-07-25 04:59:19] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.628s
[2025-07-25 04:59:29] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.593s
[2025-07-25 04:59:40] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.619s
[2025-07-25 04:59:50] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.593s
[2025-07-25 05:00:01] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.631s
[2025-07-25 05:00:12] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.593s
[2025-07-25 05:00:22] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.627s
[2025-07-25 05:00:33] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.628s
[2025-07-25 05:00:43] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.593s
[2025-07-25 05:00:52] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.827s
[2025-07-25 05:01:03] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.593s
[2025-07-25 05:01:03] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1053.24 秒
[2025-07-25 05:01:03] 计算傅里叶变换...
[2025-07-25 05:01:04] 对角二聚体结构因子计算完成
[2025-07-25 05:01:04] 对角二聚体相关函数平均误差: 0.000140
