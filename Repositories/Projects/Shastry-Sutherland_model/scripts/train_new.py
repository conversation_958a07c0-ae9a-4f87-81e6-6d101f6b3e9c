#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shastry-Sutherland模型新训练脚本
展示改进后的训练系统功能，包括退火机制和checkpoint支持
"""

import os
import sys
import argparse

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import jax
import netket as nk
from src.runner import SSRunner

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description='Shastry-Sutherland模型训练')
    
    # 系统参数
    parser.add_argument('--L', type=int, default=4, help='晶格尺寸')
    parser.add_argument('--J1', type=float, default=1.0, help='J1耦合强度')
    parser.add_argument('--J2', type=float, default=0.5, help='J2耦合强度')
    
    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=0.015, help='学习率')
    parser.add_argument('--n_iters', type=int, default=1000, help='总迭代次数')
    parser.add_argument('--n_samples', type=int, default=4096, help='样本数量')
    parser.add_argument('--chunk_size', type=int, default=1024, help='批处理大小')
    
    # 退火参数
    parser.add_argument('--n_cycles', type=int, default=1, help='退火周期数')
    parser.add_argument('--initial_period', type=int, default=100, help='初始周期长度')
    parser.add_argument('--period_mult', type=float, default=2.0, help='周期倍数')
    parser.add_argument('--max_temperature', type=float, default=1.0, help='最大温度')
    parser.add_argument('--min_temperature', type=float, default=0.01, help='最小温度')
    
    # 模型参数
    parser.add_argument('--num_features', type=int, default=4, help='特征维度')
    parser.add_argument('--num_layers', type=int, default=4, help='编码器层数')
    
    # Checkpoint参数
    parser.add_argument('--enable_checkpoint', action='store_true', help='启用checkpoint')
    parser.add_argument('--save_interval', type=int, default=500, help='checkpoint保存间隔')
    parser.add_argument('--resume_from', type=str, default=None, help='从checkpoint恢复')
    
    # 其他参数
    parser.add_argument('--diag_shift', type=float, default=0.20, help='对角线位移')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪')
    parser.add_argument('--reference_energy', type=float, default=None, help='参考能量')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    print("="*60)
    print("Shastry-Sutherland模型训练 - 改进版本")
    print("="*60)
    print(f"系统参数: L={args.L}, J1={args.J1}, J2={args.J2}")
    print(f"训练参数: 学习率={args.learning_rate}, 迭代次数={args.n_iters}")
    print(f"退火参数: 周期数={args.n_cycles}, 初始周期={args.initial_period}")
    print(f"温度范围: {args.min_temperature} - {args.max_temperature}")
    print(f"Checkpoint: {'启用' if args.enable_checkpoint else '禁用'}")
    print(f"可用设备: {jax.devices()}")
    print(f"NetKet分片: {nk.config.netket_experimental_sharding}")
    print("="*60)
    
    # 创建配置
    model_config = {
        'num_features': args.num_features,
        'num_layers': args.num_layers
    }
    
    training_config = {
        'learning_rate': args.learning_rate,
        'n_cycles': args.n_cycles,
        'initial_period': args.initial_period,
        'period_mult': args.period_mult,
        'max_temperature': args.max_temperature,
        'min_temperature': args.min_temperature,
        'n_samples': args.n_samples,
        'n_discard_per_chain': 0,
        'chunk_size': args.chunk_size,
        'diag_shift': args.diag_shift,
        'grad_clip': args.grad_clip,
        'n_iters': args.n_iters
    }
    
    checkpoint_config = {
        'enable': args.enable_checkpoint,
        'save_interval': args.save_interval,
        'resume_from': args.resume_from,
        'keep_history': True
    }
    
    # 创建运行器实例
    runner = SSRunner(
        L=args.L,
        J1=args.J1,
        J2=args.J2,
        model_class="GCNN",
        model_config=model_config,
        training_config=training_config,
        reference_energy=args.reference_energy,
        checkpoint_config=checkpoint_config
    )
    
    # 设置模型并运行
    runner.setup_model()
    runner.run()
    
    print("="*60)
    print("训练完成！")
    print("="*60)

if __name__ == "__main__":
    main()
