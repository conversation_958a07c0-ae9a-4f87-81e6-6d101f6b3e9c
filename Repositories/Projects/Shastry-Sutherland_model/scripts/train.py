#!/usr/bin/env python3
"""
训练脚本，用于训练Shastry-Sutherland模型的GCNN量子态。
"""

# 设置环境变量
import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"  # 启用NetKet的分片功能
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"  # 使用平台特定的内存分配器
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"  # 禁用预分配
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import jax
import netket as nk

class TrainingConfig:
    # 训练参数
    seed = 0               # 随机种子
    diag_shift = 0.20      # 对角线位移
    learning_rate = 0.015  # 学习率
    n_iters = 1000         # 退火迭代次数
    n_samples = 2**12      # 样本数量
    n_discard_per_chain = 0          # 丢弃的样本数
    chunk_size = 2**10     # 批处理大小
    # 热重启退火参数
    n_cycles = 1           # 退火周期数
    initial_period = 100   # 初始周期长度
    period_mult = 2.0      # 周期倍数
    max_temperature = 1.0  # 最大温度
    min_temperature = 0.01 # 最小温度
    grad_clip = 1.0        # 梯度裁剪

class ModelConfig:
    # 模型参数
    num_features = 4      # 特征维度
    num_layers = 4        # 编码器层数

class CheckpointConfig:
    # Checkpoint配置
    enable = False        # 是否启用checkpoint
    save_interval = 500   # 保存间隔
    resume_from = None    # 恢复路径
    keep_history = True   # 是否保留历史

# 导入自定义模块
from src.runner import SSRunner


def run_gcnn_simulation(L, J2, J1):
    """
    利用Group-equivariant Convolutional Neural Network (GCNN)进行变分量子态优化模拟，
    使用新的SSRunner框架。
    """
    # 创建配置对象
    model_config = {
        'num_features': ModelConfig.num_features,
        'num_layers': ModelConfig.num_layers
    }

    training_config = {
        'learning_rate': TrainingConfig.learning_rate,
        'n_cycles': TrainingConfig.n_cycles,
        'initial_period': TrainingConfig.initial_period,
        'period_mult': TrainingConfig.period_mult,
        'max_temperature': TrainingConfig.max_temperature,
        'min_temperature': TrainingConfig.min_temperature,
        'n_samples': TrainingConfig.n_samples,
        'n_discard_per_chain': TrainingConfig.n_discard_per_chain,
        'chunk_size': TrainingConfig.chunk_size,
        'diag_shift': TrainingConfig.diag_shift,
        'grad_clip': TrainingConfig.grad_clip,
        'n_iters': TrainingConfig.n_iters
    }

    checkpoint_config = {
        'enable': CheckpointConfig.enable,
        'save_interval': CheckpointConfig.save_interval,
        'resume_from': CheckpointConfig.resume_from,
        'keep_history': CheckpointConfig.keep_history
    }

    # 创建运行器实例
    runner = SSRunner(
        L=L,
        J1=J1,
        J2=J2,
        model_class="GCNN",
        model_config=model_config,
        training_config=training_config,
        reference_energy=None,
        checkpoint_config=checkpoint_config
    )

    # 设置模型并运行
    runner.setup_model()
    runner.run()

    return runner


def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("使用方法: python train.py <L> <J2> <J1>")
        sys.exit(1)

    L = int(sys.argv[1])
    J2 = float(sys.argv[2])
    J1 = float(sys.argv[3])

    print("\n开始 GCNN 模拟")
    print(f"Sharding 启用状态: {nk.config.netket_experimental_sharding}")
    print(f"可用设备: {jax.devices()}")
    print(f"GPU 信息:")
    os.system("nvidia-smi")
    print("\n")

    try:
        run_gcnn_simulation(L, J2, J1)
    except Exception as e:
        print(f"L={L}, J2={J2}, J1={J1} 的模拟失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
