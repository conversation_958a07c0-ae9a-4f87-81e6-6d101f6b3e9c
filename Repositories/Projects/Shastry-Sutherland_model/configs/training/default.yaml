# 实验名称，会作为输出目录的一部分
experiment_name: "pretrain"

# Checkpoint 配置
checkpoint:
  # 是否启用checkpoint功能
  enable: false
  # checkpoint保存间隔（迭代次数）
  save_interval: 500
  # 要从中恢复训练的checkpoint路径（相对于工作目录或绝对路径）
  # 支持以下格式：
  # 1. 具体的checkpoint文件路径：results/.../checkpoints/checkpoint_iter_001000.pkl
  # 2. checkpoint目录路径：results/.../checkpoints （自动选择最新的checkpoint）
  # 3. null 或留空：从头开始训练
  resume_from: null
  # 是否保留历史checkpoint（如果为false，只保留最新的checkpoint）
  keep_history: true

# 训练参数
learning_rate: 0.015     # 学习率
n_iters: 1000           # 总迭代次数

# 热重启退火参数
n_cycles: 1             # 退火周期数
initial_period: 100     # 初始周期长度
period_mult: 2.0        # 周期倍数
max_temperature: 1.0    # 最大温度
min_temperature: 0.01   # 最小温度

# 采样参数
n_samples: 4096         # 样本数量 (2**12)
n_discard_per_chain: 0  # 丢弃的样本数
chunk_size: 1024        # 批处理大小 (2**10)

# 优化参数
diag_shift: 0.20        # 对角线位移
grad_clip: 1.0          # 梯度裁剪
